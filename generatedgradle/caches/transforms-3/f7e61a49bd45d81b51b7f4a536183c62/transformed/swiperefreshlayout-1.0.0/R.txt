int attr alpha 0x7f040001
int attr font 0x7f040002
int attr fontProviderAuthority 0x7f040003
int attr fontProviderCerts 0x7f040004
int attr fontProviderFetchStrategy 0x7f040005
int attr fontProviderFetchTimeout 0x7f040006
int attr fontProviderPackage 0x7f040007
int attr fontProviderQuery 0x7f040008
int attr fontStyle 0x7f040009
int attr fontVariationSettings 0x7f04000a
int attr fontWeight 0x7f04000b
int attr ttcIndex 0x7f04000c
int color notification_action_color_filter 0x7f060001
int color notification_icon_bg_color 0x7f060002
int color ripple_material_light 0x7f060003
int color secondary_text_default_material_light 0x7f060004
int dimen compat_button_inset_horizontal_material 0x7f080001
int dimen compat_button_inset_vertical_material 0x7f080002
int dimen compat_button_padding_horizontal_material 0x7f080003
int dimen compat_button_padding_vertical_material 0x7f080004
int dimen compat_control_corner_material 0x7f080005
int dimen compat_notification_large_icon_max_height 0x7f080006
int dimen compat_notification_large_icon_max_width 0x7f080007
int dimen notification_action_icon_size 0x7f080008
int dimen notification_action_text_size 0x7f080009
int dimen notification_big_circle_margin 0x7f08000a
int dimen notification_content_margin_start 0x7f08000b
int dimen notification_large_icon_height 0x7f08000c
int dimen notification_large_icon_width 0x7f08000d
int dimen notification_main_column_padding_top 0x7f08000e
int dimen notification_media_narrow_margin 0x7f08000f
int dimen notification_right_icon_size 0x7f080010
int dimen notification_right_side_padding_top 0x7f080011
int dimen notification_small_icon_background_padding 0x7f080012
int dimen notification_small_icon_size_as_large 0x7f080013
int dimen notification_subtext_size 0x7f080014
int dimen notification_top_pad 0x7f080015
int dimen notification_top_pad_large_text 0x7f080016
int drawable notification_action_background 0x7f090001
int drawable notification_bg 0x7f090002
int drawable notification_bg_low 0x7f090003
int drawable notification_bg_low_normal 0x7f090004
int drawable notification_bg_low_pressed 0x7f090005
int drawable notification_bg_normal 0x7f090006
int drawable notification_bg_normal_pressed 0x7f090007
int drawable notification_icon_background 0x7f090008
int drawable notification_template_icon_bg 0x7f090009
int drawable notification_template_icon_low_bg 0x7f09000a
int drawable notification_tile_bg 0x7f09000b
int drawable notify_panel_notification_icon_bg 0x7f09000c
int id action_container 0x7f0c0001
int id action_divider 0x7f0c0002
int id action_image 0x7f0c0003
int id action_text 0x7f0c0004
int id actions 0x7f0c0005
int id async 0x7f0c0006
int id blocking 0x7f0c0007
int id chronometer 0x7f0c0008
int id forever 0x7f0c0009
int id icon 0x7f0c000a
int id icon_group 0x7f0c000b
int id info 0x7f0c000c
int id italic 0x7f0c000d
int id line1 0x7f0c000e
int id line3 0x7f0c000f
int id normal 0x7f0c0010
int id notification_background 0x7f0c0011
int id notification_main_column 0x7f0c0012
int id notification_main_column_container 0x7f0c0013
int id right_icon 0x7f0c0014
int id right_side 0x7f0c0015
int id tag_transition_group 0x7f0c0016
int id tag_unhandled_key_event_manager 0x7f0c0017
int id tag_unhandled_key_listeners 0x7f0c0018
int id text 0x7f0c0019
int id text2 0x7f0c001a
int id time 0x7f0c001b
int id title 0x7f0c001c
int integer status_bar_notification_info_maxnum 0x7f0d0001
int layout notification_action 0x7f0f0001
int layout notification_action_tombstone 0x7f0f0002
int layout notification_template_custom_big 0x7f0f0003
int layout notification_template_icon_group 0x7f0f0004
int layout notification_template_part_chronometer 0x7f0f0005
int layout notification_template_part_time 0x7f0f0006
int string status_bar_notification_info_overflow 0x7f150001
int style TextAppearance_Compat_Notification 0x7f160001
int style TextAppearance_Compat_Notification_Info 0x7f160002
int style TextAppearance_Compat_Notification_Line2 0x7f160003
int style TextAppearance_Compat_Notification_Time 0x7f160004
int style TextAppearance_Compat_Notification_Title 0x7f160005
int style Widget_Compat_NotificationActionContainer 0x7f160006
int style Widget_Compat_NotificationActionText 0x7f160007
int[] styleable ColorStateListItem { 0x7f040001, 0x101031f, 0x10101a5 }
int styleable ColorStateListItem_alpha 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_color 2
int[] styleable FontFamily { 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x7f040002, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontStyle 1
int styleable FontFamilyFont_android_fontVariationSettings 2
int styleable FontFamilyFont_android_fontWeight 3
int styleable FontFamilyFont_android_ttcIndex 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
int styleable GradientColor_android_centerColor 0
int styleable GradientColor_android_centerX 1
int styleable GradientColor_android_centerY 2
int styleable GradientColor_android_endColor 3
int styleable GradientColor_android_endX 4
int styleable GradientColor_android_endY 5
int styleable GradientColor_android_gradientRadius 6
int styleable GradientColor_android_startColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_tileMode 10
int styleable GradientColor_android_type 11
int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
