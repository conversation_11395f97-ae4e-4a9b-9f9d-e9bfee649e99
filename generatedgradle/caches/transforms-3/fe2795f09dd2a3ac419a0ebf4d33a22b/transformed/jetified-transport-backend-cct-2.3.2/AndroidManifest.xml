<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2018 Google LLC -->
<!--  -->
<!-- Licensed under the Apache License, Version 2.0 (the "License"); -->
<!-- you may not use this file except in compliance with the License. -->
<!-- You may obtain a copy of the License at -->
<!--  -->
<!-- http://www.apache.org/licenses/LICENSE-2.0 -->
<!--  -->
<!-- Unless required by applicable law or agreed to in writing, software -->
<!-- distributed under the License is distributed on an "AS IS" BASIS, -->
<!-- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. -->
<!-- See the License for the specific language governing permissions and -->
<!-- limitations under the License. -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.google.android.datatransport.backend.cct"
    android:versionName="2.3.2" >

    <uses-sdk
        android:minSdkVersion="14"
        android:targetSdkVersion="29" />
    <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
    <!-- <uses-sdk android:minSdkVersion="14"/> -->

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />

    <application>
        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
    </application>

</manifest>