<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.google.firebase.installations"
    android:versionName="16.3.4" >

    <uses-sdk
        android:minSdkVersion="14"
        android:targetSdkVersion="29" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
    <!-- <uses-sdk android:minSdkVersion="14"/> -->
    <application>
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>
    </application>

</manifest>