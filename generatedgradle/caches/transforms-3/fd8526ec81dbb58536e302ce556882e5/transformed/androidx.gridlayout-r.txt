androidx.gridlayout
attr alignmentMode
attr alpha
attr columnCount
attr columnOrderPreserved
attr coordinatorLayoutStyle
attr font
attr fontProviderAuthority
attr fontProviderCerts
attr fontProviderFetchStrategy
attr fontProviderFetchTimeout
attr fontProviderPackage
attr fontProviderQuery
attr fontStyle
attr fontVariationSettings
attr fontWeight
attr keylines
attr layout_anchor
attr layout_anchorGravity
attr layout_behavior
attr layout_column
attr layout_columnSpan
attr layout_columnWeight
attr layout_dodgeInsetEdges
attr layout_gravity
attr layout_insetEdge
attr layout_keyline
attr layout_row
attr layout_rowSpan
attr layout_rowWeight
attr orientation
attr rowCount
attr rowOrderPreserved
attr statusBarBackground
attr ttcIndex
attr useDefaultMargins
color notification_action_color_filter
color notification_icon_bg_color
color ripple_material_light
color secondary_text_default_material_light
dimen compat_button_inset_horizontal_material
dimen compat_button_inset_vertical_material
dimen compat_button_padding_horizontal_material
dimen compat_button_padding_vertical_material
dimen compat_control_corner_material
dimen compat_notification_large_icon_max_height
dimen compat_notification_large_icon_max_width
dimen default_gap
dimen notification_action_icon_size
dimen notification_action_text_size
dimen notification_big_circle_margin
dimen notification_content_margin_start
dimen notification_large_icon_height
dimen notification_large_icon_width
dimen notification_main_column_padding_top
dimen notification_media_narrow_margin
dimen notification_right_icon_size
dimen notification_right_side_padding_top
dimen notification_small_icon_background_padding
dimen notification_small_icon_size_as_large
dimen notification_subtext_size
dimen notification_top_pad
dimen notification_top_pad_large_text
drawable notification_action_background
drawable notification_bg
drawable notification_bg_low
drawable notification_bg_low_normal
drawable notification_bg_low_pressed
drawable notification_bg_normal
drawable notification_bg_normal_pressed
drawable notification_icon_background
drawable notification_template_icon_bg
drawable notification_template_icon_low_bg
drawable notification_tile_bg
drawable notify_panel_notification_icon_bg
id action_container
id action_divider
id action_image
id action_text
id actions
id alignBounds
id alignMargins
id async
id blocking
id bottom
id chronometer
id end
id forever
id horizontal
id icon
id icon_group
id info
id italic
id left
id line1
id line3
id none
id normal
id notification_background
id notification_main_column
id notification_main_column_container
id right
id right_icon
id right_side
id start
id tag_transition_group
id tag_unhandled_key_event_manager
id tag_unhandled_key_listeners
id text
id text2
id time
id title
id top
id vertical
integer status_bar_notification_info_maxnum
layout notification_action
layout notification_action_tombstone
layout notification_template_custom_big
layout notification_template_icon_group
layout notification_template_part_chronometer
layout notification_template_part_time
string status_bar_notification_info_overflow
style TextAppearance_Compat_Notification
style TextAppearance_Compat_Notification_Info
style TextAppearance_Compat_Notification_Line2
style TextAppearance_Compat_Notification_Time
style TextAppearance_Compat_Notification_Title
style Widget_Compat_NotificationActionContainer
style Widget_Compat_NotificationActionText
style Widget_Support_CoordinatorLayout
styleable ColorStateListItem alpha android_alpha android_color
styleable CoordinatorLayout keylines statusBarBackground
styleable CoordinatorLayout_Layout android_layout_gravity layout_anchor layout_anchorGravity layout_behavior layout_dodgeInsetEdges layout_insetEdge layout_keyline
styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery
styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
styleable GradientColorItem android_color android_offset
styleable GridLayout alignmentMode columnCount columnOrderPreserved orientation rowCount rowOrderPreserved useDefaultMargins
styleable GridLayout_Layout android_layout_height android_layout_margin android_layout_marginBottom android_layout_marginLeft android_layout_marginRight android_layout_marginTop android_layout_width layout_column layout_columnSpan layout_columnWeight layout_gravity layout_row layout_rowSpan layout_rowWeight
