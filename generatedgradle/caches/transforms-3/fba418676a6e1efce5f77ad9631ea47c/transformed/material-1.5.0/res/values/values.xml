<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools" xmlns:ns2="urn:oasis:names:tc:xliff:document:1.2">
    <attr format="reference" name="appBarLayoutStyle"/>
    <attr format="reference" name="badgeStyle"/>
    <attr format="reference" name="bottomAppBarStyle"/>
    <attr format="reference" name="bottomNavigationStyle"/>
    <attr format="reference" name="bottomSheetDialogTheme"/>
    <attr format="reference" name="bottomSheetStyle"/>
    <attr format="reference" name="checkedIcon"/>
    <attr format="color" name="checkedIconTint"/>
    <attr format="reference" name="chipGroupStyle"/>
    <attr format="reference" name="chipStandaloneStyle"/>
    <attr format="reference" name="chipStyle"/>
    <attr format="reference" name="circularProgressIndicatorStyle"/>
    <attr format="reference" name="clockIcon"/>
    <attr format="reference" name="collapsingToolbarLayoutLargeSize"/>
    <attr format="reference" name="collapsingToolbarLayoutLargeStyle"/>
    <attr format="reference" name="collapsingToolbarLayoutMediumSize"/>
    <attr format="reference" name="collapsingToolbarLayoutMediumStyle"/>
    <attr format="reference" name="collapsingToolbarLayoutStyle"/>
    <attr format="color" name="colorContainer"/>
    <attr format="color" name="colorErrorContainer"/>
    <attr name="colorOnBackground"/>
    <attr format="color" name="colorOnContainer"/>
    <attr format="color" name="colorOnError"/>
    <attr format="color" name="colorOnErrorContainer"/>
    <attr format="color" name="colorOnPrimary"/>
    <attr format="color" name="colorOnPrimaryContainer"/>
    <attr format="color" name="colorOnPrimarySurface"/>
    <attr format="color" name="colorOnSecondary"/>
    <attr format="color" name="colorOnSecondaryContainer"/>
    <attr format="color" name="colorOnSurface"/>
    <attr format="color" name="colorOnSurfaceInverse"/>
    <attr format="color" name="colorOnSurfaceVariant"/>
    <attr format="color" name="colorOnTertiary"/>
    <attr format="color" name="colorOnTertiaryContainer"/>
    <attr format="color" name="colorOutline"/>
    <attr format="color" name="colorPrimaryContainer"/>
    <attr format="color" name="colorPrimaryInverse"/>
    <attr format="color" name="colorPrimarySurface"/>
    <attr format="color" name="colorPrimaryVariant"/>
    <attr format="color" name="colorSecondary"/>
    <attr format="color" name="colorSecondaryContainer"/>
    <attr format="color" name="colorSecondaryVariant"/>
    <attr format="color" name="colorSurface"/>
    <attr format="color" name="colorSurfaceInverse"/>
    <attr format="color" name="colorSurfaceVariant"/>
    <attr format="color" name="colorTertiary"/>
    <attr format="color" name="colorTertiaryContainer"/>
    <attr format="dimension" name="dividerInsetEnd"/>
    <attr format="dimension" name="dividerInsetStart"/>
    <attr format="reference" name="dynamicColorThemeOverlay"/>
    <attr format="color" name="elevationOverlayAccentColor"/>
    <attr format="color" name="elevationOverlayColor"/>
    <attr format="boolean" name="elevationOverlayEnabled"/>
    <attr format="boolean" name="enableEdgeToEdge"/>
    <attr format="boolean" name="ensureMinTouchTargetSize"/>
    <attr format="reference" name="extendedFloatingActionButtonPrimaryStyle"/>
    <attr format="reference" name="extendedFloatingActionButtonSecondaryStyle"/>
    <attr format="reference" name="extendedFloatingActionButtonStyle"/>
    <attr format="reference" name="extendedFloatingActionButtonSurfaceStyle"/>
    <attr format="reference" name="extendedFloatingActionButtonTertiaryStyle"/>
    <attr format="reference" name="floatingActionButtonLargePrimaryStyle"/>
    <attr format="reference" name="floatingActionButtonLargeSecondaryStyle"/>
    <attr format="reference" name="floatingActionButtonLargeStyle"/>
    <attr format="reference" name="floatingActionButtonLargeSurfaceStyle"/>
    <attr format="reference" name="floatingActionButtonLargeTertiaryStyle"/>
    <attr format="reference" name="floatingActionButtonPrimaryStyle"/>
    <attr format="reference" name="floatingActionButtonSecondaryStyle"/>
    <attr format="reference" name="floatingActionButtonStyle"/>
    <attr format="reference" name="floatingActionButtonSurfaceStyle"/>
    <attr format="reference" name="floatingActionButtonTertiaryStyle"/>
    <attr format="reference" name="headerLayout"/>
    <attr format="reference" name="hideMotionSpec"/>
    <attr format="boolean" name="isMaterial3Theme"/>
    <attr format="boolean" name="isMaterialTheme"/>
    <attr format="reference" name="itemShapeAppearance"/>
    <attr format="reference" name="itemShapeAppearanceOverlay"/>
    <attr format="color" name="itemTextColor"/>
    <attr format="reference" name="keyboardIcon"/>
    <attr format="reference" name="linearProgressIndicatorStyle"/>
    <attr format="reference" name="materialButtonOutlinedStyle"/>
    <attr format="reference" name="materialButtonStyle"/>
    <attr format="reference" name="materialButtonToggleGroupStyle"/>
    <attr format="reference" name="materialCalendarDay"/>
    <attr format="reference" name="materialCalendarDayOfWeekLabel"/>
    <attr format="reference" name="materialCalendarFullscreenTheme"/>
    <attr format="reference" name="materialCalendarHeaderCancelButton"/>
    <attr format="reference" name="materialCalendarHeaderConfirmButton"/>
    <attr format="reference" name="materialCalendarHeaderDivider"/>
    <attr format="reference" name="materialCalendarHeaderLayout"/>
    <attr format="reference" name="materialCalendarHeaderSelection"/>
    <attr format="reference" name="materialCalendarHeaderTitle"/>
    <attr format="reference" name="materialCalendarHeaderToggleButton"/>
    <attr format="reference" name="materialCalendarMonth"/>
    <attr format="reference" name="materialCalendarMonthNavigationButton"/>
    <attr format="reference" name="materialCalendarStyle"/>
    <attr format="reference" name="materialCalendarTheme"/>
    <attr format="reference" name="materialCalendarYearNavigationButton"/>
    <attr format="reference" name="materialCardViewElevatedStyle"/>
    <attr format="reference" name="materialCardViewFilledStyle"/>
    <attr format="reference" name="materialCardViewOutlinedStyle"/>
    <attr format="reference" name="materialCardViewStyle"/>
    <attr format="dimension" name="materialCircleRadius"/>
    <attr format="reference" name="materialClockStyle"/>
    <attr format="reference" name="materialDisplayDividerStyle"/>
    <attr name="materialDividerHeavyStyle" type="reference"/>
    <attr name="materialDividerStyle" type="reference"/>
    <attr format="reference" name="materialThemeOverlay"/>
    <attr format="reference" name="materialTimePickerStyle"/>
    <attr format="reference" name="materialTimePickerTheme"/>
    <attr format="reference" name="materialTimePickerTitleStyle"/>
    <attr format="dimension" name="minTouchTargetSize"/>
    <attr format="integer" name="motionDurationLong1"/>
    <attr format="integer" name="motionDurationLong2"/>
    <attr format="integer" name="motionDurationMedium1"/>
    <attr format="integer" name="motionDurationMedium2"/>
    <attr format="integer" name="motionDurationShort1"/>
    <attr format="integer" name="motionDurationShort2"/>
    <attr format="string" name="motionEasingAccelerated"/>
    <attr format="string" name="motionEasingDecelerated"/>
    <attr format="string" name="motionEasingEmphasized"/>
    <attr format="string" name="motionEasingLinear"/>
    <attr format="string" name="motionEasingStandard"/>
    <attr format="enum|string" name="motionPath">
    <enum name="linear" value="0"/>
    <enum name="arc" value="1"/>
  </attr>
    <attr format="color" name="navigationIconTint"/>
    <attr format="reference" name="navigationRailStyle"/>
    <attr format="reference" name="navigationViewStyle"/>
    <attr format="reference" name="popupMenuBackground"/>
    <attr format="color" name="rippleColor"/>
    <attr format="color|reference" name="scrimBackground"/>
    <attr format="boolean" name="selectionRequired"/>
    <attr format="reference" name="shapeAppearanceLargeComponent"/>
    <attr format="reference" name="shapeAppearanceMediumComponent"/>
    <attr format="reference" name="shapeAppearanceSmallComponent"/>
    <attr format="reference" name="showMotionSpec"/>
    <attr format="boolean" name="singleSelection"/>
    <attr format="reference" name="sliderStyle"/>
    <attr format="color" name="strokeColor"/>
    <attr format="dimension" name="strokeWidth"/>
    <attr format="reference" name="tabSecondaryStyle"/>
    <attr format="reference" name="tabStyle"/>
    <attr format="reference" name="textAppearanceBody1"/>
    <attr format="reference" name="textAppearanceBody2"/>
    <attr format="reference" name="textAppearanceBodyLarge"/>
    <attr format="reference" name="textAppearanceBodyMedium"/>
    <attr format="reference" name="textAppearanceBodySmall"/>
    <attr format="reference" name="textAppearanceButton"/>
    <attr format="reference" name="textAppearanceCaption"/>
    <attr format="reference" name="textAppearanceDisplayLarge"/>
    <attr format="reference" name="textAppearanceDisplayMedium"/>
    <attr format="reference" name="textAppearanceDisplaySmall"/>
    <attr format="reference" name="textAppearanceHeadline1"/>
    <attr format="reference" name="textAppearanceHeadline2"/>
    <attr format="reference" name="textAppearanceHeadline3"/>
    <attr format="reference" name="textAppearanceHeadline4"/>
    <attr format="reference" name="textAppearanceHeadline5"/>
    <attr format="reference" name="textAppearanceHeadline6"/>
    <attr format="reference" name="textAppearanceHeadlineLarge"/>
    <attr format="reference" name="textAppearanceHeadlineMedium"/>
    <attr format="reference" name="textAppearanceHeadlineSmall"/>
    <attr format="reference" name="textAppearanceLabelLarge"/>
    <attr format="reference" name="textAppearanceLabelMedium"/>
    <attr format="reference" name="textAppearanceLabelSmall"/>
    <attr format="boolean" name="textAppearanceLineHeightEnabled"/>
    <attr format="reference" name="textAppearanceOverline"/>
    <attr format="reference" name="textAppearanceSubtitle1"/>
    <attr format="reference" name="textAppearanceSubtitle2"/>
    <attr format="reference" name="textAppearanceTitleLarge"/>
    <attr format="reference" name="textAppearanceTitleMedium"/>
    <attr format="reference" name="textAppearanceTitleSmall"/>
    <attr format="reference" name="textInputFilledDenseStyle"/>
    <attr format="reference" name="textInputFilledExposedDropdownMenuStyle"/>
    <attr format="reference" name="textInputFilledStyle"/>
    <attr format="reference" name="textInputOutlinedDenseStyle"/>
    <attr format="reference" name="textInputOutlinedExposedDropdownMenuStyle"/>
    <attr format="reference" name="textInputOutlinedStyle"/>
    <attr format="reference" name="textInputStyle"/>
    <attr format="dimension" name="themeLineHeight"/>
    <attr format="reference" name="toolbarSurfaceStyle"/>
    <attr format="reference" name="tooltipStyle"/>
    <attr format="color" name="trackColor"/>
    <attr format="reference" name="transitionShapeAppearance"/>
    <attr format="boolean" name="useMaterialThemeColors"/>
    <bool name="m3_sys_typescale_body_large_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_body_medium_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_body_small_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_display_large_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_display_medium_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_display_small_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_headline_large_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_headline_medium_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_headline_small_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_label_large_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_label_medium_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_label_small_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_title_large_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_title_medium_text_all_caps">false</bool>
    <bool name="m3_sys_typescale_title_small_text_all_caps">false</bool>
    <bool name="mtrl_btn_textappearance_all_caps">true</bool>
    <color name="design_bottom_navigation_shadow_color">#14000000</color>
    <color name="design_dark_default_color_background">#121212</color>
    <color name="design_dark_default_color_error">#CF6679</color>
    <color name="design_dark_default_color_on_background">#FFFFFF</color>
    <color name="design_dark_default_color_on_error">#000000</color>
    <color name="design_dark_default_color_on_primary">#000000</color>
    <color name="design_dark_default_color_on_secondary">#000000</color>
    <color name="design_dark_default_color_on_surface">#FFFFFF</color>
    <color name="design_dark_default_color_primary">#BA86FC</color>
    <color name="design_dark_default_color_primary_dark">#000000</color>
    <color name="design_dark_default_color_primary_variant">#3700B3</color>
    <color name="design_dark_default_color_secondary">#03DAC6</color>
    <color name="design_dark_default_color_secondary_variant">#03DAC6</color>
    <color name="design_dark_default_color_surface">#121212</color>
    <color name="design_default_color_background">#FFFFFF</color>
    <color name="design_default_color_error">#B00020</color>
    <color name="design_default_color_on_background">#000000</color>
    <color name="design_default_color_on_error">#FFFFFF</color>
    <color name="design_default_color_on_primary">#FFFFFF</color>
    <color name="design_default_color_on_secondary">#000000</color>
    <color name="design_default_color_on_surface">#000000</color>
    <color name="design_default_color_primary">#6200EE</color>
    <color name="design_default_color_primary_dark">#3700B3</color>
    <color name="design_default_color_primary_variant">#3700B3</color>
    <color name="design_default_color_secondary">#03DAC6</color>
    <color name="design_default_color_secondary_variant">#018786</color>
    <color name="design_default_color_surface">#FFFFFF</color>
    <color name="design_fab_shadow_end_color">@android:color/transparent</color>
    <color name="design_fab_shadow_mid_color">#14000000</color>
    <color name="design_fab_shadow_start_color">#44000000</color>
    <color name="design_fab_stroke_end_inner_color">#0A000000</color>
    <color name="design_fab_stroke_end_outer_color">#0F000000</color>
    <color name="design_fab_stroke_top_inner_color">#1AFFFFFF</color>
    <color name="design_fab_stroke_top_outer_color">#2EFFFFFF</color>
    <color name="design_snackbar_background_color">#323232</color>
    <color name="m3_ref_palette_error0">#ff000000</color>
    <color name="m3_ref_palette_error10">#ff410e0b</color>
    <color name="m3_ref_palette_error100">#ffffffff</color>
    <color name="m3_ref_palette_error20">#ff601410</color>
    <color name="m3_ref_palette_error30">#ff8c1d18</color>
    <color name="m3_ref_palette_error40">#ffb3261e</color>
    <color name="m3_ref_palette_error50">#ffdc362e</color>
    <color name="m3_ref_palette_error60">#ffe46962</color>
    <color name="m3_ref_palette_error70">#ffec928e</color>
    <color name="m3_ref_palette_error80">#fff2b8b5</color>
    <color name="m3_ref_palette_error90">#fff9dedc</color>
    <color name="m3_ref_palette_error95">#fffceeee</color>
    <color name="m3_ref_palette_error99">#fffffbf9</color>
    <color name="m3_ref_palette_neutral0">#ff000000</color>
    <color name="m3_ref_palette_neutral10">#ff1c1b1f</color>
    <color name="m3_ref_palette_neutral100">#ffffffff</color>
    <color name="m3_ref_palette_neutral20">#ff313033</color>
    <color name="m3_ref_palette_neutral30">#ff484649</color>
    <color name="m3_ref_palette_neutral40">#ff605d62</color>
    <color name="m3_ref_palette_neutral50">#ff787579</color>
    <color name="m3_ref_palette_neutral60">#ff939094</color>
    <color name="m3_ref_palette_neutral70">#ffaeaaae</color>
    <color name="m3_ref_palette_neutral80">#ffc9c5ca</color>
    <color name="m3_ref_palette_neutral90">#ffe6e1e5</color>
    <color name="m3_ref_palette_neutral95">#fff4eff4</color>
    <color name="m3_ref_palette_neutral99">#fffffbfe</color>
    <color name="m3_ref_palette_neutral_variant0">#ff000000</color>
    <color name="m3_ref_palette_neutral_variant10">#ff1d1a22</color>
    <color name="m3_ref_palette_neutral_variant100">#ffffffff</color>
    <color name="m3_ref_palette_neutral_variant20">#ff322f37</color>
    <color name="m3_ref_palette_neutral_variant30">#ff49454f</color>
    <color name="m3_ref_palette_neutral_variant40">#ff605d66</color>
    <color name="m3_ref_palette_neutral_variant50">#ff79747e</color>
    <color name="m3_ref_palette_neutral_variant60">#ff938f99</color>
    <color name="m3_ref_palette_neutral_variant70">#ffaea9b4</color>
    <color name="m3_ref_palette_neutral_variant80">#ffcac4d0</color>
    <color name="m3_ref_palette_neutral_variant90">#ffe7e0ec</color>
    <color name="m3_ref_palette_neutral_variant95">#fff5eefa</color>
    <color name="m3_ref_palette_neutral_variant99">#fffffbfe</color>
    <color name="m3_ref_palette_primary0">#ff000000</color>
    <color name="m3_ref_palette_primary10">#ff21005d</color>
    <color name="m3_ref_palette_primary100">#ffffffff</color>
    <color name="m3_ref_palette_primary20">#ff381e72</color>
    <color name="m3_ref_palette_primary30">#ff4f378b</color>
    <color name="m3_ref_palette_primary40">#ff6750a4</color>
    <color name="m3_ref_palette_primary50">#ff7f67be</color>
    <color name="m3_ref_palette_primary60">#ff9a82db</color>
    <color name="m3_ref_palette_primary70">#ffb69df8</color>
    <color name="m3_ref_palette_primary80">#ffd0bcff</color>
    <color name="m3_ref_palette_primary90">#ffeaddff</color>
    <color name="m3_ref_palette_primary95">#fff6edff</color>
    <color name="m3_ref_palette_primary99">#fffffbfe</color>
    <color name="m3_ref_palette_secondary0">#ff000000</color>
    <color name="m3_ref_palette_secondary10">#ff1d192b</color>
    <color name="m3_ref_palette_secondary100">#ffffffff</color>
    <color name="m3_ref_palette_secondary20">#ff332d41</color>
    <color name="m3_ref_palette_secondary30">#ff4a4458</color>
    <color name="m3_ref_palette_secondary40">#ff625b71</color>
    <color name="m3_ref_palette_secondary50">#ff7a7289</color>
    <color name="m3_ref_palette_secondary60">#ff958da5</color>
    <color name="m3_ref_palette_secondary70">#ffb0a7c0</color>
    <color name="m3_ref_palette_secondary80">#ffccc2dc</color>
    <color name="m3_ref_palette_secondary90">#ffe8def8</color>
    <color name="m3_ref_palette_secondary95">#fff6edff</color>
    <color name="m3_ref_palette_secondary99">#fffffbfe</color>
    <color name="m3_ref_palette_tertiary0">#ff000000</color>
    <color name="m3_ref_palette_tertiary10">#ff31111d</color>
    <color name="m3_ref_palette_tertiary100">#ffffffff</color>
    <color name="m3_ref_palette_tertiary20">#ff492532</color>
    <color name="m3_ref_palette_tertiary30">#ff633b48</color>
    <color name="m3_ref_palette_tertiary40">#ff7d5260</color>
    <color name="m3_ref_palette_tertiary50">#ff986977</color>
    <color name="m3_ref_palette_tertiary60">#ffb58392</color>
    <color name="m3_ref_palette_tertiary70">#ffd29dac</color>
    <color name="m3_ref_palette_tertiary80">#ffefb8c8</color>
    <color name="m3_ref_palette_tertiary90">#ffffd8e4</color>
    <color name="m3_ref_palette_tertiary95">#ffffecf1</color>
    <color name="m3_ref_palette_tertiary99">#fffffbfa</color>
    <color name="m3_sys_color_dark_background">@color/m3_ref_palette_neutral10</color>
    <color name="m3_sys_color_dark_error">@color/m3_ref_palette_error80</color>
    <color name="m3_sys_color_dark_error_container">@color/m3_ref_palette_error30</color>
    <color name="m3_sys_color_dark_inverse_on_surface">@color/m3_ref_palette_neutral20</color>
    <color name="m3_sys_color_dark_inverse_primary">@color/m3_ref_palette_primary40</color>
    <color name="m3_sys_color_dark_inverse_surface">@color/m3_ref_palette_neutral90</color>
    <color name="m3_sys_color_dark_on_background">@color/m3_ref_palette_neutral90</color>
    <color name="m3_sys_color_dark_on_error">@color/m3_ref_palette_error20</color>
    <color name="m3_sys_color_dark_on_error_container">@color/m3_ref_palette_error80</color>
    <color name="m3_sys_color_dark_on_primary">@color/m3_ref_palette_primary20</color>
    <color name="m3_sys_color_dark_on_primary_container">@color/m3_ref_palette_primary90</color>
    <color name="m3_sys_color_dark_on_secondary">@color/m3_ref_palette_secondary20</color>
    <color name="m3_sys_color_dark_on_secondary_container">@color/m3_ref_palette_secondary90</color>
    <color name="m3_sys_color_dark_on_surface">@color/m3_ref_palette_neutral90</color>
    <color name="m3_sys_color_dark_on_surface_variant">@color/m3_ref_palette_neutral_variant80</color>
    <color name="m3_sys_color_dark_on_tertiary">@color/m3_ref_palette_tertiary20</color>
    <color name="m3_sys_color_dark_on_tertiary_container">@color/m3_ref_palette_tertiary90</color>
    <color name="m3_sys_color_dark_outline">@color/m3_ref_palette_neutral_variant60</color>
    <color name="m3_sys_color_dark_primary">@color/m3_ref_palette_primary80</color>
    <color name="m3_sys_color_dark_primary_container">@color/m3_ref_palette_primary30</color>
    <color name="m3_sys_color_dark_secondary">@color/m3_ref_palette_secondary80</color>
    <color name="m3_sys_color_dark_secondary_container">@color/m3_ref_palette_secondary30</color>
    <color name="m3_sys_color_dark_surface">@color/m3_ref_palette_neutral10</color>
    <color name="m3_sys_color_dark_surface_variant">@color/m3_ref_palette_neutral_variant30</color>
    <color name="m3_sys_color_dark_tertiary">@color/m3_ref_palette_tertiary80</color>
    <color name="m3_sys_color_dark_tertiary_container">@color/m3_ref_palette_tertiary30</color>
    <color name="m3_sys_color_light_background">@color/m3_ref_palette_neutral99</color>
    <color name="m3_sys_color_light_error">@color/m3_ref_palette_error40</color>
    <color name="m3_sys_color_light_error_container">@color/m3_ref_palette_error90</color>
    <color name="m3_sys_color_light_inverse_on_surface">@color/m3_ref_palette_neutral95</color>
    <color name="m3_sys_color_light_inverse_primary">@color/m3_ref_palette_primary80</color>
    <color name="m3_sys_color_light_inverse_surface">@color/m3_ref_palette_neutral20</color>
    <color name="m3_sys_color_light_on_background">@color/m3_ref_palette_neutral10</color>
    <color name="m3_sys_color_light_on_error">@color/m3_ref_palette_error100</color>
    <color name="m3_sys_color_light_on_error_container">@color/m3_ref_palette_error10</color>
    <color name="m3_sys_color_light_on_primary">@color/m3_ref_palette_primary100</color>
    <color name="m3_sys_color_light_on_primary_container">@color/m3_ref_palette_primary10</color>
    <color name="m3_sys_color_light_on_secondary">@color/m3_ref_palette_secondary100</color>
    <color name="m3_sys_color_light_on_secondary_container">@color/m3_ref_palette_secondary10</color>
    <color name="m3_sys_color_light_on_surface">@color/m3_ref_palette_neutral10</color>
    <color name="m3_sys_color_light_on_surface_variant">@color/m3_ref_palette_neutral_variant30</color>
    <color name="m3_sys_color_light_on_tertiary">@color/m3_ref_palette_tertiary100</color>
    <color name="m3_sys_color_light_on_tertiary_container">@color/m3_ref_palette_tertiary10</color>
    <color name="m3_sys_color_light_outline">@color/m3_ref_palette_neutral_variant50</color>
    <color name="m3_sys_color_light_primary">@color/m3_ref_palette_primary40</color>
    <color name="m3_sys_color_light_primary_container">@color/m3_ref_palette_primary90</color>
    <color name="m3_sys_color_light_secondary">@color/m3_ref_palette_secondary40</color>
    <color name="m3_sys_color_light_secondary_container">@color/m3_ref_palette_secondary90</color>
    <color name="m3_sys_color_light_surface">@color/m3_ref_palette_neutral99</color>
    <color name="m3_sys_color_light_surface_variant">@color/m3_ref_palette_neutral_variant90</color>
    <color name="m3_sys_color_light_tertiary">@color/m3_ref_palette_tertiary40</color>
    <color name="m3_sys_color_light_tertiary_container">@color/m3_ref_palette_tertiary90</color>
    <color name="mtrl_btn_text_color_disabled">#61000000</color>
    <color name="mtrl_btn_transparent_bg_color">#00ffffff</color>
    <color name="mtrl_scrim_color">#52000000</color>
    <color name="mtrl_textinput_default_box_stroke_color">#6B000000</color>
    <color name="mtrl_textinput_disabled_color">#1F000000</color>
    <color name="mtrl_textinput_filled_box_default_background_color">#0A000000</color>
    <color name="mtrl_textinput_focused_box_stroke_color">#00000000</color>
    <color name="mtrl_textinput_hovered_box_stroke_color">#DE000000</color>
    <color name="test_color">#000</color>
    <dimen name="action_bar_size">16dp</dimen>
    <dimen name="appcompat_dialog_background_inset">16dp</dimen>
    <dimen name="clock_face_margin_start">64dp</dimen>
    <dimen name="default_dimension">100dp</dimen>
    <dimen name="design_appbar_elevation">4dp</dimen>
    <dimen name="design_bottom_navigation_active_item_max_width">168dp</dimen>
    <dimen name="design_bottom_navigation_active_item_min_width">96dp</dimen>
    <dimen name="design_bottom_navigation_active_text_size">14sp</dimen>
    <dimen name="design_bottom_navigation_elevation">8dp</dimen>
    <dimen name="design_bottom_navigation_height">56dp</dimen>
    <dimen name="design_bottom_navigation_icon_size">24dp</dimen>
    <dimen name="design_bottom_navigation_item_max_width">96dp</dimen>
    <dimen name="design_bottom_navigation_item_min_width">56dp</dimen>
    <dimen name="design_bottom_navigation_label_padding">10dp</dimen>
    <dimen name="design_bottom_navigation_margin">8dp</dimen>
    <dimen name="design_bottom_navigation_shadow_height">1dp</dimen>
    <dimen name="design_bottom_navigation_text_size">12sp</dimen>
    <dimen name="design_bottom_sheet_elevation">8dp</dimen>
    <dimen name="design_bottom_sheet_modal_elevation">16dp</dimen>
    <dimen name="design_bottom_sheet_peek_height_min">64dp</dimen>
    <dimen name="design_fab_border_width">0.5dp</dimen>
    <dimen name="design_fab_elevation">6dp</dimen>
    <dimen name="design_fab_image_size">24dp</dimen>
    <dimen name="design_fab_size_mini">40dp</dimen>
    <dimen name="design_fab_size_normal">56dp</dimen>
    <dimen name="design_fab_translation_z_hovered_focused">6dp</dimen>
    <dimen name="design_fab_translation_z_pressed">6dp</dimen>
    <dimen name="design_navigation_elevation">16dp</dimen>
    <dimen name="design_navigation_icon_padding">32dp</dimen>
    <dimen name="design_navigation_icon_size">24dp</dimen>
    <dimen name="design_navigation_item_horizontal_padding">16dp</dimen>
    <dimen name="design_navigation_item_icon_padding">32dp</dimen>
    <dimen name="design_navigation_item_vertical_padding">4dp</dimen>
    <dimen name="design_navigation_max_width">280dp</dimen>
    <dimen name="design_navigation_padding_bottom">8dp</dimen>
    <dimen name="design_navigation_separator_vertical_padding">8dp</dimen>
    <dimen name="design_snackbar_action_inline_max_width">128dp</dimen>
    <item format="float" name="design_snackbar_action_text_color_alpha" type="dimen">1.0</item>
    <dimen name="design_snackbar_background_corner_radius">0dp</dimen>
    <dimen name="design_snackbar_elevation">6dp</dimen>
    <dimen name="design_snackbar_extra_spacing_horizontal">0dp</dimen>
    <dimen name="design_snackbar_max_width">-1px</dimen>
    <dimen name="design_snackbar_min_width">-1px</dimen>
    <dimen name="design_snackbar_padding_horizontal">12dp</dimen>
    <dimen name="design_snackbar_padding_vertical">14dp</dimen>
    <dimen name="design_snackbar_padding_vertical_2lines">16dp</dimen>
    <dimen name="design_snackbar_text_size">14sp</dimen>
    <dimen name="design_tab_max_width">264dp</dimen>
    <dimen name="design_tab_scrollable_min_width">72dp</dimen>
    <dimen name="design_tab_text_size">14sp</dimen>
    <dimen name="design_tab_text_size_2line">12sp</dimen>
    <dimen name="design_textinput_caption_translate_y">5dp</dimen>
    <dimen name="m3_alert_dialog_action_bottom_padding">14dp</dimen>
    <dimen name="m3_alert_dialog_action_top_padding">14dp</dimen>
    <dimen name="m3_alert_dialog_corner_size">28dp</dimen>
    <dimen name="m3_alert_dialog_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_alert_dialog_icon_margin">16dp</dimen>
    <dimen name="m3_alert_dialog_icon_size">24dp</dimen>
    <dimen name="m3_alert_dialog_title_bottom_margin">16dp</dimen>
    <dimen name="m3_appbar_expanded_title_margin_bottom">16dp</dimen>
    <dimen name="m3_appbar_expanded_title_margin_horizontal">16dp</dimen>
    <dimen name="m3_appbar_scrim_height_trigger">96dp</dimen>
    <dimen name="m3_appbar_scrim_height_trigger_large">112dp</dimen>
    <dimen name="m3_appbar_scrim_height_trigger_medium">112dp</dimen>
    <dimen name="m3_appbar_size_compact">64dp</dimen>
    <dimen name="m3_appbar_size_large">152dp</dimen>
    <dimen name="m3_appbar_size_medium">112dp</dimen>
    <dimen name="m3_badge_horizontal_offset">1.5dp</dimen>
    <dimen name="m3_badge_radius">3dp</dimen>
    <dimen name="m3_badge_vertical_offset">1.5dp</dimen>
    <dimen name="m3_badge_with_text_horizontal_offset">3dp</dimen>
    <dimen name="m3_badge_with_text_radius">7dp</dimen>
    <dimen name="m3_badge_with_text_vertical_offset">4dp</dimen>
    <dimen name="m3_bottom_nav_item_active_indicator_height">32dp</dimen>
    <dimen name="m3_bottom_nav_item_active_indicator_margin_horizontal">4dp</dimen>
    <dimen name="m3_bottom_nav_item_active_indicator_width">64dp</dimen>
    <dimen name="m3_bottom_nav_item_padding_bottom">16dp</dimen>
    <dimen name="m3_bottom_nav_item_padding_top">12dp</dimen>
    <dimen name="m3_bottom_nav_min_height">80dp</dimen>
    <dimen name="m3_bottom_sheet_elevation">@dimen/m3_sys_elevation_level2</dimen>
    <dimen name="m3_bottom_sheet_modal_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_bottomappbar_fab_cradle_margin">6dp</dimen>
    <dimen name="m3_bottomappbar_fab_cradle_rounded_corner_radius">4dp</dimen>
    <dimen name="m3_bottomappbar_fab_cradle_vertical_offset">12dp</dimen>
    <dimen name="m3_btn_dialog_btn_min_width">64dp</dimen>
    <dimen name="m3_btn_dialog_btn_spacing">8dp</dimen>
    <dimen name="m3_btn_disabled_elevation">0dp</dimen>
    <dimen name="m3_btn_disabled_translation_z">0dp</dimen>
    <dimen name="m3_btn_elevated_btn_elevation">1dp</dimen>
    <dimen name="m3_btn_elevation">0dp</dimen>
    <dimen name="m3_btn_icon_btn_padding_left">16dp</dimen>
    <dimen name="m3_btn_icon_btn_padding_right">24dp</dimen>
    <dimen name="m3_btn_icon_only_default_padding">10dp</dimen>
    <dimen name="m3_btn_icon_only_default_size">20dp</dimen>
    <dimen name="m3_btn_icon_only_icon_padding">0dp</dimen>
    <dimen name="m3_btn_icon_only_min_width">20dp</dimen>
    <dimen name="m3_btn_inset">4dp</dimen>
    <dimen name="m3_btn_max_width">320dp</dimen>
    <dimen name="m3_btn_padding_bottom">6dp</dimen>
    <dimen name="m3_btn_padding_left">24dp</dimen>
    <dimen name="m3_btn_padding_right">24dp</dimen>
    <dimen name="m3_btn_padding_top">6dp</dimen>
    <dimen name="m3_btn_stroke_size">1dp</dimen>
    <dimen name="m3_btn_text_btn_icon_padding_left">12dp</dimen>
    <dimen name="m3_btn_text_btn_icon_padding_right">16dp</dimen>
    <dimen name="m3_btn_text_btn_padding_left">12dp</dimen>
    <dimen name="m3_btn_text_btn_padding_right">12dp</dimen>
    <dimen name="m3_btn_translation_z_base">0dp</dimen>
    <dimen name="m3_btn_translation_z_hovered">1dp</dimen>
    <dimen name="m3_card_dragged_z">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_card_elevated_dragged_z">7dp</dimen>
    <dimen name="m3_card_elevated_elevation">@dimen/m3_sys_elevation_level1</dimen>
    <dimen name="m3_card_elevated_hovered_z">2dp</dimen>
    <dimen name="m3_card_elevation">@dimen/m3_sys_elevation_level0</dimen>
    <dimen name="m3_card_hovered_z">@dimen/m3_sys_elevation_level1</dimen>
    <dimen name="m3_card_stroke_width">1dp</dimen>
    <dimen name="m3_chip_checked_hovered_translation_z">1dp</dimen>
    <dimen name="m3_chip_corner_size">8dp</dimen>
    <dimen name="m3_chip_disabled_translation_z">-1dp</dimen>
    <dimen name="m3_chip_dragged_translation_z">7dp</dimen>
    <dimen name="m3_chip_elevated_elevation">1dp</dimen>
    <dimen name="m3_chip_hovered_translation_z">2dp</dimen>
    <dimen name="m3_chip_icon_size">18dp</dimen>
    <dimen name="m3_datepicker_elevation">@dimen/m3_sys_elevation_level1</dimen>
    <dimen name="m3_divider_heavy_thickness">8dp</dimen>
    <dimen name="m3_extended_fab_bottom_padding">8dp</dimen>
    <dimen name="m3_extended_fab_end_padding">20dp</dimen>
    <dimen name="m3_extended_fab_icon_padding">12dp</dimen>
    <dimen name="m3_extended_fab_min_height">56dp</dimen>
    <dimen name="m3_extended_fab_start_padding">16dp</dimen>
    <dimen name="m3_extended_fab_top_padding">8dp</dimen>
    <dimen name="m3_fab_border_width">0dp</dimen>
    <dimen name="m3_fab_corner_size">30%</dimen>
    <dimen name="m3_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="m3_fab_translation_z_pressed">6dp</dimen>
    <dimen name="m3_large_fab_max_image_size">36dp</dimen>
    <dimen name="m3_large_fab_size">96dp</dimen>
    <dimen name="m3_menu_elevation">@dimen/m3_sys_elevation_level2</dimen>
    <dimen name="m3_navigation_drawer_layout_corner_size">16dp</dimen>
    <dimen name="m3_navigation_item_horizontal_padding">28dp</dimen>
    <dimen name="m3_navigation_item_icon_padding">12dp</dimen>
    <dimen name="m3_navigation_item_shape_inset_bottom">0dp</dimen>
    <dimen name="m3_navigation_item_shape_inset_end">12dp</dimen>
    <dimen name="m3_navigation_item_shape_inset_start">12dp</dimen>
    <dimen name="m3_navigation_item_shape_inset_top">0dp</dimen>
    <dimen name="m3_navigation_item_vertical_padding">4dp</dimen>
    <dimen name="m3_navigation_menu_divider_horizontal_padding">28dp</dimen>
    <dimen name="m3_navigation_menu_headline_horizontal_padding">28dp</dimen>
    <dimen name="m3_navigation_rail_default_width">80dp</dimen>
    <dimen name="m3_navigation_rail_item_active_indicator_height">32dp</dimen>
    <dimen name="m3_navigation_rail_item_active_indicator_margin_horizontal">4dp</dimen>
    <dimen name="m3_navigation_rail_item_active_indicator_width">56dp</dimen>
    <dimen name="m3_navigation_rail_item_min_height">60dp</dimen>
    <dimen name="m3_navigation_rail_item_padding_bottom">8dp</dimen>
    <dimen name="m3_navigation_rail_item_padding_top">4dp</dimen>
    <dimen name="m3_ripple_default_alpha">@dimen/m3_sys_state_dragged_state_layer_opacity</dimen>
    <dimen name="m3_ripple_focused_alpha">@dimen/m3_sys_state_focus_state_layer_opacity</dimen>
    <dimen name="m3_ripple_hovered_alpha">@dimen/m3_sys_state_hover_state_layer_opacity</dimen>
    <dimen name="m3_ripple_pressed_alpha">@dimen/m3_sys_state_pressed_state_layer_opacity</dimen>
    <dimen name="m3_ripple_selectable_pressed_alpha">@dimen/m3_sys_state_pressed_state_layer_opacity</dimen>
    <dimen name="m3_slider_thumb_elevation">2dp</dimen>
    <item format="float" name="m3_snackbar_action_text_color_alpha" type="dimen">1.0</item>
    <dimen name="m3_snackbar_margin">8dp</dimen>
    <dimen name="m3_sys_elevation_level0">0dp</dimen>
    <dimen name="m3_sys_elevation_level1">1dp</dimen>
    <dimen name="m3_sys_elevation_level2">3dp</dimen>
    <dimen name="m3_sys_elevation_level3">6dp</dimen>
    <dimen name="m3_sys_elevation_level4">8dp</dimen>
    <dimen name="m3_sys_elevation_level5">12dp</dimen>
    <dimen name="m3_sys_shape_large_corner_size">8dp</dimen>
    <dimen name="m3_sys_shape_medium_corner_size">8dp</dimen>
    <dimen name="m3_sys_shape_small_corner_size">4dp</dimen>
    <item format="float" name="m3_sys_state_dragged_state_layer_opacity" type="dimen">0.16</item>
    <item format="float" name="m3_sys_state_focus_state_layer_opacity" type="dimen">0.12</item>
    <item format="float" name="m3_sys_state_hover_state_layer_opacity" type="dimen">0.08</item>
    <item format="float" name="m3_sys_state_pressed_state_layer_opacity" type="dimen">0.12</item>
    <item format="float" name="m3_sys_typescale_body_large_letter_spacing" type="dimen">0.03125</item>
    <dimen name="m3_sys_typescale_body_large_text_size">16sp</dimen>
    <item format="float" name="m3_sys_typescale_body_medium_letter_spacing" type="dimen">0.01785714</item>
    <dimen name="m3_sys_typescale_body_medium_text_size">14sp</dimen>
    <item format="float" name="m3_sys_typescale_body_small_letter_spacing" type="dimen">0.03333333</item>
    <dimen name="m3_sys_typescale_body_small_text_size">12sp</dimen>
    <item format="float" name="m3_sys_typescale_display_large_letter_spacing" type="dimen">-0.00438596</item>
    <dimen name="m3_sys_typescale_display_large_text_size">57sp</dimen>
    <item format="float" name="m3_sys_typescale_display_medium_letter_spacing" type="dimen">0</item>
    <dimen name="m3_sys_typescale_display_medium_text_size">45sp</dimen>
    <item format="float" name="m3_sys_typescale_display_small_letter_spacing" type="dimen">0</item>
    <dimen name="m3_sys_typescale_display_small_text_size">36sp</dimen>
    <item format="float" name="m3_sys_typescale_headline_large_letter_spacing" type="dimen">0</item>
    <dimen name="m3_sys_typescale_headline_large_text_size">32sp</dimen>
    <item format="float" name="m3_sys_typescale_headline_medium_letter_spacing" type="dimen">0</item>
    <dimen name="m3_sys_typescale_headline_medium_text_size">28sp</dimen>
    <item format="float" name="m3_sys_typescale_headline_small_letter_spacing" type="dimen">0</item>
    <dimen name="m3_sys_typescale_headline_small_text_size">24sp</dimen>
    <item format="float" name="m3_sys_typescale_label_large_letter_spacing" type="dimen">0.00714286</item>
    <dimen name="m3_sys_typescale_label_large_text_size">14sp</dimen>
    <item format="float" name="m3_sys_typescale_label_medium_letter_spacing" type="dimen">0.04166667</item>
    <dimen name="m3_sys_typescale_label_medium_text_size">12sp</dimen>
    <item format="float" name="m3_sys_typescale_label_small_letter_spacing" type="dimen">0.04545455</item>
    <dimen name="m3_sys_typescale_label_small_text_size">11sp</dimen>
    <item format="float" name="m3_sys_typescale_title_large_letter_spacing" type="dimen">0</item>
    <dimen name="m3_sys_typescale_title_large_text_size">22sp</dimen>
    <item format="float" name="m3_sys_typescale_title_medium_letter_spacing" type="dimen">0.009375</item>
    <dimen name="m3_sys_typescale_title_medium_text_size">16sp</dimen>
    <item format="float" name="m3_sys_typescale_title_small_letter_spacing" type="dimen">0.00714286</item>
    <dimen name="m3_sys_typescale_title_small_text_size">14sp</dimen>
    <dimen name="m3_timepicker_display_stroke_width">2dp</dimen>
    <dimen name="m3_timepicker_window_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="material_bottom_sheet_max_width">640dp</dimen>
    <dimen name="material_clock_display_padding">24dp</dimen>
    <dimen name="material_clock_face_margin_top">44dp</dimen>
    <dimen name="material_clock_hand_center_dot_radius">4dp</dimen>
    <dimen name="material_clock_hand_padding">4dp</dimen>
    <dimen name="material_clock_hand_stroke_width">2dp</dimen>
    <dimen name="material_clock_number_text_size">15dp</dimen>
    <dimen name="material_clock_period_toggle_height">96dp</dimen>
    <dimen name="material_clock_period_toggle_margin_left">12dp</dimen>
    <dimen name="material_clock_period_toggle_width">52dp</dimen>
    <dimen name="material_clock_size">256dp</dimen>
    <dimen name="material_cursor_inset_bottom">-6dp</dimen>
    <dimen name="material_cursor_inset_top">-12dp</dimen>
    <dimen name="material_cursor_width">2dp</dimen>
    <dimen name="material_divider_thickness">1dp</dimen>
    <item format="float" name="material_emphasis_disabled" type="dimen">0.38</item>
    <item format="float" name="material_emphasis_disabled_background" type="dimen">0.12</item>
    <item format="float" name="material_emphasis_high_type" type="dimen">0.87</item>
    <item format="float" name="material_emphasis_medium" type="dimen">0.6</item>
    <dimen name="material_filled_edittext_font_1_3_padding_bottom">12dp</dimen>
    <dimen name="material_filled_edittext_font_1_3_padding_top">23dp</dimen>
    <dimen name="material_filled_edittext_font_2_0_padding_bottom">8dp</dimen>
    <dimen name="material_filled_edittext_font_2_0_padding_top">32dp</dimen>
    <dimen name="material_font_1_3_box_collapsed_padding_top">4dp</dimen>
    <dimen name="material_font_2_0_box_collapsed_padding_top">8dp</dimen>
    <dimen name="material_helper_text_default_padding_top">4dp</dimen>
    <dimen name="material_helper_text_font_1_3_padding_horizontal">12dp</dimen>
    <dimen name="material_helper_text_font_1_3_padding_top">8dp</dimen>
    <dimen name="material_input_text_to_prefix_suffix_padding">2dp</dimen>
    <dimen name="material_text_view_test_line_height">200px</dimen>
    <dimen name="material_text_view_test_line_height_override">100px</dimen>
    <dimen name="material_textinput_default_width">245dp</dimen>
    <dimen name="material_textinput_max_width">488dp</dimen>
    <dimen name="material_textinput_min_width">56dp</dimen>
    <dimen name="material_time_picker_minimum_screen_height">560dp</dimen>
    <dimen name="material_time_picker_minimum_screen_width">340dp</dimen>
    <dimen name="material_timepicker_dialog_buttons_margin_top">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_bottom">80dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_end">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_start">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_top">80dp</dimen>
    <dimen name="mtrl_alert_dialog_picker_background_inset">24dp</dimen>
    <dimen name="mtrl_badge_horizontal_edge_offset">4dp</dimen>
    <dimen name="mtrl_badge_long_text_horizontal_padding">4dp</dimen>
    <dimen name="mtrl_badge_radius">4dp</dimen>
    <dimen name="mtrl_badge_text_horizontal_edge_offset">6dp</dimen>
    <dimen name="mtrl_badge_text_size">10sp</dimen>
    <dimen name="mtrl_badge_toolbar_action_menu_item_horizontal_offset">12dp</dimen>
    <dimen name="mtrl_badge_toolbar_action_menu_item_vertical_offset">12dp</dimen>
    <dimen name="mtrl_badge_with_text_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fabOffsetEndMode">60dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_bottom_margin">16dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_margin">5dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_vertical_offset">0dp</dimen>
    <dimen name="mtrl_bottomappbar_height">56dp</dimen>
    <dimen name="mtrl_btn_corner_radius">4dp</dimen>
    <dimen name="mtrl_btn_dialog_btn_min_width">64dp</dimen>
    <dimen name="mtrl_btn_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_btn_disabled_z">0dp</dimen>
    <dimen name="mtrl_btn_elevation">2dp</dimen>
    <dimen name="mtrl_btn_focused_z">2dp</dimen>
    <dimen name="mtrl_btn_hovered_z">2dp</dimen>
    <dimen name="mtrl_btn_icon_btn_padding_left">12dp</dimen>
    <dimen name="mtrl_btn_icon_padding">8dp</dimen>
    <dimen name="mtrl_btn_inset">6dp</dimen>
    <item format="float" name="mtrl_btn_letter_spacing" type="dimen">0.07</item>
    <dimen name="mtrl_btn_max_width">320dp</dimen>
    <dimen name="mtrl_btn_padding_bottom">4dp</dimen>
    <dimen name="mtrl_btn_padding_left">16dp</dimen>
    <dimen name="mtrl_btn_padding_right">16dp</dimen>
    <dimen name="mtrl_btn_padding_top">4dp</dimen>
    <dimen name="mtrl_btn_pressed_z">6dp</dimen>
    <dimen name="mtrl_btn_snackbar_margin_horizontal">8dp</dimen>
    <dimen name="mtrl_btn_stroke_size">1dp</dimen>
    <dimen name="mtrl_btn_text_btn_icon_padding">4dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_left">8dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_right">8dp</dimen>
    <dimen name="mtrl_btn_text_size">14sp</dimen>
    <dimen name="mtrl_btn_z">0dp</dimen>
    <dimen name="mtrl_calendar_action_confirm_button_min_width">64dp</dimen>
    <dimen name="mtrl_calendar_action_height">52dp</dimen>
    <dimen name="mtrl_calendar_action_padding">8dp</dimen>
    <dimen name="mtrl_calendar_bottom_padding">0dp</dimen>
    <dimen name="mtrl_calendar_content_padding">12dp</dimen>
    <dimen name="mtrl_calendar_day_corner">15dp</dimen>
    <dimen name="mtrl_calendar_day_height">32dp</dimen>
    <dimen name="mtrl_calendar_day_horizontal_padding">3dp</dimen>
    <dimen name="mtrl_calendar_day_today_stroke">1dp</dimen>
    <dimen name="mtrl_calendar_day_vertical_padding">1dp</dimen>
    <dimen name="mtrl_calendar_day_width">36dp</dimen>
    <dimen name="mtrl_calendar_days_of_week_height">24dp</dimen>
    <dimen name="mtrl_calendar_dialog_background_inset">16dp</dimen>
    <dimen name="mtrl_calendar_header_content_padding">12dp</dimen>
    <dimen name="mtrl_calendar_header_content_padding_fullscreen">4dp</dimen>
    <dimen name="mtrl_calendar_header_divider_thickness">1dp</dimen>
    <dimen name="mtrl_calendar_header_height">120dp</dimen>
    <dimen name="mtrl_calendar_header_height_fullscreen">128dp</dimen>
    <dimen name="mtrl_calendar_header_selection_line_height">32dp</dimen>
    <dimen name="mtrl_calendar_header_text_padding">12dp</dimen>
    <dimen name="mtrl_calendar_header_toggle_margin_bottom">8dp</dimen>
    <dimen name="mtrl_calendar_header_toggle_margin_top">24dp</dimen>
    <dimen name="mtrl_calendar_landscape_header_width">0dp</dimen>
    <dimen name="mtrl_calendar_maximum_default_fullscreen_minor_axis">480dp</dimen>
    <dimen name="mtrl_calendar_month_horizontal_padding">2dp</dimen>
    <dimen name="mtrl_calendar_month_vertical_padding">0dp</dimen>
    <dimen name="mtrl_calendar_navigation_bottom_padding">4dp</dimen>
    <dimen name="mtrl_calendar_navigation_height">48dp</dimen>
    <dimen name="mtrl_calendar_navigation_top_padding">4dp</dimen>
    <dimen name="mtrl_calendar_pre_l_text_clip_padding">8dp</dimen>
    <dimen name="mtrl_calendar_selection_baseline_to_top_fullscreen">104dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_bottom">20dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen">24dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_top">100dp</dimen>
    <dimen name="mtrl_calendar_text_input_padding_top">16dp</dimen>
    <dimen name="mtrl_calendar_title_baseline_to_top">28dp</dimen>
    <dimen name="mtrl_calendar_title_baseline_to_top_fullscreen">68dp</dimen>
    <dimen name="mtrl_calendar_year_corner">18dp</dimen>
    <dimen name="mtrl_calendar_year_height">52dp</dimen>
    <dimen name="mtrl_calendar_year_horizontal_padding">8dp</dimen>
    <dimen name="mtrl_calendar_year_vertical_padding">8dp</dimen>
    <dimen name="mtrl_calendar_year_width">88dp</dimen>
    <dimen name="mtrl_card_checked_icon_margin">8dp</dimen>
    <dimen name="mtrl_card_checked_icon_size">24dp</dimen>
    <dimen name="mtrl_card_corner_radius">4dp</dimen>
    <dimen name="mtrl_card_dragged_z">5dp</dimen>
    <dimen name="mtrl_card_elevation">1dp</dimen>
    <dimen name="mtrl_card_spacing">8dp</dimen>
    <dimen name="mtrl_chip_pressed_translation_z">3dp</dimen>
    <dimen name="mtrl_chip_text_size">14sp</dimen>
    <dimen name="mtrl_edittext_rectangle_top_offset">12dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_elevation">8dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_offset">-8dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_padding">8dp</dimen>
    <dimen name="mtrl_extended_fab_bottom_padding">12dp</dimen>
    <dimen name="mtrl_extended_fab_corner_radius">24dp</dimen>
    <dimen name="mtrl_extended_fab_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_extended_fab_disabled_translation_z">0dp</dimen>
    <dimen name="mtrl_extended_fab_elevation">6dp</dimen>
    <dimen name="mtrl_extended_fab_end_padding">20dp</dimen>
    <dimen name="mtrl_extended_fab_end_padding_icon">20dp</dimen>
    <dimen name="mtrl_extended_fab_icon_size">24dp</dimen>
    <dimen name="mtrl_extended_fab_icon_text_spacing">12dp</dimen>
    <dimen name="mtrl_extended_fab_min_height">48dp</dimen>
    <dimen name="mtrl_extended_fab_min_width">120dp</dimen>
    <dimen name="mtrl_extended_fab_start_padding">20dp</dimen>
    <dimen name="mtrl_extended_fab_start_padding_icon">12dp</dimen>
    <dimen name="mtrl_extended_fab_top_padding">12dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_base">0dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_pressed">6dp</dimen>
    <dimen name="mtrl_fab_elevation">6dp</dimen>
    <dimen name="mtrl_fab_min_touch_target">48dp</dimen>
    <dimen name="mtrl_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_fab_translation_z_pressed">6dp</dimen>
    <item format="float" name="mtrl_high_ripple_default_alpha" type="dimen">0.00</item>
    <item format="float" name="mtrl_high_ripple_focused_alpha" type="dimen">0.24</item>
    <item format="float" name="mtrl_high_ripple_hovered_alpha" type="dimen">0.08</item>
    <item format="float" name="mtrl_high_ripple_pressed_alpha" type="dimen">0.24</item>
    <dimen name="mtrl_large_touch_target">100dp</dimen>
    <item format="float" name="mtrl_low_ripple_default_alpha" type="dimen">0.00</item>
    <item format="float" name="mtrl_low_ripple_focused_alpha" type="dimen">0.12</item>
    <item format="float" name="mtrl_low_ripple_hovered_alpha" type="dimen">0.04</item>
    <item format="float" name="mtrl_low_ripple_pressed_alpha" type="dimen">0.12</item>
    <dimen name="mtrl_min_touch_target_size">48dp</dimen>
    <dimen name="mtrl_navigation_bar_item_default_icon_size">24dp</dimen>
    <dimen name="mtrl_navigation_bar_item_default_margin">8dp</dimen>
    <dimen name="mtrl_navigation_elevation">0dp</dimen>
    <dimen name="mtrl_navigation_item_horizontal_padding">22dp</dimen>
    <dimen name="mtrl_navigation_item_icon_padding">14dp</dimen>
    <dimen name="mtrl_navigation_item_icon_size">24dp</dimen>
    <dimen name="mtrl_navigation_item_shape_horizontal_margin">8dp</dimen>
    <dimen name="mtrl_navigation_item_shape_vertical_margin">4dp</dimen>
    <dimen name="mtrl_navigation_rail_active_text_size" ns1:ignore="SpUsage">14dp</dimen>
    <dimen name="mtrl_navigation_rail_compact_width">56dp</dimen>
    <dimen name="mtrl_navigation_rail_default_width">72dp</dimen>
    <dimen name="mtrl_navigation_rail_elevation">8dp</dimen>
    <dimen name="mtrl_navigation_rail_icon_margin">14dp</dimen>
    <dimen name="mtrl_navigation_rail_icon_size">24dp</dimen>
    <dimen name="mtrl_navigation_rail_margin">8dp</dimen>
    <dimen name="mtrl_navigation_rail_text_bottom_margin">16dp</dimen>
    <dimen name="mtrl_navigation_rail_text_size" ns1:ignore="SpUsage">12dp</dimen>
    <dimen name="mtrl_progress_circular_inset">4dp</dimen>
    <dimen name="mtrl_progress_circular_inset_extra_small">2dp</dimen>
    <dimen name="mtrl_progress_circular_inset_medium">4dp</dimen>
    <dimen name="mtrl_progress_circular_inset_small">4dp</dimen>
    <dimen name="mtrl_progress_circular_radius">18dp</dimen>
    <dimen name="mtrl_progress_circular_size">40dp</dimen>
    <dimen name="mtrl_progress_circular_size_extra_small">20dp</dimen>
    <dimen name="mtrl_progress_circular_size_medium">40dp</dimen>
    <dimen name="mtrl_progress_circular_size_small">28dp</dimen>
    <dimen name="mtrl_progress_circular_track_thickness_extra_small">2.5dp</dimen>
    <dimen name="mtrl_progress_circular_track_thickness_medium">4dp</dimen>
    <dimen name="mtrl_progress_circular_track_thickness_small">3dp</dimen>
    <dimen name="mtrl_progress_indicator_full_rounded_corner_radius">2dp</dimen>
    <dimen name="mtrl_progress_track_thickness">4dp</dimen>
    <dimen name="mtrl_shape_corner_size_large_component">0dp</dimen>
    <dimen name="mtrl_shape_corner_size_medium_component">4dp</dimen>
    <dimen name="mtrl_shape_corner_size_small_component">4dp</dimen>
    <dimen name="mtrl_slider_halo_radius">24dp</dimen>
    <dimen name="mtrl_slider_label_padding">4dp</dimen>
    <dimen name="mtrl_slider_label_radius">13dp</dimen>
    <dimen name="mtrl_slider_label_square_side">26dp</dimen>
    <dimen name="mtrl_slider_thumb_elevation">1dp</dimen>
    <dimen name="mtrl_slider_thumb_radius">10dp</dimen>
    <dimen name="mtrl_slider_track_height">4dp</dimen>
    <dimen name="mtrl_slider_track_side_padding">16dp</dimen>
    <dimen name="mtrl_slider_track_top">24dp</dimen>
    <dimen name="mtrl_slider_widget_height">48dp</dimen>
    <item format="float" name="mtrl_snackbar_action_text_color_alpha" type="dimen">0.5</item>
    <dimen name="mtrl_snackbar_background_corner_radius">4dp</dimen>
    <item format="float" name="mtrl_snackbar_background_overlay_color_alpha" type="dimen">0.8</item>
    <dimen name="mtrl_snackbar_margin">8dp</dimen>
    <dimen name="mtrl_snackbar_message_margin_horizontal">8dp</dimen>
    <dimen name="mtrl_snackbar_padding_horizontal">8dp</dimen>
    <dimen name="mtrl_switch_thumb_elevation">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_medium">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_small">0dp</dimen>
    <dimen name="mtrl_textinput_box_label_cutout_padding">4dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_default">1dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_focused">2dp</dimen>
    <dimen name="mtrl_textinput_counter_margin_start">16dp</dimen>
    <dimen name="mtrl_textinput_end_icon_margin_start">4dp</dimen>
    <dimen name="mtrl_textinput_outline_box_expanded_padding">16dp</dimen>
    <dimen name="mtrl_textinput_start_icon_margin_end">4dp</dimen>
    <dimen name="mtrl_toolbar_default_height">56dp</dimen>
    <dimen name="mtrl_tooltip_arrowSize">14dp</dimen>
    <dimen name="mtrl_tooltip_cornerSize">4dp</dimen>
    <dimen name="mtrl_tooltip_minHeight">32dp</dimen>
    <dimen name="mtrl_tooltip_minWidth">32dp</dimen>
    <dimen name="mtrl_tooltip_padding">12dp</dimen>
    <dimen name="mtrl_transition_shared_axis_slide_distance">30dp</dimen>
    <dimen name="test_dimen">2dp</dimen>
    <dimen name="test_mtrl_calendar_day_cornerSize">52dp</dimen>
    <dimen name="test_navigation_bar_active_item_max_width">168dp</dimen>
    <dimen name="test_navigation_bar_active_item_min_width">96dp</dimen>
    <dimen name="test_navigation_bar_active_text_size">14sp</dimen>
    <dimen name="test_navigation_bar_elevation">8dp</dimen>
    <dimen name="test_navigation_bar_height">56dp</dimen>
    <dimen name="test_navigation_bar_icon_size">24dp</dimen>
    <dimen name="test_navigation_bar_item_max_width">96dp</dimen>
    <dimen name="test_navigation_bar_item_min_width">56dp</dimen>
    <dimen name="test_navigation_bar_label_padding">10dp</dimen>
    <dimen name="test_navigation_bar_shadow_height">1dp</dimen>
    <dimen name="test_navigation_bar_text_size">12sp</dimen>
    <item name="material_ic_keyboard_arrow_next_black_24dp" type="drawable">@drawable/material_ic_keyboard_arrow_right_black_24dp</item>
    <item name="material_ic_keyboard_arrow_previous_black_24dp" type="drawable">@drawable/material_ic_keyboard_arrow_left_black_24dp</item>
    <item name="material_clock_face" type="id"/>
    <item name="material_value_index" type="id"/>
    <item name="mtrl_anchor_parent" type="id"/>
    <item name="mtrl_card_checked_layer_id" type="id"/>
    <item name="mtrl_child_content_container" type="id"/>
    <item name="mtrl_internal_children_alpha_tag" type="id"/>
    <item name="mtrl_motion_snapshot_view" type="id"/>
    <item name="mtrl_view_tag_bottom_padding" type="id"/>
    <item name="navigation_bar_item_active_indicator_view" type="id"/>
    <item name="navigation_bar_item_icon_container" type="id"/>
    <item name="navigation_bar_item_icon_view" type="id"/>
    <item name="navigation_bar_item_labels_group" type="id"/>
    <item name="navigation_bar_item_large_label_view" type="id"/>
    <item name="navigation_bar_item_small_label_view" type="id"/>
    <item name="row_index_key" type="id"/>
    <item name="selection_type" type="id"/>
    <item name="snackbar_action" type="id"/>
    <item name="snackbar_text" type="id"/>
    <item name="text_input_error_icon" type="id"/>
    <item name="textinput_counter" type="id"/>
    <item name="textinput_error" type="id"/>
    <item name="textinput_helper_text" type="id"/>
    <item name="textinput_placeholder" type="id"/>
    <item name="textinput_prefix_text" type="id"/>
    <item name="textinput_suffix_text" type="id"/>
    <item name="view_offset_helper" type="id"/>
    <integer name="app_bar_elevation_anim_duration">150</integer>
    <integer name="bottom_sheet_slide_duration">150</integer>
    <integer name="design_snackbar_text_max_lines">2</integer>
    <integer name="design_tab_indicator_anim_duration_ms">300</integer>
    <integer name="hide_password_duration">320</integer>
    <integer name="m3_btn_anim_delay_ms">100</integer>
    <integer name="m3_btn_anim_duration_ms">100</integer>
    <integer name="m3_card_anim_delay_ms">75</integer>
    <integer name="m3_card_anim_duration_ms">120</integer>
    <integer name="m3_chip_anim_duration">100</integer>
    <integer name="m3_sys_motion_duration_long1">500</integer>
    <integer name="m3_sys_motion_duration_long2">600</integer>
    <integer name="m3_sys_motion_duration_medium1">300</integer>
    <integer name="m3_sys_motion_duration_medium2">400</integer>
    <integer name="m3_sys_motion_duration_short1">100</integer>
    <integer name="m3_sys_motion_duration_short2">200</integer>
    <integer name="m3_sys_motion_path">0</integer>
    <integer name="m3_sys_shape_large_corner_family">0</integer>
    <integer name="m3_sys_shape_medium_corner_family">0</integer>
    <integer name="m3_sys_shape_small_corner_family">0</integer>
    <integer name="material_motion_duration_long_1">300</integer>
    <integer name="material_motion_duration_long_2">350</integer>
    <integer name="material_motion_duration_medium_1">200</integer>
    <integer name="material_motion_duration_medium_2">250</integer>
    <integer name="material_motion_duration_short_1">75</integer>
    <integer name="material_motion_duration_short_2">150</integer>
    <integer name="material_motion_path" translatable="false">0</integer>
    <integer name="mtrl_badge_max_character_count">4</integer>
    <integer name="mtrl_btn_anim_delay_ms">100</integer>
    <integer name="mtrl_btn_anim_duration_ms">100</integer>
    <integer name="mtrl_calendar_header_orientation">1</integer>
    <integer name="mtrl_calendar_selection_text_lines">1</integer>
    <integer name="mtrl_calendar_year_selector_span">3</integer>
    <integer name="mtrl_card_anim_delay_ms">75</integer>
    <integer name="mtrl_card_anim_duration_ms">120</integer>
    <integer name="mtrl_chip_anim_duration">100</integer>
    <integer name="mtrl_tab_indicator_anim_duration_ms">250</integer>
    <integer name="mtrl_view_gone">2</integer>
    <integer name="mtrl_view_invisible">1</integer>
    <integer name="mtrl_view_visible">0</integer>
    <integer name="show_password_duration">200</integer>
    <plurals description="Plural form content description for number of new notifications [CHAR_LIMIT=NONE]" name="mtrl_badge_content_description">
    <item quantity="one"><ns2:g id="count">%d</ns2:g> new notification</item>
    <item quantity="other"><ns2:g id="count">%d</ns2:g> new notifications</item>
  </plurals>
    <string name="appbar_scrolling_view_behavior" translatable="false">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="bottom_sheet_behavior" translatable="false">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string description="A description of an action that a user of an accessibility service can perform to expand a bottomsheet halfway to its full height. [CHAR LIMIT=25]" name="bottomsheet_action_expand_halfway">Expand halfway</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern" translatable="false">%1$d/%2$d</string>
    <string name="chip_text">Chip text</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="error_icon_content_description">Error</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="fab_transformation_scrim_behavior" translatable="false">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior" translatable="false">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="hide_bottom_view_on_scroll_behavior" translatable="false">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string>
    <string description="Content description for an icon that appears in the title area of a dialog" name="icon_content_description">Dialog Icon</string>
    <string name="item_view_role_description">Tab</string>
    <string name="m3_ref_typeface_brand_display_regular" translatable="false">sans-serif</string>
    <string name="m3_ref_typeface_brand_medium" translatable="false">sans-serif-medium</string>
    <string name="m3_ref_typeface_brand_regular" translatable="false">sans-serif</string>
    <string name="m3_ref_typeface_plain_medium" translatable="false">sans-serif-medium</string>
    <string name="m3_ref_typeface_plain_regular" translatable="false">sans-serif</string>
    <string name="m3_sys_motion_easing_accelerated" translatable="false">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="m3_sys_motion_easing_decelerated" translatable="false">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="m3_sys_motion_easing_emphasized" translatable="false">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="m3_sys_motion_easing_linear" translatable="false">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="m3_sys_motion_easing_standard" translatable="false">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="m3_sys_typescale_body_large_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_body_medium_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_body_small_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_display_large_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_display_medium_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_display_small_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_headline_large_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_headline_medium_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_headline_small_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_label_large_font" translatable="false">sans-serif-medium</string>
    <string name="m3_sys_typescale_label_medium_font" translatable="false">sans-serif-medium</string>
    <string name="m3_sys_typescale_label_small_font" translatable="false">sans-serif-medium</string>
    <string name="m3_sys_typescale_title_large_font" translatable="false">sans-serif</string>
    <string name="m3_sys_typescale_title_medium_font" translatable="false">sans-serif-medium</string>
    <string name="m3_sys_typescale_title_small_font" translatable="false">sans-serif-medium</string>
    <string name="material_clock_display_divider" translatable="false">:</string>
    <string description="Description for the toggle to choose between AM and PM [CHAR_LIMIT=NONE] " name="material_clock_toggle_content_description">Select AM or PM</string>
    <string description="Description for button to switch to select the hour [CHAR_LIMIT=NONE]" name="material_hour_selection">Select hour</string>
    <string description="spoken suffix for an hour in the clock [CHAR_LIMIT=10]" name="material_hour_suffix"><ns2:g id="number">%1$s</ns2:g> o\'clock</string>
    <string description="Description for button to switch to select the minute [CHAR_LIMIT=NONE]" name="material_minute_selection">Select minutes</string>
    <string description="spoken suffix for an amount of minutes in the clock [CHAR_LIMIT=16]" name="material_minute_suffix"><ns2:g id="number">%1$s</ns2:g> minutes</string>
    <string name="material_motion_easing_accelerated" translatable="false">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_decelerated" translatable="false">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="material_motion_easing_emphasized" translatable="false">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="material_motion_easing_linear" translatable="false">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_standard" translatable="false">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string>
    <string description="Content description for the thumb with the maximum value in a Range slider. [CHAR_LIMIT=NONE]" name="material_slider_range_end">Range end, </string>
    <string description="Content description for the thumb with the minimum value in a Range slider. [CHAR_LIMIT=NONE]" name="material_slider_range_start">Range start, </string>
    <string description="Suffix for time in 12-hour standard, before noon. [CHAR_LIMIT=2]" name="material_timepicker_am">AM</string>
    <string name="material_timepicker_clock_mode_description">Switch to clock mode for the time input.</string>
    <string description="The label for a text field to select the hour on the time [CHAR_LIMIT=24]" name="material_timepicker_hour">Hour</string>
    <string description="The label for a text field to select the minute on the hour [CHAR_LIMIT=24]" name="material_timepicker_minute">Minute</string>
    <string description="Suffix for time in 12-hour standard, after noon. [CHAR_LIMIT=2]" name="material_timepicker_pm">PM</string>
    <string description="Title for the dialog with a time picker. [CHAR_LIMIT=32]" name="material_timepicker_select_time">Select time</string>
    <string name="material_timepicker_text_input_mode_description">Switch to text input mode for the time input.</string>
    <string description="Content description for new notification (no number count) [CHAR_LIMIT=NONE]" name="mtrl_badge_numberless_content_description">New notification</string>
    <string description="Content description for a close icon that lets the user remove content from the screen" name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string description="Plural form content description for when the number of new notifications exceeds a maximum count[CHAR_LIMIT=NONE]" name="mtrl_exceed_max_badge_number_content_description" ns1:ignore="PluralsCandidate">More than <ns2:g example="999" id="maximum number">%1$d</ns2:g> new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix" translatable="false"><ns2:g example="999" id="maximum number">%1$d</ns2:g><ns2:g example="+" id="suffix">%2$s</ns2:g></string>
    <string description="a11y string to indicate this button moves the calendar to the next month [CHAR_LIMIT=NONE]" name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string description="a11y string to indicate this button moves the calendar to the previous month [CHAR_LIMIT=NONE]" name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string description="a11y string read on selection change to indicate the new selection [CHAR_LIMIT=NONE]" name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string description="Button text to indicate that the widget will ignore the user&apos;s selection [CHAR_LIMIT=16]" name="mtrl_picker_cancel" translatable="false">@android:string/cancel</string>
    <string description="Button text to indicate that the widget will save the user&apos;s selection [CHAR_LIMIT=16]" name="mtrl_picker_confirm">OK</string>
    <string description="A single date [CHAR_LIMIT=60]" name="mtrl_picker_date_header_selected">%1$s</string>
    <string description="Indicates that the user must take the action of picking a date within the calendar [CHAR_LIMIT=60]" name="mtrl_picker_date_header_title">Select Date</string>
    <string description="Placeholder for a single date [CHAR_LIMIT=60]" name="mtrl_picker_date_header_unselected">Selected date</string>
    <string description="a11y string to indicate this is a header for a column of days for one day of the week (e.g., Monday) [CHAR_LIMIT=NONE]" name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string description="Indicates that the user entered date cannot be parsed because its format is wrong. [CHAR_LIMIT=36]" name="mtrl_picker_invalid_format">Invalid format.</string>
    <string description="Tells a user what an example valid entry looks like. [CHAR_LIMIT=18]" name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string description="Tells a user what format is expected for their date entry. [CHAR_LIMIT=18]" name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string description="Notifies the user that the two entered dates do not represent a valid range of dates [CHAR_LIMIT=36]" name="mtrl_picker_invalid_range">Invalid range.</string>
    <string description="a11y string that informs the user that tapping this button will switch the year [CHAR_LIMIT=NONE]" name="mtrl_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string description="Notifies the user that the entered date is outside the allowed range [CHAR_LIMIT=36]" name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string description="Placeholders for two dates separated by a dash representing a range where end date has been selected [CHAR_LIMIT=60]" name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string description="Placeholders for two dates separated by a dash representing a range where start date has been selected [CHAR_LIMIT=60]" name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string description="Two dates separated by a dash representing a range [CHAR_LIMIT=60]" name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string description="Indicates that the user must take the action of picking dates within the calendar to form a range [CHAR_LIMIT=60]" name="mtrl_picker_range_header_title">Select Range</string>
    <string description="Placeholders for two dates separated by a dash representing a range [CHAR_LIMIT=60]" name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string description="Confirms the selection [CHAR_LIMIT=12]" name="mtrl_picker_save">Save</string>
    <string description="Label for a single date selected by the user [CHAR_LIMIT=60]" name="mtrl_picker_text_input_date_hint">Date</string>
    <string description="Label for the end date in a range selected by the user [CHAR_LIMIT=60]" name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string description="Label for the start date in a range selected by the user [CHAR_LIMIT=60]" name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string description="A 1 character abbreviation for day. It will be part of a string such as dd/mm/yyyy or mm/dd/yyyy or y.mm.dd. [CHAR_LIMIT=2]" name="mtrl_picker_text_input_day_abbr">d</string>
    <string description="A 1 character abbreviation for month. It will be part of a string such as dd/mm/yyyy or mm/dd/yyyy or y.mm.dd. [CHAR_LIMIT=2]" name="mtrl_picker_text_input_month_abbr">m</string>
    <string description="A 1 character abbreviation for year. It will be part of a string such as dd/mm/yyyy or mm/dd/yyyy or y.mm.dd. [CHAR_LIMIT=2]" name="mtrl_picker_text_input_year_abbr">y</string>
    <string description="a11y string to indicate this button changes the input mode to a calendar [CHAR_LIMIT=NONE]" name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string description="a11y string to indicate this button switches the user to choosing a day [CHAR_LIMIT=NONE]" name="mtrl_picker_toggle_to_day_selection">Tap to switch to selecting a day</string>
    <string description="a11y string to indicate this button changes the input mode to a text field [CHAR_LIMIT=NONE]" name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string description="a11y string to indicate this button switches the user to choosing a year [CHAR_LIMIT=NONE]" name="mtrl_picker_toggle_to_year_selection">Tap to switch to selecting a year</string>
    <string description="Button text to indicate that the widget will save the user&apos;s selection [CHAR_LIMIT=16]" name="mtrl_timepicker_confirm">OK</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye" translatable="false">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through" translatable="false">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible" translatable="false">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through" translatable="false">M3.27,4.27 L19.74,20.74</string>
    <style name="AndroidThemeColorAccentYellow" ns1:ignore="NewApi">
    <item name="android:colorAccent">#FFFFFF00</item>
  </style>
    <style name="Animation.Design.BottomSheetDialog" parent="Animation.AppCompat.Dialog">
    <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in</item>
    <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
  </style>
    <style name="Animation.MaterialComponents.BottomSheetDialog" parent="Animation.AppCompat.Dialog">
    <item name="android:windowEnterAnimation">@anim/mtrl_bottom_sheet_slide_in</item>
    <item name="android:windowExitAnimation">@anim/mtrl_bottom_sheet_slide_out</item>
  </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" parent="android:Widget">
    <item name="android:layout_width">32dip</item>
    <item name="android:layout_height">32dip</item>
    <item name="android:scaleType">fitCenter</item>
    <item name="android:src">@null</item>
    <item name="android:contentDescription">@string/icon_content_description</item>
  </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" parent="android:Widget">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
    <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
    <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
  </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" parent="RtlOverlay.DialogWindowTitle.AppCompat">
    <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="android:textColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:ellipsize">end</item>
    <item name="android:singleLine">true</item>
  </style>
    <style name="Base.TextAppearance.Material3.LabelLarge" parent="TextAppearance.MaterialComponents.Body2">
    <item name="fontFamily">@string/m3_sys_typescale_label_large_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_label_large_font</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_label_large_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_label_large_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_label_large_letter_spacing</item>
  </style>
    <style name="Base.TextAppearance.Material3.LabelMedium" parent="TextAppearance.MaterialComponents.Caption">
    <item name="fontFamily">@string/m3_sys_typescale_label_medium_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_label_medium_font</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_label_medium_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_label_medium_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_label_medium_letter_spacing</item>
  </style>
    <style name="Base.TextAppearance.Material3.LabelSmall" parent="TextAppearance.MaterialComponents.Caption">
    <item name="fontFamily">@string/m3_sys_typescale_label_small_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_label_small_font</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_label_small_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_label_small_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_label_small_letter_spacing</item>
  </style>
    <style name="Base.TextAppearance.Material3.TitleMedium" parent="TextAppearance.MaterialComponents.Subtitle1">
    <item name="fontFamily">@string/m3_sys_typescale_title_medium_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_title_medium_font</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_title_medium_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_title_medium_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_title_medium_letter_spacing</item>
  </style>
    <style name="Base.TextAppearance.Material3.TitleSmall" parent="TextAppearance.MaterialComponents.Subtitle2">
    <item name="fontFamily">@string/m3_sys_typescale_title_small_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_title_small_font</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_title_small_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_title_small_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_title_small_letter_spacing</item>
  </style>
    <style name="Base.TextAppearance.MaterialComponents.Badge" parent="TextAppearance.AppCompat">
    <!-- Fake Roboto Medium. -->
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">@dimen/mtrl_badge_text_size</item>
    <item name="android:letterSpacing">0.0892857143</item>
    <item name="android:textColor">?attr/colorOnError</item>
  </style>
    <style name="Base.TextAppearance.MaterialComponents.Button" parent="TextAppearance.AppCompat.Button">
    <!-- Fake Roboto Medium. -->
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">true</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.0892857143</item>
  </style>
    <style name="Base.TextAppearance.MaterialComponents.Headline6" parent="TextAppearance.AppCompat.Title">
    <!-- Fake Roboto Medium. -->
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">20sp</item>
    <item name="android:letterSpacing">0.0125</item>
  </style>
    <style name="Base.TextAppearance.MaterialComponents.Subtitle2" parent="TextAppearance.AppCompat.Subhead">
    <!-- Fake Roboto Medium. -->
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.00714285714</item>
  </style>
    <style name="Base.Theme.Material3.Dark" parent="Base.V14.Theme.Material3.Dark"/>
    <style name="Base.Theme.Material3.Dark.BottomSheetDialog" parent="Base.V14.Theme.Material3.Dark.BottomSheetDialog"/>
    <style name="Base.Theme.Material3.Dark.Dialog" parent="Base.V14.Theme.Material3.Dark.Dialog"/>
    <style name="Base.Theme.Material3.Light" parent="Base.V14.Theme.Material3.Light"/>
    <style name="Base.Theme.Material3.Light.BottomSheetDialog" parent="Base.V14.Theme.Material3.Light.BottomSheetDialog"/>
    <style name="Base.Theme.Material3.Light.Dialog" parent="Base.V14.Theme.Material3.Light.Dialog"/>
    <style name="Base.Theme.MaterialComponents" parent="Base.V14.Theme.MaterialComponents"/>
    <style name="Base.Theme.MaterialComponents.Bridge" parent="Base.V14.Theme.MaterialComponents.Bridge"/>
    <style name="Base.Theme.MaterialComponents.CompactMenu" parent="">
    <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
    <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="Base.V14.Theme.MaterialComponents.Dialog"/>
    <style name="Base.Theme.MaterialComponents.Dialog.Alert">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog.Bridge" parent="Base.V14.Theme.MaterialComponents.Dialog.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Dialog.FixedSize">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog.MinWidth">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="Theme.MaterialComponents"/>
    <style name="Base.Theme.MaterialComponents.Light" parent="Base.V14.Theme.MaterialComponents.Light"/>
    <style name="Base.Theme.MaterialComponents.Light.Bridge" parent="Base.V14.Theme.MaterialComponents.Light.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar" parent="Base.Theme.MaterialComponents.Light">
    <item name="actionBarWidgetTheme">@null</item>
    <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>

    <!-- Panel attributes -->
    <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

    <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
    <item name="colorPrimary">@color/primary_material_dark</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="Base.V14.Theme.MaterialComponents.Light.Dialog"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Alert">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" parent="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="Theme.MaterialComponents.Light"/>
    <style name="Base.ThemeOverlay.Material3.AutoCompleteTextView" parent="ThemeOverlay.MaterialComponents.AutoCompleteTextView">
    <item name="colorControlActivated">?attr/colorPrimary</item>
  </style>
    <style name="Base.ThemeOverlay.Material3.BottomSheetDialog" parent="Base.V14.ThemeOverlay.Material3.BottomSheetDialog"/>
    <style name="Base.ThemeOverlay.Material3.Dialog" parent="Base.ThemeOverlay.MaterialComponents.Dialog">
    <item name="materialButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="Base.ThemeOverlay.Material3.TextInputEditText" parent="ThemeOverlay.MaterialComponents.TextInputEditText">
    <item name="colorControlActivated">?attr/colorPrimary</item>
  </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="Base.V14.ThemeOverlay.MaterialComponents.Dialog"/>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert"/>
    <style name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog"/>
    <style name="Base.V14.Theme.Material3.Dark" parent="Theme.MaterialComponents">
    <item name="isMaterial3Theme">true</item>

    <!-- Color palettes -->
    <item name="colorPrimary">@color/m3_sys_color_dark_primary</item>
    <item name="colorPrimaryDark">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/m3_sys_color_dark_on_primary</item>
    <item name="colorPrimaryInverse">@color/m3_sys_color_dark_inverse_primary</item>
    <item name="colorPrimaryContainer">@color/m3_sys_color_dark_primary_container</item>
    <item name="colorOnPrimaryContainer">@color/m3_sys_color_dark_on_primary_container</item>
    <item name="colorSecondary">@color/m3_sys_color_dark_secondary</item>
    <item name="colorOnSecondary">@color/m3_sys_color_dark_on_secondary</item>
    <item name="colorSecondaryContainer">@color/m3_sys_color_dark_secondary_container</item>
    <item name="colorOnSecondaryContainer">@color/m3_sys_color_dark_on_secondary_container</item>
    <item name="colorTertiary">@color/m3_sys_color_dark_tertiary</item>
    <item name="colorOnTertiary">@color/m3_sys_color_dark_on_tertiary</item>
    <item name="colorTertiaryContainer">@color/m3_sys_color_dark_tertiary_container</item>
    <item name="colorOnTertiaryContainer">@color/m3_sys_color_dark_on_tertiary_container</item>
    <item name="android:colorBackground">@color/m3_sys_color_dark_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_dark_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_dark_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_dark_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_dark_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_dark_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_dark_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_dark_inverse_on_surface</item>
    <item name="colorOutline">@color/m3_sys_color_dark_outline</item>
    <item name="colorError">@color/m3_sys_color_dark_error</item>
    <item name="colorOnError">@color/m3_sys_color_dark_on_error</item>
    <item name="colorErrorContainer">@color/m3_sys_color_dark_error_container</item>
    <item name="colorOnErrorContainer">@color/m3_sys_color_dark_on_error_container</item>

    <!-- Default Framework Text Colors. -->
    <item name="android:textColorPrimary">@color/m3_dark_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_dark_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_dark_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_dark_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_highlighted_text</item>
    <item name="android:textColorLink">?attr/colorPrimary</item>
    <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_dark_default_color_primary_text</item>

    <!-- Dialog themes. -->
    <item name="alertDialogTheme">@style/ThemeOverlay.Material3.Dialog.Alert</item>
    <item name="bottomSheetDialogTheme">@style/ThemeOverlay.Material3.BottomSheetDialog</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.Material3.MaterialAlertDialog</item>

    <!-- Picker styles and themes. -->
    <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar</item>
    <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarTheme">@style/ThemeOverlay.Material3.MaterialCalendar</item>
    <item name="materialTimePickerTheme">@style/ThemeOverlay.Material3.MaterialTimePicker</item>

    <!-- Widget styles. -->
    <item name="actionBarStyle">@style/Widget.Material3.ActionBar.Solid</item>
    <item name="actionModeStyle">@style/Widget.Material3.ActionMode</item>
    <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout</item>
    <item name="badgeStyle">@style/Widget.Material3.Badge</item>
    <item name="borderlessButtonStyle">@style/Widget.Material3.Button.TextButton</item>
    <item name="bottomAppBarStyle">@style/Widget.Material3.BottomAppBar</item>
    <item name="bottomNavigationStyle">@style/Widget.Material3.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
    <item name="checkboxStyle">@style/Widget.Material3.CompoundButton.CheckBox</item>
    <item name="chipStyle">@style/Widget.Material3.Chip.Assist</item>
    <item name="chipGroupStyle">@style/Widget.Material3.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.Material3.Chip.Input</item>
    <item name="circularProgressIndicatorStyle">@style/Widget.Material3.CircularProgressIndicator</item>
    <item name="collapsingToolbarLayoutStyle">@style/Widget.Material3.CollapsingToolbar</item>
    <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.Material3.CollapsingToolbar.Medium</item>
    <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.Material3.CollapsingToolbar.Large</item>
    <item name="drawerLayoutStyle">@style/Widget.Material3.DrawerLayout</item>
    <item name="extendedFloatingActionButtonStyle">?attr/extendedFloatingActionButtonPrimaryStyle</item>
    <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary</item>
    <item name="extendedFloatingActionButtonSecondaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary</item>
    <item name="extendedFloatingActionButtonTertiaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary</item>
    <item name="extendedFloatingActionButtonSurfaceStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface</item>
    <item name="floatingActionButtonStyle">?attr/floatingActionButtonPrimaryStyle</item>
    <item name="floatingActionButtonPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Primary</item>
    <item name="floatingActionButtonSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Secondary</item>
    <item name="floatingActionButtonTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Tertiary</item>
    <item name="floatingActionButtonSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Surface</item>
    <item name="floatingActionButtonLargeStyle">?attr/floatingActionButtonLargePrimaryStyle</item>
    <item name="floatingActionButtonLargePrimaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Primary</item>
    <item name="floatingActionButtonLargeSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Secondary</item>
    <item name="floatingActionButtonLargeTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Tertiary</item>
    <item name="floatingActionButtonLargeSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Large.Surface</item>
    <item name="linearProgressIndicatorStyle">@style/Widget.Material3.LinearProgressIndicator</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.Material3.Button.OutlinedButton</item>
    <item name="materialButtonStyle">@style/Widget.Material3.Button</item>
    <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
    <item name="materialCardViewOutlinedStyle">@style/Widget.Material3.CardView.Outlined</item>
    <item name="materialCardViewFilledStyle">@style/Widget.Material3.CardView.Filled</item>
    <item name="materialCardViewElevatedStyle">@style/Widget.Material3.CardView.Elevated</item>
    <item name="navigationRailStyle">@style/Widget.Material3.NavigationRailView</item>
    <item name="navigationViewStyle">@style/Widget.Material3.NavigationView</item>
    <item name="radioButtonStyle">@style/Widget.Material3.CompoundButton.RadioButton</item>
    <item name="sliderStyle">@style/Widget.Material3.Slider</item>
    <item name="snackbarStyle">@style/Widget.Material3.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.Material3.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.Material3.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.Material3.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.Material3.TabLayout</item>
    <item name="tabSecondaryStyle">@style/Widget.Material3.TabLayout.Secondary</item>
    <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
    <item name="textInputOutlinedStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox</item>
    <item name="textInputFilledStyle">@style/Widget.Material3.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedDenseStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense</item>
    <item name="textInputFilledDenseStyle">@style/Widget.Material3.TextInputLayout.FilledBox.Dense</item>
    <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
    <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
    <item name="toolbarStyle">@style/Widget.Material3.Toolbar</item>
    <item name="toolbarSurfaceStyle">@style/Widget.Material3.Toolbar.Surface</item>

    <!-- Top App Bars. -->
    <item name="actionBarSize">@dimen/m3_appbar_size_compact</item>
    <item name="collapsingToolbarLayoutMediumSize">@dimen/m3_appbar_size_medium</item>
    <item name="collapsingToolbarLayoutLargeSize">@dimen/m3_appbar_size_large</item>

    <!-- Popup Menu styles. -->
    <item name="popupMenuBackground">@drawable/m3_popupmenu_background_overlay</item>
    <item name="listPopupWindowStyle">@style/Widget.Material3.PopupMenu.ListPopupWindow</item>
    <item name="popupMenuStyle">@style/Widget.Material3.PopupMenu</item>
    <item name="actionOverflowMenuStyle">@style/Widget.Material3.PopupMenu.Overflow</item>

    <!-- Divider styles. -->
    <item name="materialDividerStyle">@style/Widget.Material3.MaterialDivider</item>
    <item name="materialDividerHeavyStyle">@style/Widget.Material3.MaterialDivider.Heavy</item>

    <!-- Type styles. -->
    <item name="textAppearanceDisplayLarge">@style/TextAppearance.Material3.DisplayLarge</item>
    <item name="textAppearanceDisplayMedium">@style/TextAppearance.Material3.DisplayMedium</item>
    <item name="textAppearanceDisplaySmall">@style/TextAppearance.Material3.DisplaySmall</item>
    <item name="textAppearanceHeadlineLarge">@style/TextAppearance.Material3.HeadlineLarge</item>
    <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
    <item name="textAppearanceHeadlineSmall">@style/TextAppearance.Material3.HeadlineSmall</item>
    <item name="textAppearanceTitleLarge">@style/TextAppearance.Material3.TitleLarge</item>
    <item name="textAppearanceTitleMedium">@style/TextAppearance.Material3.TitleMedium</item>
    <item name="textAppearanceTitleSmall">@style/TextAppearance.Material3.TitleSmall</item>
    <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
    <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
    <item name="textAppearanceBodySmall">@style/TextAppearance.Material3.BodySmall</item>
    <item name="textAppearanceLabelLarge">@style/TextAppearance.Material3.LabelLarge</item>
    <item name="textAppearanceLabelMedium">@style/TextAppearance.Material3.LabelMedium</item>
    <item name="textAppearanceLabelSmall">@style/TextAppearance.Material3.LabelSmall</item>

    <!-- Default Framework Text styles. -->
    <item name="android:textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
    <item name="android:textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
    <item name="android:textAppearanceListItemSecondary" ns1:targetApi="21">?attr/textAppearanceBodyMedium</item>
    <item name="textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
    <item name="textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
    <item name="textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>

    <!-- Menu type styles. -->
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceBodyLarge</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceBodyLarge</item>

    <!-- TODO: Populate font families here -->

    <!-- Shape styles. -->
    <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.Material3.SmallComponent</item>
    <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.Material3.MediumComponent</item>
    <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.Material3.LargeComponent</item>

    <!-- Motion-->
    <item name="motionEasingStandard">@string/m3_sys_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/m3_sys_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/m3_sys_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/m3_sys_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/m3_sys_motion_easing_linear</item>
    <item name="motionDurationShort1">@integer/m3_sys_motion_duration_short1</item>
    <item name="motionDurationShort2">@integer/m3_sys_motion_duration_short2</item>
    <item name="motionDurationMedium1">@integer/m3_sys_motion_duration_medium1</item>
    <item name="motionDurationMedium2">@integer/m3_sys_motion_duration_medium2</item>
    <item name="motionDurationLong1">@integer/m3_sys_motion_duration_long1</item>
    <item name="motionDurationLong2">@integer/m3_sys_motion_duration_long2</item>
    <item name="motionPath">@integer/m3_sys_motion_path</item>

    <!-- Elevation overlays. -->
    <item name="elevationOverlayEnabled">true</item>
    <item name="elevationOverlayColor">?attr/colorPrimary</item>

    <!-- Theme overlays. -->
    <item name="dynamicColorThemeOverlay">@style/ThemeOverlay.Material3.DynamicColors.Dark</item>
  </style>
    <style name="Base.V14.Theme.Material3.Dark.BottomSheetDialog" parent="Theme.Material3.Dark.Dialog">
    <item name="android:windowIsFloating">false</item>
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Material3.BottomSheet.Modal</item>
  </style>
    <style name="Base.V14.Theme.Material3.Dark.Dialog" parent="Theme.MaterialComponents.Dialog">
    <item name="isMaterial3Theme">true</item>

    <!-- Color palettes -->
    <item name="colorPrimary">@color/m3_sys_color_dark_primary</item>
    <item name="colorPrimaryDark">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/m3_sys_color_dark_on_primary</item>
    <item name="colorPrimaryInverse">@color/m3_sys_color_dark_inverse_primary</item>
    <item name="colorPrimaryContainer">@color/m3_sys_color_dark_primary_container</item>
    <item name="colorOnPrimaryContainer">@color/m3_sys_color_dark_on_primary_container</item>
    <item name="colorSecondary">@color/m3_sys_color_dark_secondary</item>
    <item name="colorOnSecondary">@color/m3_sys_color_dark_on_secondary</item>
    <item name="colorSecondaryContainer">@color/m3_sys_color_dark_secondary_container</item>
    <item name="colorOnSecondaryContainer">@color/m3_sys_color_dark_on_secondary_container</item>
    <item name="colorTertiary">@color/m3_sys_color_dark_tertiary</item>
    <item name="colorOnTertiary">@color/m3_sys_color_dark_on_tertiary</item>
    <item name="colorTertiaryContainer">@color/m3_sys_color_dark_tertiary_container</item>
    <item name="colorOnTertiaryContainer">@color/m3_sys_color_dark_on_tertiary_container</item>
    <item name="android:colorBackground">@color/m3_sys_color_dark_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_dark_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_dark_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_dark_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_dark_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_dark_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_dark_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_dark_inverse_on_surface</item>
    <item name="colorOutline">@color/m3_sys_color_dark_outline</item>
    <item name="colorError">@color/m3_sys_color_dark_error</item>
    <item name="colorOnError">@color/m3_sys_color_dark_on_error</item>
    <item name="colorErrorContainer">@color/m3_sys_color_dark_error_container</item>
    <item name="colorOnErrorContainer">@color/m3_sys_color_dark_on_error_container</item>

    <!-- Default Framework Text Colors. -->
    <item name="android:textColorPrimary">@color/m3_dark_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_dark_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_dark_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_dark_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_highlighted_text</item>
    <item name="android:textColorLink">?attr/colorPrimary</item>
    <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_dark_default_color_primary_text</item>

    <!-- Widget styles. -->
    <item name="actionBarStyle">@style/Widget.Material3.ActionBar.Solid</item>
    <item name="actionModeStyle">@style/Widget.Material3.ActionMode</item>
    <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout</item>
    <item name="badgeStyle">@style/Widget.Material3.Badge</item>
    <item name="borderlessButtonStyle">@style/Widget.Material3.Button.TextButton</item>
    <item name="bottomAppBarStyle">@style/Widget.Material3.BottomAppBar</item>
    <item name="bottomNavigationStyle">@style/Widget.Material3.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
    <item name="checkboxStyle">@style/Widget.Material3.CompoundButton.CheckBox</item>
    <item name="chipStyle">@style/Widget.Material3.Chip.Assist</item>
    <item name="chipGroupStyle">@style/Widget.Material3.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.Material3.Chip.Input</item>
    <item name="circularProgressIndicatorStyle">@style/Widget.Material3.CircularProgressIndicator</item>
    <item name="collapsingToolbarLayoutStyle">@style/Widget.Material3.CollapsingToolbar</item>
    <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.Material3.CollapsingToolbar.Medium</item>
    <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.Material3.CollapsingToolbar.Large</item>
    <item name="drawerLayoutStyle">@style/Widget.Material3.DrawerLayout</item>
    <item name="extendedFloatingActionButtonStyle">?attr/extendedFloatingActionButtonPrimaryStyle</item>
    <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary</item>
    <item name="extendedFloatingActionButtonSecondaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary</item>
    <item name="extendedFloatingActionButtonTertiaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary</item>
    <item name="extendedFloatingActionButtonSurfaceStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface</item>
    <item name="floatingActionButtonStyle">?attr/floatingActionButtonPrimaryStyle</item>
    <item name="floatingActionButtonPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Primary</item>
    <item name="floatingActionButtonSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Secondary</item>
    <item name="floatingActionButtonTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Tertiary</item>
    <item name="floatingActionButtonSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Surface</item>
    <item name="floatingActionButtonLargeStyle">?attr/floatingActionButtonLargePrimaryStyle</item>
    <item name="floatingActionButtonLargePrimaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Primary</item>
    <item name="floatingActionButtonLargeSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Secondary</item>
    <item name="floatingActionButtonLargeTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Tertiary</item>
    <item name="floatingActionButtonLargeSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Large.Surface</item>
    <item name="linearProgressIndicatorStyle">@style/Widget.Material3.LinearProgressIndicator</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.Material3.Button.OutlinedButton</item>
    <item name="materialButtonStyle">@style/Widget.Material3.Button</item>
    <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
    <item name="materialCardViewOutlinedStyle">@style/Widget.Material3.CardView.Outlined</item>
    <item name="materialCardViewFilledStyle">@style/Widget.Material3.CardView.Filled</item>
    <item name="materialCardViewElevatedStyle">@style/Widget.Material3.CardView.Elevated</item>
    <item name="navigationRailStyle">@style/Widget.Material3.NavigationRailView</item>
    <item name="navigationViewStyle">@style/Widget.Material3.NavigationView</item>
    <item name="radioButtonStyle">@style/Widget.Material3.CompoundButton.RadioButton</item>
    <item name="sliderStyle">@style/Widget.Material3.Slider</item>
    <item name="snackbarStyle">@style/Widget.Material3.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.Material3.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.Material3.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.Material3.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.Material3.TabLayout</item>
    <item name="tabSecondaryStyle">@style/Widget.Material3.TabLayout.Secondary</item>
    <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
    <item name="textInputOutlinedStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox</item>
    <item name="textInputFilledStyle">@style/Widget.Material3.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedDenseStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense</item>
    <item name="textInputFilledDenseStyle">@style/Widget.Material3.TextInputLayout.FilledBox.Dense</item>
    <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
    <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
    <item name="toolbarStyle">@style/Widget.Material3.Toolbar</item>
    <item name="toolbarSurfaceStyle">@style/Widget.Material3.Toolbar.Surface</item>

    <!-- Top App Bars. -->
    <item name="actionBarSize">@dimen/m3_appbar_size_compact</item>
    <item name="collapsingToolbarLayoutMediumSize">@dimen/m3_appbar_size_medium</item>
    <item name="collapsingToolbarLayoutLargeSize">@dimen/m3_appbar_size_large</item>

    <!-- Popup Menu styles. -->
    <item name="popupMenuBackground">@drawable/m3_popupmenu_background_overlay</item>
    <item name="listPopupWindowStyle">@style/Widget.Material3.PopupMenu.ListPopupWindow</item>
    <item name="popupMenuStyle">@style/Widget.Material3.PopupMenu</item>
    <item name="actionOverflowMenuStyle">@style/Widget.Material3.PopupMenu.Overflow</item>

    <!-- Divider styles. -->
    <item name="materialDividerStyle">@style/Widget.Material3.MaterialDivider</item>
    <item name="materialDividerHeavyStyle">@style/Widget.Material3.MaterialDivider.Heavy</item>

    <!-- Type styles. -->
    <item name="textAppearanceDisplayLarge">@style/TextAppearance.Material3.DisplayLarge</item>
    <item name="textAppearanceDisplayMedium">@style/TextAppearance.Material3.DisplayMedium</item>
    <item name="textAppearanceDisplaySmall">@style/TextAppearance.Material3.DisplaySmall</item>
    <item name="textAppearanceHeadlineLarge">@style/TextAppearance.Material3.HeadlineLarge</item>
    <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
    <item name="textAppearanceHeadlineSmall">@style/TextAppearance.Material3.HeadlineSmall</item>
    <item name="textAppearanceTitleLarge">@style/TextAppearance.Material3.TitleLarge</item>
    <item name="textAppearanceTitleMedium">@style/TextAppearance.Material3.TitleMedium</item>
    <item name="textAppearanceTitleSmall">@style/TextAppearance.Material3.TitleSmall</item>
    <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
    <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
    <item name="textAppearanceBodySmall">@style/TextAppearance.Material3.BodySmall</item>
    <item name="textAppearanceLabelLarge">@style/TextAppearance.Material3.LabelLarge</item>
    <item name="textAppearanceLabelMedium">@style/TextAppearance.Material3.LabelMedium</item>
    <item name="textAppearanceLabelSmall">@style/TextAppearance.Material3.LabelSmall</item>

    <!-- Default Framework Text styles. -->
    <item name="android:textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
    <item name="android:textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
    <item name="android:textAppearanceListItemSecondary" ns1:targetApi="21">?attr/textAppearanceBodyMedium</item>
    <item name="textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
    <item name="textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
    <item name="textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>

    <!-- Menu type styles. -->
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceBodyLarge</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceBodyLarge</item>

    <!-- TODO: Populate font families here -->

    <!-- Shape styles. -->
    <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.Material3.SmallComponent</item>
    <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.Material3.MediumComponent</item>
    <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.Material3.LargeComponent</item>

    <!-- Motion-->
    <item name="motionEasingStandard">@string/m3_sys_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/m3_sys_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/m3_sys_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/m3_sys_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/m3_sys_motion_easing_linear</item>
    <item name="motionDurationShort1">@integer/m3_sys_motion_duration_short1</item>
    <item name="motionDurationShort2">@integer/m3_sys_motion_duration_short2</item>
    <item name="motionDurationMedium1">@integer/m3_sys_motion_duration_medium1</item>
    <item name="motionDurationMedium2">@integer/m3_sys_motion_duration_medium2</item>
    <item name="motionDurationLong1">@integer/m3_sys_motion_duration_long1</item>
    <item name="motionDurationLong2">@integer/m3_sys_motion_duration_long2</item>
    <item name="motionPath">@integer/m3_sys_motion_path</item>

    <!-- Elevation overlays. -->
    <item name="elevationOverlayEnabled">true</item>
    <item name="elevationOverlayColor">?attr/colorPrimary</item>

    <!-- Dialog themes. -->
    <item name="alertDialogTheme">@style/ThemeOverlay.Material3.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.Material3.MaterialAlertDialog</item>

    <!-- Theme overlays. -->
    <item name="dynamicColorThemeOverlay">@style/ThemeOverlay.Material3.DynamicColors.Dark</item>
  </style>
    <style name="Base.V14.Theme.Material3.Light" parent="Theme.MaterialComponents.Light">
    <item name="isMaterial3Theme">true</item>

    <!-- Color palettes -->
    <item name="colorPrimary">@color/m3_sys_color_light_primary</item>
    <item name="colorPrimaryDark">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/m3_sys_color_light_on_primary</item>
    <item name="colorPrimaryInverse">@color/m3_sys_color_light_inverse_primary</item>
    <item name="colorPrimaryContainer">@color/m3_sys_color_light_primary_container</item>
    <item name="colorOnPrimaryContainer">@color/m3_sys_color_light_on_primary_container</item>
    <item name="colorSecondary">@color/m3_sys_color_light_secondary</item>
    <item name="colorOnSecondary">@color/m3_sys_color_light_on_secondary</item>
    <item name="colorSecondaryContainer">@color/m3_sys_color_light_secondary_container</item>
    <item name="colorOnSecondaryContainer">@color/m3_sys_color_light_on_secondary_container</item>
    <item name="colorTertiary">@color/m3_sys_color_light_tertiary</item>
    <item name="colorOnTertiary">@color/m3_sys_color_light_on_tertiary</item>
    <item name="colorTertiaryContainer">@color/m3_sys_color_light_tertiary_container</item>
    <item name="colorOnTertiaryContainer">@color/m3_sys_color_light_on_tertiary_container</item>
    <item name="android:colorBackground">@color/m3_sys_color_light_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_light_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_light_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_light_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_light_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_light_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_light_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_light_inverse_on_surface</item>
    <item name="colorOutline">@color/m3_sys_color_light_outline</item>
    <item name="colorError">@color/m3_sys_color_light_error</item>
    <item name="colorOnError">@color/m3_sys_color_light_on_error</item>
    <item name="colorErrorContainer">@color/m3_sys_color_light_error_container</item>
    <item name="colorOnErrorContainer">@color/m3_sys_color_light_on_error_container</item>

    <!-- Default Framework Text Colors. -->
    <item name="android:textColorPrimary">@color/m3_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_dark_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dark_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_dark_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_dark_highlighted_text</item>
    <item name="android:textColorLink">?attr/colorPrimary</item>
    <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_default_color_primary_text</item>

    <!-- Dialog themes. -->
    <item name="alertDialogTheme">@style/ThemeOverlay.Material3.Dialog.Alert</item>
    <item name="bottomSheetDialogTheme">@style/ThemeOverlay.Material3.BottomSheetDialog</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.Material3.MaterialAlertDialog</item>

    <!-- Picker styles and themes. -->
    <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar</item>
    <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarTheme">@style/ThemeOverlay.Material3.MaterialCalendar</item>
    <item name="materialTimePickerTheme">@style/ThemeOverlay.Material3.MaterialTimePicker</item>

    <!-- Widget styles. -->
    <item name="actionBarStyle">@style/Widget.Material3.Light.ActionBar.Solid</item>
    <item name="actionModeStyle">@style/Widget.Material3.ActionMode</item>
    <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout</item>
    <item name="badgeStyle">@style/Widget.Material3.Badge</item>
    <item name="borderlessButtonStyle">@style/Widget.Material3.Button.TextButton</item>
    <item name="bottomAppBarStyle">@style/Widget.Material3.BottomAppBar</item>
    <item name="bottomNavigationStyle">@style/Widget.Material3.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
    <item name="checkboxStyle">@style/Widget.Material3.CompoundButton.CheckBox</item>
    <item name="chipStyle">@style/Widget.Material3.Chip.Assist</item>
    <item name="chipGroupStyle">@style/Widget.Material3.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.Material3.Chip.Input</item>
    <item name="circularProgressIndicatorStyle">@style/Widget.Material3.CircularProgressIndicator</item>
    <item name="collapsingToolbarLayoutStyle">@style/Widget.Material3.CollapsingToolbar</item>
    <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.Material3.CollapsingToolbar.Medium</item>
    <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.Material3.CollapsingToolbar.Large</item>
    <item name="drawerLayoutStyle">@style/Widget.Material3.DrawerLayout</item>
    <item name="extendedFloatingActionButtonStyle">?attr/extendedFloatingActionButtonPrimaryStyle</item>
    <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary</item>
    <item name="extendedFloatingActionButtonSecondaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary</item>
    <item name="extendedFloatingActionButtonTertiaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary</item>
    <item name="extendedFloatingActionButtonSurfaceStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface</item>
    <item name="floatingActionButtonStyle">?attr/floatingActionButtonPrimaryStyle</item>
    <item name="floatingActionButtonPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Primary</item>
    <item name="floatingActionButtonSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Secondary</item>
    <item name="floatingActionButtonTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Tertiary</item>
    <item name="floatingActionButtonSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Surface</item>
    <item name="floatingActionButtonLargeStyle">?attr/floatingActionButtonLargePrimaryStyle</item>
    <item name="floatingActionButtonLargePrimaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Primary</item>
    <item name="floatingActionButtonLargeSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Secondary</item>
    <item name="floatingActionButtonLargeTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Tertiary</item>
    <item name="floatingActionButtonLargeSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Large.Surface</item>
    <item name="linearProgressIndicatorStyle">@style/Widget.Material3.LinearProgressIndicator</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.Material3.Button.OutlinedButton</item>
    <item name="materialButtonStyle">@style/Widget.Material3.Button</item>
    <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
    <item name="materialCardViewOutlinedStyle">@style/Widget.Material3.CardView.Outlined</item>
    <item name="materialCardViewFilledStyle">@style/Widget.Material3.CardView.Filled</item>
    <item name="materialCardViewElevatedStyle">@style/Widget.Material3.CardView.Elevated</item>
    <item name="navigationRailStyle">@style/Widget.Material3.NavigationRailView</item>
    <item name="navigationViewStyle">@style/Widget.Material3.NavigationView</item>
    <item name="radioButtonStyle">@style/Widget.Material3.CompoundButton.RadioButton</item>
    <item name="sliderStyle">@style/Widget.Material3.Slider</item>
    <item name="snackbarStyle">@style/Widget.Material3.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.Material3.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.Material3.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.Material3.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.Material3.TabLayout</item>
    <item name="tabSecondaryStyle">@style/Widget.Material3.TabLayout.Secondary</item>
    <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
    <item name="textInputOutlinedStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox</item>
    <item name="textInputFilledStyle">@style/Widget.Material3.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedDenseStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense</item>
    <item name="textInputFilledDenseStyle">@style/Widget.Material3.TextInputLayout.FilledBox.Dense</item>
    <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
    <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
    <item name="toolbarStyle">@style/Widget.Material3.Toolbar</item>
    <item name="toolbarSurfaceStyle">@style/Widget.Material3.Toolbar.Surface</item>

    <!-- Top App Bars. -->
    <item name="actionBarSize">@dimen/m3_appbar_size_compact</item>
    <item name="collapsingToolbarLayoutMediumSize">@dimen/m3_appbar_size_medium</item>
    <item name="collapsingToolbarLayoutLargeSize">@dimen/m3_appbar_size_large</item>

    <!-- Popup Menu styles. -->
    <item name="popupMenuBackground">@drawable/m3_popupmenu_background_overlay</item>
    <item name="listPopupWindowStyle">@style/Widget.Material3.PopupMenu.ListPopupWindow</item>
    <item name="popupMenuStyle">@style/Widget.Material3.PopupMenu</item>
    <item name="actionOverflowMenuStyle">@style/Widget.Material3.PopupMenu.Overflow</item>

    <!-- Divider styles. -->
    <item name="materialDividerStyle">@style/Widget.Material3.MaterialDivider</item>
    <item name="materialDividerHeavyStyle">@style/Widget.Material3.MaterialDivider.Heavy</item>

    <!-- Type styles. -->
    <item name="textAppearanceDisplayLarge">@style/TextAppearance.Material3.DisplayLarge</item>
    <item name="textAppearanceDisplayMedium">@style/TextAppearance.Material3.DisplayMedium</item>
    <item name="textAppearanceDisplaySmall">@style/TextAppearance.Material3.DisplaySmall</item>
    <item name="textAppearanceHeadlineLarge">@style/TextAppearance.Material3.HeadlineLarge</item>
    <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
    <item name="textAppearanceHeadlineSmall">@style/TextAppearance.Material3.HeadlineSmall</item>
    <item name="textAppearanceTitleLarge">@style/TextAppearance.Material3.TitleLarge</item>
    <item name="textAppearanceTitleMedium">@style/TextAppearance.Material3.TitleMedium</item>
    <item name="textAppearanceTitleSmall">@style/TextAppearance.Material3.TitleSmall</item>
    <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
    <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
    <item name="textAppearanceBodySmall">@style/TextAppearance.Material3.BodySmall</item>
    <item name="textAppearanceLabelLarge">@style/TextAppearance.Material3.LabelLarge</item>
    <item name="textAppearanceLabelMedium">@style/TextAppearance.Material3.LabelMedium</item>
    <item name="textAppearanceLabelSmall">@style/TextAppearance.Material3.LabelSmall</item>

    <!-- Default Framework Text styles. -->
    <item name="android:textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
    <item name="android:textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
    <item name="android:textAppearanceListItemSecondary" ns1:targetApi="21">?attr/textAppearanceBodyMedium</item>
    <item name="textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
    <item name="textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
    <item name="textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>

    <!-- Menu type styles. -->
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceBodyLarge</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceBodyLarge</item>

    <!-- TODO: Populate font families here -->

    <!-- Shape styles. -->
    <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.Material3.SmallComponent</item>
    <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.Material3.MediumComponent</item>
    <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.Material3.LargeComponent</item>

    <!-- Motion-->
    <item name="motionEasingStandard">@string/m3_sys_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/m3_sys_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/m3_sys_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/m3_sys_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/m3_sys_motion_easing_linear</item>
    <item name="motionDurationShort1">@integer/m3_sys_motion_duration_short1</item>
    <item name="motionDurationShort2">@integer/m3_sys_motion_duration_short2</item>
    <item name="motionDurationMedium1">@integer/m3_sys_motion_duration_medium1</item>
    <item name="motionDurationMedium2">@integer/m3_sys_motion_duration_medium2</item>
    <item name="motionDurationLong1">@integer/m3_sys_motion_duration_long1</item>
    <item name="motionDurationLong2">@integer/m3_sys_motion_duration_long2</item>
    <item name="motionPath">@integer/m3_sys_motion_path</item>

    <!-- Elevation overlays. -->
    <item name="elevationOverlayEnabled">true</item>
    <item name="elevationOverlayColor">?attr/colorPrimary</item>

    <!-- Theme overlays. -->
    <item name="dynamicColorThemeOverlay">@style/ThemeOverlay.Material3.DynamicColors.Light</item>
  </style>
    <style name="Base.V14.Theme.Material3.Light.BottomSheetDialog" parent="Theme.Material3.Light.Dialog">
    <item name="android:windowIsFloating">false</item>
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Material3.BottomSheet.Modal</item>
  </style>
    <style name="Base.V14.Theme.Material3.Light.Dialog" parent="Theme.MaterialComponents.Light.Dialog">
    <item name="isMaterial3Theme">true</item>

    <!-- Color palettes -->
    <item name="colorPrimary">@color/m3_sys_color_light_primary</item>
    <item name="colorPrimaryDark">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/m3_sys_color_light_on_primary</item>
    <item name="colorPrimaryInverse">@color/m3_sys_color_light_inverse_primary</item>
    <item name="colorPrimaryContainer">@color/m3_sys_color_light_primary_container</item>
    <item name="colorOnPrimaryContainer">@color/m3_sys_color_light_on_primary_container</item>
    <item name="colorSecondary">@color/m3_sys_color_light_secondary</item>
    <item name="colorOnSecondary">@color/m3_sys_color_light_on_secondary</item>
    <item name="colorSecondaryContainer">@color/m3_sys_color_light_secondary_container</item>
    <item name="colorOnSecondaryContainer">@color/m3_sys_color_light_on_secondary_container</item>
    <item name="colorTertiary">@color/m3_sys_color_light_tertiary</item>
    <item name="colorOnTertiary">@color/m3_sys_color_light_on_tertiary</item>
    <item name="colorTertiaryContainer">@color/m3_sys_color_light_tertiary_container</item>
    <item name="colorOnTertiaryContainer">@color/m3_sys_color_light_on_tertiary_container</item>
    <item name="android:colorBackground">@color/m3_sys_color_light_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_light_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_light_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_light_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_light_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_light_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_light_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_light_inverse_on_surface</item>
    <item name="colorOutline">@color/m3_sys_color_light_outline</item>
    <item name="colorError">@color/m3_sys_color_light_error</item>
    <item name="colorOnError">@color/m3_sys_color_light_on_error</item>
    <item name="colorErrorContainer">@color/m3_sys_color_light_error_container</item>
    <item name="colorOnErrorContainer">@color/m3_sys_color_light_on_error_container</item>

    <!-- Default Framework Text Colors. -->
    <item name="android:textColorPrimary">@color/m3_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_dark_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dark_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_dark_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_dark_highlighted_text</item>
    <item name="android:textColorLink">?attr/colorPrimary</item>
    <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_default_color_primary_text</item>

    <!-- Widget styles. -->
    <item name="actionBarStyle">@style/Widget.Material3.Light.ActionBar.Solid</item>
    <item name="actionModeStyle">@style/Widget.Material3.ActionMode</item>
    <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout</item>
    <item name="badgeStyle">@style/Widget.Material3.Badge</item>
    <item name="borderlessButtonStyle">@style/Widget.Material3.Button.TextButton</item>
    <item name="bottomAppBarStyle">@style/Widget.Material3.BottomAppBar</item>
    <item name="bottomNavigationStyle">@style/Widget.Material3.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
    <item name="checkboxStyle">@style/Widget.Material3.CompoundButton.CheckBox</item>
    <item name="chipStyle">@style/Widget.Material3.Chip.Assist</item>
    <item name="chipGroupStyle">@style/Widget.Material3.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.Material3.Chip.Input</item>
    <item name="circularProgressIndicatorStyle">@style/Widget.Material3.CircularProgressIndicator</item>
    <item name="collapsingToolbarLayoutStyle">@style/Widget.Material3.CollapsingToolbar</item>
    <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.Material3.CollapsingToolbar.Medium</item>
    <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.Material3.CollapsingToolbar.Large</item>
    <item name="drawerLayoutStyle">@style/Widget.Material3.DrawerLayout</item>
    <item name="extendedFloatingActionButtonStyle">?attr/extendedFloatingActionButtonPrimaryStyle</item>
    <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary</item>
    <item name="extendedFloatingActionButtonSecondaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary</item>
    <item name="extendedFloatingActionButtonTertiaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary</item>
    <item name="extendedFloatingActionButtonSurfaceStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface</item>
    <item name="floatingActionButtonStyle">?attr/floatingActionButtonPrimaryStyle</item>
    <item name="floatingActionButtonPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Primary</item>
    <item name="floatingActionButtonSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Secondary</item>
    <item name="floatingActionButtonTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Tertiary</item>
    <item name="floatingActionButtonSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Surface</item>
    <item name="floatingActionButtonLargeStyle">?attr/floatingActionButtonLargePrimaryStyle</item>
    <item name="floatingActionButtonLargePrimaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Primary</item>
    <item name="floatingActionButtonLargeSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Secondary</item>
    <item name="floatingActionButtonLargeTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Tertiary</item>
    <item name="floatingActionButtonLargeSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Large.Surface</item>
    <item name="linearProgressIndicatorStyle">@style/Widget.Material3.LinearProgressIndicator</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.Material3.Button.OutlinedButton</item>
    <item name="materialButtonStyle">@style/Widget.Material3.Button</item>
    <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
    <item name="materialCardViewOutlinedStyle">@style/Widget.Material3.CardView.Outlined</item>
    <item name="materialCardViewFilledStyle">@style/Widget.Material3.CardView.Filled</item>
    <item name="materialCardViewElevatedStyle">@style/Widget.Material3.CardView.Elevated</item>
    <item name="navigationRailStyle">@style/Widget.Material3.NavigationRailView</item>
    <item name="navigationViewStyle">@style/Widget.Material3.NavigationView</item>
    <item name="radioButtonStyle">@style/Widget.Material3.CompoundButton.RadioButton</item>
    <item name="sliderStyle">@style/Widget.Material3.Slider</item>
    <item name="snackbarStyle">@style/Widget.Material3.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.Material3.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.Material3.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.Material3.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.Material3.TabLayout</item>
    <item name="tabSecondaryStyle">@style/Widget.Material3.TabLayout.Secondary</item>
    <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
    <item name="textInputOutlinedStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox</item>
    <item name="textInputFilledStyle">@style/Widget.Material3.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedDenseStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense</item>
    <item name="textInputFilledDenseStyle">@style/Widget.Material3.TextInputLayout.FilledBox.Dense</item>
    <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
    <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
    <item name="toolbarStyle">@style/Widget.Material3.Toolbar</item>
    <item name="toolbarSurfaceStyle">@style/Widget.Material3.Toolbar.Surface</item>

    <!-- Top App Bars. -->
    <item name="actionBarSize">@dimen/m3_appbar_size_compact</item>
    <item name="collapsingToolbarLayoutMediumSize">@dimen/m3_appbar_size_medium</item>
    <item name="collapsingToolbarLayoutLargeSize">@dimen/m3_appbar_size_large</item>

    <!-- Popup Menu styles. -->
    <item name="popupMenuBackground">@drawable/m3_popupmenu_background_overlay</item>
    <item name="listPopupWindowStyle">@style/Widget.Material3.PopupMenu.ListPopupWindow</item>
    <item name="popupMenuStyle">@style/Widget.Material3.PopupMenu</item>
    <item name="actionOverflowMenuStyle">@style/Widget.Material3.PopupMenu.Overflow</item>

    <!-- Divider styles. -->
    <item name="materialDividerStyle">@style/Widget.Material3.MaterialDivider</item>
    <item name="materialDividerHeavyStyle">@style/Widget.Material3.MaterialDivider.Heavy</item>

    <!-- Type styles. -->
    <item name="textAppearanceDisplayLarge">@style/TextAppearance.Material3.DisplayLarge</item>
    <item name="textAppearanceDisplayMedium">@style/TextAppearance.Material3.DisplayMedium</item>
    <item name="textAppearanceDisplaySmall">@style/TextAppearance.Material3.DisplaySmall</item>
    <item name="textAppearanceHeadlineLarge">@style/TextAppearance.Material3.HeadlineLarge</item>
    <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
    <item name="textAppearanceHeadlineSmall">@style/TextAppearance.Material3.HeadlineSmall</item>
    <item name="textAppearanceTitleLarge">@style/TextAppearance.Material3.TitleLarge</item>
    <item name="textAppearanceTitleMedium">@style/TextAppearance.Material3.TitleMedium</item>
    <item name="textAppearanceTitleSmall">@style/TextAppearance.Material3.TitleSmall</item>
    <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
    <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
    <item name="textAppearanceBodySmall">@style/TextAppearance.Material3.BodySmall</item>
    <item name="textAppearanceLabelLarge">@style/TextAppearance.Material3.LabelLarge</item>
    <item name="textAppearanceLabelMedium">@style/TextAppearance.Material3.LabelMedium</item>
    <item name="textAppearanceLabelSmall">@style/TextAppearance.Material3.LabelSmall</item>

    <!-- Default Framework Text styles. -->
    <item name="android:textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
    <item name="android:textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
    <item name="android:textAppearanceListItemSecondary" ns1:targetApi="21">?attr/textAppearanceBodyMedium</item>
    <item name="textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
    <item name="textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
    <item name="textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>

    <!-- Menu type styles. -->
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceBodyLarge</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceBodyLarge</item>

    <!-- TODO: Populate font families here -->

    <!-- Shape styles. -->
    <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.Material3.SmallComponent</item>
    <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.Material3.MediumComponent</item>
    <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.Material3.LargeComponent</item>

    <!-- Motion-->
    <item name="motionEasingStandard">@string/m3_sys_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/m3_sys_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/m3_sys_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/m3_sys_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/m3_sys_motion_easing_linear</item>
    <item name="motionDurationShort1">@integer/m3_sys_motion_duration_short1</item>
    <item name="motionDurationShort2">@integer/m3_sys_motion_duration_short2</item>
    <item name="motionDurationMedium1">@integer/m3_sys_motion_duration_medium1</item>
    <item name="motionDurationMedium2">@integer/m3_sys_motion_duration_medium2</item>
    <item name="motionDurationLong1">@integer/m3_sys_motion_duration_long1</item>
    <item name="motionDurationLong2">@integer/m3_sys_motion_duration_long2</item>
    <item name="motionPath">@integer/m3_sys_motion_path</item>

    <!-- Elevation overlays. -->
    <item name="elevationOverlayEnabled">true</item>
    <item name="elevationOverlayColor">?attr/colorPrimary</item>

    <!-- Dialog themes. -->
    <item name="alertDialogTheme">@style/ThemeOverlay.Material3.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.Material3.MaterialAlertDialog</item>

    <!-- Theme overlays. -->
    <item name="dynamicColorThemeOverlay">@style/ThemeOverlay.Material3.DynamicColors.Light</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents" parent="Base.V14.Theme.MaterialComponents.Bridge">
    <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>

    <!-- Colors -->
    <item name="colorPrimary">@color/design_dark_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="android:colorBackground">@color/design_dark_default_color_background</item>
    <item name="colorError">@color/design_dark_default_color_error</item>

    <!-- Action bar -->
    <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
    <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
    <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
    <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>

    <!-- Framework, AppCompat, or Design Widget styles -->
    <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
    <item name="android:contextPopupMenuStyle" ns1:targetApi="n">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
    <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
    <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
    <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
    <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
    <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    <!-- Dialog themes -->
    <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
    <item name="android:datePickerDialogTheme" ns1:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>
    <item name="android:timePickerDialogTheme" ns1:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>

    <!-- Type styles -->
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>

    <!-- Picker styles and themes. -->
    <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
    <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
    <item name="materialTimePickerTheme">@style/ThemeOverlay.MaterialComponents.TimePicker</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge" parent="Platform.MaterialComponents">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_dark_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorSurface</item>
    <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
    <item name="colorOnError">@color/design_dark_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnSurface</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_overlay</item>

    <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>

    <!-- MaterialComponents Widget styles -->
    <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
    <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
    <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
    <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
    <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>

    <!-- Type styles -->
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    <!-- Shape styles -->
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    <!-- Motion -->
    <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/material_motion_easing_linear</item>

    <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
    <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
    <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
    <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
    <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
    <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>

    <item name="motionPath">@integer/material_motion_path</item>

    <!-- Elevation Overlays -->
    <item name="elevationOverlayEnabled">true</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="Base.V14.Theme.MaterialComponents.Dialog.Bridge">
    <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>

    <item name="colorPrimary">@color/design_dark_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
    <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="android:colorBackground">@color/design_dark_default_color_background</item>
    <item name="colorError">@color/design_dark_default_color_error</item>

    <!-- Action bar -->
    <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
    <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
    <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
    <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>

    <!-- Framework, AppCompat, or Design Widget styles -->
    <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
    <item name="android:contextPopupMenuStyle" ns1:targetApi="n">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
    <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
    <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
    <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
    <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
    <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>

    <!-- Type styles -->
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" parent="Platform.MaterialComponents.Dialog">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_dark_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorSurface</item>
    <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
    <item name="colorOnError">@color/design_dark_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnSurface</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_overlay</item>

    <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>

    <!-- MaterialComponents Widget styles -->
    <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
    <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
    <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
    <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
    <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>

    <!-- Type styles -->
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    <!-- Shape styles -->
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    <!-- Motion -->
    <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/material_motion_easing_linear</item>

    <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
    <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
    <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
    <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
    <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
    <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>

    <item name="motionPath">@integer/material_motion_path</item>

    <!-- Elevation Overlays -->
    <item name="elevationOverlayEnabled">true</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="Base.V14.Theme.MaterialComponents.Light.Bridge">
    <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>

    <!-- Colors -->
    <item name="colorPrimary">@color/design_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="android:colorBackground">@color/design_default_color_background</item>
    <item name="colorError">@color/design_default_color_error</item>

    <!-- Action bar -->
    <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
    <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
    <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>

    <!-- Framework, AppCompat, or Design Widget styles -->
    <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
    <item name="android:contextPopupMenuStyle" ns1:targetApi="n">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
    <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
    <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
    <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
    <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
    <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    <!-- Dialog themes -->
    <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
    <item name="android:datePickerDialogTheme" ns1:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>
    <item name="android:timePickerDialogTheme" ns1:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>

    <!-- Type styles -->
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>

    <!-- Picker styles and themes. -->
    <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
    <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
    <item name="materialTimePickerTheme">@style/ThemeOverlay.MaterialComponents.TimePicker</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge" parent="Platform.MaterialComponents.Light">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_default_color_on_background</item>
    <item name="colorOnError">@color/design_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>

    <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>

    <!-- MaterialComponents Widget styles -->
    <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
    <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
    <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
    <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
    <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>

    <!-- Type styles -->
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    <!-- Shape styles -->
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    <!-- Motion -->
    <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/material_motion_easing_linear</item>

    <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
    <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
    <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
    <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
    <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
    <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>

    <item name="motionPath">@integer/material_motion_path</item>

    <!-- Elevation Overlays -->
    <item name="elevationOverlayEnabled">false</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Theme.AppCompat.Light.DarkActionBar">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_default_color_on_background</item>
    <item name="colorOnError">@color/design_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>

    <!-- Type styles -->
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    <!-- Shape styles -->
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    <!-- Motion -->
    <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/material_motion_easing_linear</item>

    <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
    <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
    <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
    <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
    <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
    <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>

    <item name="motionPath">@integer/material_motion_path</item>

    <!-- Elevation Overlays -->
    <item name="elevationOverlayEnabled">false</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge">
    <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>

    <!-- Colors -->
    <item name="colorPrimary">@color/design_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
    <item name="colorSecondary">@color/design_default_color_secondary</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="android:colorBackground">@color/design_default_color_background</item>
    <item name="colorError">@color/design_default_color_error</item>

    <!-- Action bar -->
    <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
    <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
    <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>

    <!-- Framework, AppCompat, or Design Widget styles -->
    <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
    <item name="android:contextPopupMenuStyle" ns1:targetApi="n">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
    <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
    <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
    <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
    <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
    <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>

    <!-- Type styles -->
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" parent="Platform.MaterialComponents.Light.Dialog">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_default_color_on_background</item>
    <item name="colorOnError">@color/design_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>

    <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>

    <!-- MaterialComponents Widget styles -->
    <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
    <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
    <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
    <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
    <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>

    <!-- Type styles -->
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    <!-- Shape styles -->
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    <!-- Motion -->
    <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
    <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
    <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
    <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
    <item name="motionEasingLinear">@string/material_motion_easing_linear</item>

    <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
    <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
    <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
    <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
    <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
    <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>

    <item name="motionPath">@integer/material_motion_path</item>

    <!-- Elevation Overlays -->
    <item name="elevationOverlayEnabled">false</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.ThemeOverlay.Material3.BottomSheetDialog" parent="Base.ThemeOverlay.Material3.Dialog">
    <item name="android:windowIsFloating">false</item>
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Material3.BottomSheet.Modal</item>
    <item name="enableEdgeToEdge">true</item>
    <item name="paddingBottomSystemWindowInsets">true</item>
    <item name="paddingLeftSystemWindowInsets">true</item>
    <item name="paddingRightSystemWindowInsets">true</item>
    <item name="paddingTopSystemWindowInsets">true</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="Base.ThemeOverlay.MaterialComponents.Dialog">
    <item name="android:windowIsFloating">false</item>
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
    <item name="enableEdgeToEdge">true</item>
    <item name="paddingBottomSystemWindowInsets">true</item>
    <item name="paddingLeftSystemWindowInsets">true</item>
    <item name="paddingRightSystemWindowInsets">true</item>
    <item name="paddingTopSystemWindowInsets">true</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" parent="ThemeOverlay.AppCompat.Dialog">
    <!-- Widget styles -->
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="ThemeOverlay.AppCompat.Dialog.Alert">
    <!-- Widget styles -->
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="ThemeOverlay.AppCompat.Dialog.Alert">
    <!-- Widget styles -->
    <item name="materialAlertDialogButtonSpacerVisibility">@integer/mtrl_view_invisible</item>
    <item name="alertDialogStyle">@style/MaterialAlertDialog.MaterialComponents</item>
    <item name="android:checkedTextViewStyle" ns1:ignore="NewApi">@style/Widget.MaterialComponents.CheckedTextView</item>
    <item name="android:dialogCornerRadius" ns1:ignore="newApi">@null</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="buttonBarNeutralButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    <item name="android:backgroundDimAmount">0.32</item>
    <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.MaterialComponents.Body.Text</item>
  </style>
    <style name="Base.Widget.Design.TabLayout" parent="android:Widget">
    <item name="android:background">@null</item>
    <item name="tabIconTint">@null</item>
    <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
    <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
    <item name="tabIndicatorColor">?attr/colorAccent</item>
    <item name="tabIndicatorGravity">bottom</item>
    <item name="tabIndicatorAnimationMode">linear</item>
    <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
    <item name="tabPaddingStart">12dp</item>
    <item name="tabPaddingEnd">12dp</item>
    <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
    <item name="tabTextColor">@null</item>
    <item name="tabRippleColor">?attr/colorControlHighlight</item>
    <item name="tabUnboundedRipple">false</item>
  </style>
    <style name="Base.Widget.Material3.ActionBar.Solid" parent="Widget.AppCompat.ActionBar.Solid">
    <item name="titleTextStyle">@style/TextAppearance.Material3.ActionBar.Title</item>
    <item name="subtitleTextStyle">@style/TextAppearance.Material3.ActionBar.Subtitle</item>
    <item name="background">?attr/colorSurface</item>
    <item name="backgroundStacked">?attr/colorSurface</item>
    <item name="backgroundSplit">?attr/colorSurface</item>
  </style>
    <style name="Base.Widget.Material3.ActionMode" parent="Widget.AppCompat.ActionMode">
    <item name="titleTextStyle">@style/TextAppearance.Material3.ActionBar.Title</item>
    <item name="subtitleTextStyle">@style/TextAppearance.Material3.ActionBar.Subtitle</item>
    <item name="background">?attr/colorSurface</item>
    <item name="backgroundSplit">?attr/colorSurface</item>
  </style>
    <style name="Base.Widget.Material3.CardView" parent="Widget.MaterialComponents.CardView">
    <item name="enforceMaterialTheme">true</item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_card_state_list_anim
    </item>
    <item name="cardElevation">@dimen/m3_card_elevation</item>
    <item name="cardForegroundColor">@color/m3_card_foreground_color</item>
    <item name="rippleColor">@color/m3_card_ripple_color</item>
    <item name="strokeColor">@color/m3_card_stroke_color</item>
    <item name="strokeWidth">@dimen/m3_card_stroke_width</item>
    <item name="checkedIconTint">@color/m3_card_stroke_color</item>
  </style>
    <style name="Base.Widget.Material3.Chip" parent="Base.Widget.MaterialComponents.Chip">
    <item name="enforceTextAppearance">false</item>
    <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.Chip</item>
    <item name="android:textColor">@color/m3_chip_text_color</item>
    <item name="closeIconTint">@color/m3_chip_text_color</item>
    <item name="chipStrokeColor">@color/m3_chip_stroke_color</item>
    <item name="chipSurfaceColor">?attr/colorSurface</item>
    <item name="chipBackgroundColor">@color/m3_chip_background_color</item>
    <item name="chipStrokeWidth">1dp</item>
    <item name="rippleColor">@color/m3_chip_ripple_color</item>
    <item name="checkedIcon">@drawable/ic_m3_chip_checked_circle</item>
    <item name="closeIcon">@drawable/ic_m3_chip_close</item>
    <item name="chipIconSize">@dimen/m3_chip_icon_size</item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_chip_state_list_anim
    </item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Chip</item>
  </style>
    <style name="Base.Widget.Material3.CollapsingToolbar" parent="Widget.MaterialComponents.CollapsingToolbar">
    <item name="collapsedTitleTextAppearance">?attr/textAppearanceTitleLarge</item>
    <item name="collapsedTitleTextColor">?attr/colorOnSurface</item>
    <item name="expandedTitleTextColor">?attr/colorOnSurface</item>
    <item name="expandedTitleMarginStart">@dimen/m3_appbar_expanded_title_margin_horizontal</item>
    <item name="expandedTitleMarginEnd">@dimen/m3_appbar_expanded_title_margin_horizontal</item>
    <item name="expandedTitleMarginBottom">@dimen/m3_appbar_expanded_title_margin_bottom</item>
    <item name="extraMultilineHeightEnabled">true</item>
    <item name="forceApplySystemWindowInsetTop">true</item>

    <!-- Title fade behavior -->
    <item name="titleCollapseMode">fade</item>
    <item name="statusBarScrim">@empty</item>
    <item name="scrimAnimationDuration">@integer/app_bar_elevation_anim_duration</item>
    <!-- Allows contentScrim to be drawn in top padding area for edge-to-edge. -->
    <item name="android:clipToPadding">false</item>
  </style>
    <style name="Base.Widget.Material3.CompoundButton.CheckBox" parent="Widget.MaterialComponents.CompoundButton.CheckBox">
    <!-- Inherit default text color since the component doesn't draw a surface. -->
    <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
    <item name="buttonTint">@color/m3_selection_control_button_tint</item>
  </style>
    <style name="Base.Widget.Material3.CompoundButton.RadioButton" parent="Widget.MaterialComponents.CompoundButton.RadioButton">
    <!-- Inherit default text color since the component doesn't draw a surface. -->
    <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
    <item name="buttonTint">@color/m3_selection_control_button_tint</item>
  </style>
    <style name="Base.Widget.Material3.CompoundButton.Switch" parent="Widget.MaterialComponents.CompoundButton.Switch">
    <!-- Inherit default text color since the component doesn't draw a surface. -->
    <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
    <item name="thumbTint">@color/m3_switch_thumb_tint</item>
    <item name="trackTint">@color/m3_switch_track_tint</item>
    <item name="trackTintMode">src_in</item>
  </style>
    <style name="Base.Widget.Material3.ExtendedFloatingActionButton" parent="Widget.MaterialComponents.ExtendedFloatingActionButton">
    <item name="android:minHeight">@dimen/m3_extended_fab_min_height</item>
    <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
    <item name="android:textColor">@color/m3_button_foreground_color_selector</item>
    <item name="backgroundTint">@color/m3_button_background_color_selector</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="iconTint">@color/m3_button_foreground_color_selector</item>
    <item name="rippleColor">@color/m3_button_ripple_color_selector</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.FloatingActionButton</item>
  </style>
    <style name="Base.Widget.Material3.ExtendedFloatingActionButton.Icon" parent="Base.Widget.Material3.ExtendedFloatingActionButton">
    <item name="android:paddingStart" ns1:ignore="NewApi">
      @dimen/m3_extended_fab_start_padding
    </item>
    <item name="android:paddingLeft">@dimen/m3_extended_fab_start_padding</item>
    <item name="android:paddingTop">@dimen/m3_extended_fab_top_padding</item>
    <item name="android:paddingEnd" ns1:ignore="NewApi">
      @dimen/m3_extended_fab_end_padding
    </item>
    <item name="android:paddingRight">@dimen/m3_extended_fab_end_padding</item>
    <item name="android:paddingBottom">@dimen/m3_extended_fab_bottom_padding</item>
    <item name="iconPadding">@dimen/m3_extended_fab_icon_padding</item>
  </style>
    <style name="Base.Widget.Material3.FloatingActionButton" parent="Widget.MaterialComponents.FloatingActionButton">
    <item name="backgroundTint">@color/m3_button_background_color_selector</item>
    <item name="borderWidth">@dimen/m3_fab_border_width</item>
    <item name="elevation">6dp</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="hoveredFocusedTranslationZ">@dimen/m3_fab_translation_z_hovered_focused</item>
    <item name="pressedTranslationZ">@dimen/m3_fab_translation_z_pressed</item>
    <item name="rippleColor">@color/m3_button_ripple_color_selector</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.FloatingActionButton</item>
    <item name="tint">@color/m3_button_foreground_color_selector</item>
    <!--Remove the motion spec and allow FloatingActionButtonImpl to create a theme-based animation-->
    <item name="showMotionSpec">@null</item>
    <item name="hideMotionSpec">@null</item>
  </style>
    <style name="Base.Widget.Material3.FloatingActionButton.Large" parent="Base.Widget.Material3.FloatingActionButton">
    <item name="fabCustomSize">@dimen/m3_large_fab_size</item>
    <item name="maxImageSize">@dimen/m3_large_fab_max_image_size</item>
  </style>
    <style name="Base.Widget.Material3.Light.ActionBar.Solid" parent="Widget.AppCompat.Light.ActionBar.Solid">
    <item name="titleTextStyle">@style/TextAppearance.Material3.ActionBar.Title</item>
    <item name="subtitleTextStyle">@style/TextAppearance.Material3.ActionBar.Subtitle</item>
    <item name="background">?attr/colorSurface</item>
    <item name="backgroundStacked">?attr/colorSurface</item>
    <item name="backgroundSplit">?attr/colorSurface</item>
  </style>
    <style name="Base.Widget.Material3.MaterialCalendar.NavigationButton" parent="Widget.Material3.Button.TextButton.Dialog.Flush">
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    <item name="iconTint">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="Base.Widget.Material3.Snackbar" parent="Base.Widget.MaterialComponents.Snackbar">
    <item name="enforceMaterialTheme">true</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Snackbar</item>
    <item name="backgroundTint">?attr/colorSurfaceInverse</item>
    <item name="actionTextColorAlpha">@dimen/m3_snackbar_action_text_color_alpha</item>
  </style>
    <style name="Base.Widget.Material3.TabLayout" parent="Widget.MaterialComponents.TabLayout">
    <item name="enforceTextAppearance">false</item>
    <item name="tabIconTint">@color/m3_tabs_icon_color</item>
    <item name="tabTextAppearance">?attr/textAppearanceLabelLarge</item>
    <item name="tabTextColor">@color/m3_tabs_icon_color</item>
    <item name="tabIndicator">@drawable/m3_tabs_rounded_line_indicator</item>
    <item name="tabIndicatorAnimationMode">elastic</item>
    <item name="tabIndicatorColor">?attr/colorPrimary</item>
    <item name="tabRippleColor">@color/m3_tabs_ripple_color</item>
    <item name="tabIndicatorFullWidth">false</item>
  </style>
    <style name="Base.Widget.Material3.TabLayout.OnSurface" parent="Widget.Material3.TabLayout">
    <item name="android:background">@android:color/transparent</item>
  </style>
    <style name="Base.Widget.Material3.TabLayout.Secondary" parent="Widget.Material3.TabLayout">
    <item name="tabIndicator">@drawable/m3_tabs_line_indicator</item>
    <item name="tabIndicatorFullWidth">true</item>
  </style>
    <style name="Base.Widget.MaterialComponents.AutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView">
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:background">@null</item>
    <item name="android:paddingStart" ns1:ignore="NewApi">16dp</item>
    <item name="android:paddingEnd" ns1:ignore="NewApi">16dp</item>
    <item name="android:paddingLeft">16dp</item>
    <item name="android:paddingRight">16dp</item>
    <!-- Edit text's default text size is 16sp which currently equals to 22dp total line height, so
     we need a total of 34dp to get the 56dp height of the default layout. -->
    <item name="android:paddingTop">17dp</item>
    <item name="android:paddingBottom">17dp</item>
    <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="android:dropDownVerticalOffset">@dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset</item>
    <item name="android:popupElevation" ns1:ignore="NewApi">@dimen/mtrl_exposed_dropdown_menu_popup_elevation</item>
  </style>
    <style name="Base.Widget.MaterialComponents.CheckedTextView" parent="android:Widget"/>
    <style name="Base.Widget.MaterialComponents.Chip" parent="android:Widget">
    <item name="android:focusable">true</item>
    <item name="android:clickable">true</item>
    <item name="android:checkable">false</item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/mtrl_chip_state_list_anim
    </item>

    <item name="chipIconVisible">true</item>
    <item name="checkedIconVisible">true</item>
    <item name="closeIconVisible">true</item>

    <item name="chipIcon">@null</item>
    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
    <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>

    <item name="android:text">@null</item>
    <item name="android:includeFontPadding">false</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    <item name="android:textColor">@color/mtrl_chip_text_color</item>
    <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>

    <item name="chipSurfaceColor">@color/mtrl_chip_surface_color</item>
    <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
    <item name="chipStrokeColor">?attr/colorOnSurface</item>
    <item name="chipStrokeWidth">0dp</item>
    <item name="rippleColor">@color/mtrl_on_surface_ripple_color</item>

    <item name="chipMinTouchTargetSize">48dp</item>
    <item name="ensureMinTouchTargetSize">true</item>
    <item name="chipMinHeight">32dp</item>
    <item name="chipIconSize">24dp</item>
    <item name="closeIconSize">18dp</item>

    <item name="chipStartPadding">4dp</item>
    <item name="iconStartPadding">0dp</item>
    <item name="iconEndPadding">0dp</item>
    <item name="textStartPadding">8dp</item>
    <item name="textEndPadding">6dp</item>
    <item name="closeIconStartPadding">2dp</item>
    <item name="closeIconEndPadding">2dp</item>
    <item name="chipEndPadding">6dp</item>

    <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.Chip</item>
  </style>
    <style name="Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton" parent="Widget.MaterialComponents.Button.TextButton.Dialog.Flush">
    <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    <item name="iconTint">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu" parent="Widget.AppCompat.PopupMenu">
    <item name="overlapAnchor">false</item>
    <item name="android:dropDownVerticalOffset">1px</item>
  </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" parent="Widget.AppCompat.PopupMenu"/>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow"/>
    <style name="Base.Widget.MaterialComponents.PopupMenu.Overflow" parent="Widget.AppCompat.PopupMenu.Overflow">
    <item name="android:dropDownVerticalOffset">1px</item>
  </style>
    <style name="Base.Widget.MaterialComponents.Slider" parent="android:Widget">
    <item name="haloColor">@color/material_slider_halo_color</item>
    <item name="haloRadius">@dimen/mtrl_slider_halo_radius</item>
    <item name="labelStyle">@style/Widget.MaterialComponents.Tooltip</item>
    <item name="thumbColor">@color/material_slider_thumb_color</item>
    <item name="thumbElevation">@dimen/mtrl_slider_thumb_elevation</item>
    <item name="thumbRadius">@dimen/mtrl_slider_thumb_radius</item>
    <item name="tickColorActive">@color/material_slider_active_tick_marks_color</item>
    <item name="tickColorInactive">@color/material_slider_inactive_tick_marks_color</item>
    <item name="trackColorActive">@color/material_slider_active_track_color</item>
    <item name="trackColorInactive">@color/material_slider_inactive_track_color</item>
    <item name="trackHeight">@dimen/mtrl_slider_track_height</item>
    <item name="minSeparation">0dp</item>
  </style>
    <style name="Base.Widget.MaterialComponents.Snackbar" parent="Widget.Design.Snackbar">
    <!--
      The snackbar view has a 8dp padding on left and right. Message Textview has a 8dp margin on left and right.
    -->
    <item name="android:paddingLeft">@dimen/mtrl_snackbar_padding_horizontal</item>
    <item name="android:paddingRight">@dimen/mtrl_snackbar_padding_horizontal</item>
    <item name="backgroundOverlayColorAlpha">@dimen/mtrl_snackbar_background_overlay_color_alpha</item>
    <item name="actionTextColorAlpha">@dimen/mtrl_snackbar_action_text_color_alpha</item>
  </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="Widget.Design.TextInputEditText">
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:background">@null</item>
    <item name="android:paddingStart" ns1:ignore="NewApi">16dp</item>
    <item name="android:paddingEnd" ns1:ignore="NewApi">16dp</item>
    <item name="android:paddingLeft">16dp</item>
    <item name="android:paddingRight">16dp</item>
    <!-- Edit text's default text size is 16sp which currently equals to 22dp total line height, so
         we need a total of 34dp to get the 56dp height of the default layout. -->
    <item name="android:paddingTop">17dp</item>
    <item name="android:paddingBottom">17dp</item>
    <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="textInputLayoutFocusedRectEnabled">true</item>
  </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout" parent="Widget.Design.TextInputLayout">
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>

    <item name="android:minWidth">@dimen/material_textinput_min_width</item>
    <item name="android:maxWidth">@dimen/material_textinput_max_width</item>
    <item name="boxBackgroundMode">outline</item>
    <item name="boxBackgroundColor">@null</item>
    <item name="errorIconDrawable">@drawable/mtrl_ic_error</item>
    <item name="errorIconTint">@color/mtrl_error</item>
    <item name="endIconTint">@color/mtrl_outlined_icon_tint</item>
    <item name="startIconTint">@color/mtrl_outlined_icon_tint</item>
    <item name="boxCollapsedPaddingTop">0dp</item>
    <item name="boxStrokeColor">@color/mtrl_outlined_stroke_color</item>
    <item name="boxStrokeErrorColor">@color/mtrl_error</item>
    <item name="boxStrokeWidth">@dimen/mtrl_textinput_box_stroke_width_default</item>
    <item name="boxStrokeWidthFocused">@dimen/mtrl_textinput_box_stroke_width_focused</item>

    <item name="counterTextAppearance">?attr/textAppearanceCaption</item>
    <item name="counterOverflowTextAppearance">?attr/textAppearanceCaption</item>
    <item name="errorTextAppearance">?attr/textAppearanceCaption</item>
    <item name="helperTextTextAppearance">?attr/textAppearanceCaption</item>
    <item name="hintTextAppearance">?attr/textAppearanceCaption</item>
    <item name="placeholderTextAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="prefixTextAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="suffixTextAppearance">?attr/textAppearanceSubtitle1</item>

    <item name="counterTextColor">@color/mtrl_indicator_text_color</item>
    <item name="counterOverflowTextColor">@color/mtrl_error</item>
    <item name="errorTextColor">@color/mtrl_error</item>
    <item name="helperTextTextColor">@color/mtrl_indicator_text_color</item>
    <!-- The color of the label when it is collapsed and the text field is active -->
    <item name="hintTextColor">?attr/colorPrimary</item>
    <item name="placeholderTextColor">@color/mtrl_indicator_text_color</item>
    <item name="prefixTextColor">@color/mtrl_indicator_text_color</item>
    <item name="suffixTextColor">@color/mtrl_indicator_text_color</item>
    <!-- The color of the label in all other text field states (such as resting and disabled) -->
    <item name="android:textColorHint">@color/mtrl_indicator_text_color</item>

    <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="shapeAppearanceOverlay">@null</item>
  </style>
    <style name="Base.Widget.MaterialComponents.TextView" parent="Widget.AppCompat.TextView"/>
    <style name="EmptyTheme"/>
    <style name="MaterialAlertDialog.Material3" parent="MaterialAlertDialog.MaterialComponents">
    <item name="enforceMaterialTheme">true</item>
    <item name="android:layout">@layout/m3_alert_dialog</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3</item>
  </style>
    <style name="MaterialAlertDialog.Material3.Body.Text" parent="">
    <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="MaterialAlertDialog.Material3.Body.Text.CenterStacked">
    <item name="android:layout_gravity">center</item>
    <item name="android:gravity">center</item>
    <item name="android:textAlignment" ns1:ignore="NewApi">gravity</item>
  </style>
    <style name="MaterialAlertDialog.Material3.Title.Icon" parent="MaterialAlertDialog.MaterialComponents.Title.Icon">
    <item name="android:layout_width">@dimen/m3_alert_dialog_icon_size</item>
    <item name="android:layout_height">@dimen/m3_alert_dialog_icon_size</item>
    <item name="android:layout_marginEnd" ns1:ignore="NewApi">@dimen/m3_alert_dialog_icon_margin</item>
    <item name="android:layout_marginRight">@dimen/m3_alert_dialog_icon_margin</item>
    <item name="android:theme">@style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon</item>
  </style>
    <style name="MaterialAlertDialog.Material3.Title.Icon.CenterStacked">
    <item name="android:layout_marginEnd" ns1:ignore="NewApi">0dp</item>
    <item name="android:layout_marginRight">0dp</item>
    <item name="android:layout_marginBottom">@dimen/m3_alert_dialog_icon_margin</item>
    <item name="android:layout_gravity">center</item>
  </style>
    <style name="MaterialAlertDialog.Material3.Title.Panel" parent="MaterialAlertDialog.MaterialComponents.Title.Panel"/>
    <style name="MaterialAlertDialog.Material3.Title.Panel.CenterStacked">
    <item name="android:orientation">vertical</item>
  </style>
    <style name="MaterialAlertDialog.Material3.Title.Text" parent="MaterialAlertDialog.MaterialComponents.Title.Text">
    <item name="android:textAppearance">?attr/textAppearanceHeadlineSmall</item>
    <item name="android:textColor">?attr/colorOnSurface</item>
  </style>
    <style name="MaterialAlertDialog.Material3.Title.Text.CenterStacked">
    <item name="android:layout_gravity">center</item>
    <item name="android:gravity">center</item>
    <item name="android:textAlignment" ns1:ignore="NewApi">gravity</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents" parent="AlertDialog.AppCompat">
    <item name="android:layout">@layout/mtrl_alert_dialog</item>
    <item name="listItemLayout">@layout/mtrl_alert_select_dialog_item</item>
    <item name="multiChoiceItemLayout">@layout/mtrl_alert_select_dialog_multichoice</item>
    <item name="singleChoiceItemLayout">@layout/mtrl_alert_select_dialog_singlechoice</item>

    <item name="backgroundInsetStart">@dimen/mtrl_alert_dialog_background_inset_start</item>
    <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_background_inset_top</item>
    <item name="backgroundInsetEnd">@dimen/mtrl_alert_dialog_background_inset_end</item>
    <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_background_inset_bottom</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Body.Text" parent="TextAppearance.MaterialComponents.Body2">
    <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" parent="android:Widget.DeviceDefault.DatePicker">
    <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" parent="android:Widget.DeviceDefault.DatePicker">
    <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
    <item name="android:layout_gravity">start|center_vertical</item>
    <item name="android:layout_marginEnd" ns1:ignore="NewApi">8dip</item>
    <item name="android:layout_marginRight">8dip</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
    <item name="android:layout_gravity">center</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
    <item name="android:orientation">horizontal</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
    <item name="android:orientation">vertical</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Text">
    <item name="android:layout_gravity">start|center_vertical</item>
    <item name="android:textAlignment" ns1:ignore="NewApi">viewStart</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Text">
    <item name="android:layout_gravity">center</item>
    <item name="android:textAlignment" ns1:ignore="NewApi">center</item>
  </style>
    <style name="Platform.MaterialComponents" parent="Theme.AppCompat"/>
    <style name="Platform.MaterialComponents.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Platform.MaterialComponents.Light" parent="Theme.AppCompat.Light"/>
    <style name="Platform.MaterialComponents.Light.Dialog" parent="Theme.AppCompat.Light.Dialog"/>
    <style name="ShapeAppearance.Material3.LargeComponent" parent="ShapeAppearance.MaterialComponents.LargeComponent">
    <item name="cornerFamily">@integer/m3_sys_shape_large_corner_family</item>
    <item name="cornerSize">@dimen/m3_sys_shape_large_corner_size</item>
  </style>
    <style name="ShapeAppearance.Material3.MediumComponent" parent="ShapeAppearance.MaterialComponents.MediumComponent">
    <item name="cornerFamily">@integer/m3_sys_shape_medium_corner_family</item>
    <item name="cornerSize">@dimen/m3_sys_shape_medium_corner_size</item>
  </style>
    <style name="ShapeAppearance.Material3.NavigationBarView.ActiveIndicator" parent="ShapeAppearance.Material3.SmallComponent">
    <item name="cornerSize">50%</item>
  </style>
    <style name="ShapeAppearance.Material3.SmallComponent" parent="ShapeAppearance.MaterialComponents.SmallComponent">
    <item name="cornerFamily">@integer/m3_sys_shape_small_corner_family</item>
    <item name="cornerSize">@dimen/m3_sys_shape_small_corner_size</item>
  </style>
    <style name="ShapeAppearance.Material3.Tooltip" parent="ShapeAppearance.MaterialComponents.Tooltip">
    <item name="cornerSize">50%</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents" parent="">
    <item name="cornerFamily">rounded</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.LargeComponent">
    <item name="cornerSize">@dimen/mtrl_shape_corner_size_large_component</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.MediumComponent">
    <item name="cornerSize">@dimen/mtrl_shape_corner_size_medium_component</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.SmallComponent">
    <item name="cornerSize">@dimen/mtrl_shape_corner_size_small_component</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.Test" parent="">
    <item name="cornerFamily">rounded</item>
    <item name="cornerSize">10px</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.Tooltip" parent="">
    <item name="cornerFamily">rounded</item>
    <item name="cornerSize">4dp</item>
  </style>
    <style name="ShapeAppearanceOverlay" parent=""/>
    <style name="ShapeAppearanceOverlay.BottomLeftDifferentCornerSize">
    <item name="cornerSizeBottomLeft">20px</item>
  </style>
    <style name="ShapeAppearanceOverlay.BottomRightCut">
    <item name="cornerFamilyBottomRight">cut</item>
  </style>
    <style name="ShapeAppearanceOverlay.Cut">
    <item name="cornerFamily">cut</item>
  </style>
    <style name="ShapeAppearanceOverlay.DifferentCornerSize">
    <item name="cornerSize">20px</item>
  </style>
    <style name="ShapeAppearanceOverlay.Material3.Button" parent="">
    <item name="cornerSize">50%</item>
  </style>
    <style name="ShapeAppearanceOverlay.Material3.Chip" parent="">
    <item name="cornerSize">8dp</item>
  </style>
    <style name="ShapeAppearanceOverlay.Material3.FloatingActionButton" parent="">
    <item name="cornerSize">@dimen/m3_fab_corner_size</item>
  </style>
    <style name="ShapeAppearanceOverlay.Material3.NavigationView.Item" parent="">
    <item name="cornerSize">50%</item>
  </style>
    <style name="ShapeAppearanceOverlay.Material3.TextField.Filled" parent="ShapeAppearance.Material3.SmallComponent">
    <item name="cornerSizeBottomLeft">0dp</item>
    <item name="cornerSizeBottomRight">0dp</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialAlertDialog.Material3" parent="">
    <item name="cornerSize">@dimen/m3_alert_dialog_corner_size</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" parent="">
    <item name="cornerSizeBottomRight">0dp</item>
    <item name="cornerSizeBottomLeft">0dp</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.Chip" parent="">
    <item name="cornerSize">50%</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" parent="">
    <item name="cornerSize">@null</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" parent="">
    <item name="cornerSize">50%</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" parent="">
    <item name="cornerSize">@dimen/mtrl_calendar_day_corner</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" parent="">
    <item name="cornerSize">0dp</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" parent="">
    <item name="cornerSize">@dimen/mtrl_calendar_year_corner</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" parent="">
    <item name="cornerSizeBottomLeft">@dimen/mtrl_textinput_box_corner_radius_small</item>
    <item name="cornerSizeBottomRight">@dimen/mtrl_textinput_box_corner_radius_small</item>
  </style>
    <style name="ShapeAppearanceOverlay.TopLeftCut">
    <item name="cornerFamilyTopLeft">cut</item>
  </style>
    <style name="ShapeAppearanceOverlay.TopRightDifferentCornerSize">
    <item name="cornerSizeTopRight">20px</item>
  </style>
    <style name="Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" parent="">
    <item name="cornerFamily">rounded</item>
    <item name="cornerSize">@dimen/test_mtrl_calendar_day_cornerSize</item>
  </style>
    <style name="Test.Theme.MaterialComponents.MaterialCalendar" parent="Theme.MaterialComponents.Light">
    <item name="materialCalendarStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar</item>
  </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar" parent="">
    <item name="dayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="dayInvalidStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="daySelectedStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
    <item name="dayTodayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="yearStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="yearSelectedStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
    <item name="yearTodayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="rangeFillColor">@color/test_mtrl_calendar_day</item>
  </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar.Day" parent="">
    <item name="itemFillColor">@color/test_mtrl_calendar_day</item>
    <item name="itemTextColor">@color/test_mtrl_calendar_day</item>
    <item name="itemStrokeColor">@color/test_mtrl_calendar_day</item>
    <item name="itemStrokeWidth">0dp</item>
    <item name="itemShapeAppearance">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    <item name="itemShapeAppearanceOverlay">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
  </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected" parent="">
    <item name="itemFillColor">@color/test_mtrl_calendar_day_selected</item>
    <item name="itemTextColor">@color/test_mtrl_calendar_day_selected</item>
    <item name="itemStrokeColor">@color/test_mtrl_calendar_day</item>
    <item name="itemStrokeWidth">0dp</item>
    <item name="itemShapeAppearance">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    <item name="itemShapeAppearanceOverlay">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
  </style>
    <style name="TestStyleWithLineHeight" parent="TestStyleWithoutLineHeight">
    <item name="lineHeight">@dimen/material_text_view_test_line_height</item>
  </style>
    <style name="TestStyleWithLineHeightAppearance">
    <item name="android:textAppearance">@style/TestStyleWithLineHeight</item>
  </style>
    <style name="TestStyleWithThemeLineHeightAttribute" parent="TestStyleWithoutLineHeight">
    <item name="lineHeight">?attr/themeLineHeight</item>
  </style>
    <style name="TestStyleWithoutLineHeight" parent="TextAppearance.AppCompat.Title">
    <item name="android:textSize">20sp</item>
  </style>
    <style name="TestThemeWithLineHeight" parent="Theme.AppCompat.Light">
    <item name="themeLineHeight">@dimen/material_text_view_test_line_height</item>
  </style>
    <style name="TestThemeWithLineHeightDisabled" parent="TestThemeWithLineHeight">
    <item name="textAppearanceLineHeightEnabled">false</item>
  </style>
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded" parent="TextAppearance.AppCompat.Display1">
    <item name="android:textColor">?android:attr/textColorPrimary</item>
  </style>
    <style name="TextAppearance.Design.Counter" parent="TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.Design.Counter.Overflow" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">@color/design_error</item>
  </style>
    <style name="TextAppearance.Design.Error" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">@color/design_error</item>
  </style>
    <style name="TextAppearance.Design.HelperText" parent="TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.Design.Hint" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">?attr/colorControlActivated</item>
  </style>
    <style name="TextAppearance.Design.Placeholder" parent="TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.Design.Prefix" parent="TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.Design.Snackbar.Message" parent="android:TextAppearance">
    <item name="android:textSize">@dimen/design_snackbar_text_size</item>
    <item name="android:textColor">?android:textColorPrimary</item>
  </style>
    <style name="TextAppearance.Design.Suffix" parent="TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.Design.Tab" parent="TextAppearance.AppCompat.Button">
    <item name="android:textSize">@dimen/design_tab_text_size</item>
    <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
    <item name="textAllCaps">true</item>
  </style>
    <style name="TextAppearance.Material3.ActionBar.Subtitle" parent="TextAppearance.Material3.TitleMedium">
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="TextAppearance.Material3.ActionBar.Title" parent="TextAppearance.Material3.TitleLarge">
    <item name="android:textColor">?attr/colorOnSurface</item>
  </style>
    <style name="TextAppearance.Material3.BodyLarge" parent="TextAppearance.MaterialComponents.Body1">
    <item name="fontFamily">@string/m3_sys_typescale_body_large_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_body_large_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_body_large_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_body_large_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_body_large_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.BodyMedium" parent="TextAppearance.MaterialComponents.Body2">
    <item name="fontFamily">@string/m3_sys_typescale_body_medium_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_body_medium_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_body_medium_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_body_medium_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_body_medium_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.BodySmall" parent="TextAppearance.MaterialComponents.Caption">
    <item name="fontFamily">@string/m3_sys_typescale_body_small_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_body_small_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_body_small_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_body_small_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_body_small_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.DisplayLarge" parent="TextAppearance.MaterialComponents.Headline2">
    <item name="fontFamily">@string/m3_sys_typescale_display_large_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_display_large_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_display_large_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_display_large_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_display_large_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.DisplayMedium" parent="TextAppearance.MaterialComponents.Headline3">
    <item name="fontFamily">@string/m3_sys_typescale_display_medium_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_display_medium_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_display_medium_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_display_medium_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_display_medium_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.DisplaySmall" parent="TextAppearance.MaterialComponents.Headline4">
    <item name="fontFamily">@string/m3_sys_typescale_display_small_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_display_small_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_display_small_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_display_small_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_display_small_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.HeadlineLarge" parent="TextAppearance.MaterialComponents.Headline4">
    <item name="fontFamily">@string/m3_sys_typescale_headline_large_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_headline_large_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_headline_large_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_headline_large_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_headline_large_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.HeadlineMedium" parent="TextAppearance.MaterialComponents.Headline5">
    <item name="fontFamily">@string/m3_sys_typescale_headline_medium_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_headline_medium_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_headline_medium_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_headline_medium_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_headline_medium_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.HeadlineSmall" parent="TextAppearance.MaterialComponents.Headline6">
    <item name="fontFamily">@string/m3_sys_typescale_headline_small_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_headline_small_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_headline_small_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_headline_small_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_headline_small_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.LabelLarge" parent="Base.TextAppearance.Material3.LabelLarge"/>
    <style name="TextAppearance.Material3.LabelMedium" parent="Base.TextAppearance.Material3.LabelMedium"/>
    <style name="TextAppearance.Material3.LabelSmall" parent="Base.TextAppearance.Material3.LabelSmall"/>
    <style name="TextAppearance.Material3.MaterialTimePicker.Title" parent="TextAppearance.Material3.LabelMedium">
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="TextAppearance.Material3.TitleLarge" parent="TextAppearance.MaterialComponents.Headline6">
    <item name="fontFamily">@string/m3_sys_typescale_title_large_font</item>
    <item name="android:fontFamily">@string/m3_sys_typescale_title_large_font</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">@bool/m3_sys_typescale_title_large_text_all_caps</item>
    <item name="android:textSize">@dimen/m3_sys_typescale_title_large_text_size</item>
    <item name="android:letterSpacing">@dimen/m3_sys_typescale_title_large_letter_spacing</item>
  </style>
    <style name="TextAppearance.Material3.TitleMedium" parent="Base.TextAppearance.Material3.TitleMedium"/>
    <style name="TextAppearance.Material3.TitleSmall" parent="Base.TextAppearance.Material3.TitleSmall"/>
    <style name="TextAppearance.MaterialComponents.Badge" parent="Base.TextAppearance.MaterialComponents.Badge"/>
    <style name="TextAppearance.MaterialComponents.Body1" parent="TextAppearance.AppCompat.Body2">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">16sp</item>
    <item name="android:letterSpacing">0.03125</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="TextAppearance.AppCompat.Body1">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.0178571429</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="Base.TextAppearance.MaterialComponents.Button"/>
    <style name="TextAppearance.MaterialComponents.Caption" parent="TextAppearance.AppCompat.Caption">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">12sp</item>
    <item name="android:letterSpacing">0.0333333333</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Chip" parent="TextAppearance.AppCompat">
    <item name="android:textColor">@color/mtrl_chip_text_color</item>
    <item name="android:textSize">@dimen/mtrl_chip_text_size</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="TextAppearance.AppCompat.Display4">
    <item name="fontFamily">sans-serif-light</item>
    <item name="android:fontFamily">sans-serif-light</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">96sp</item>
    <item name="android:letterSpacing">-0.015625</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="TextAppearance.AppCompat.Display3">
    <item name="fontFamily">sans-serif-light</item>
    <item name="android:fontFamily">sans-serif-light</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">60sp</item>
    <item name="android:letterSpacing">-0.00833333333</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="TextAppearance.AppCompat.Display2">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">48sp</item>
    <item name="android:letterSpacing">0</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="TextAppearance.AppCompat.Display1">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">34sp</item>
    <item name="android:letterSpacing">0.00735294118</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="TextAppearance.AppCompat.Headline">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">24sp</item>
    <item name="android:letterSpacing">0</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="Base.TextAppearance.MaterialComponents.Headline6"/>
    <style name="TextAppearance.MaterialComponents.Overline" parent="TextAppearance.AppCompat">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">true</item>
    <item name="android:textSize">10sp</item>
    <item name="android:letterSpacing">0.166666667</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="TextAppearance.AppCompat.Subhead">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">16sp</item>
    <item name="android:letterSpacing">0.009375</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="Base.TextAppearance.MaterialComponents.Subtitle2"/>
    <style name="TextAppearance.MaterialComponents.TimePicker.Title" parent="TextAppearance.MaterialComponents.Overline">
    <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Tooltip" parent="TextAppearance.MaterialComponents.Body2">
    <item name="android:textColor">?attr/colorOnPrimary</item>
  </style>
    <style name="Theme.Design" parent="Theme.AppCompat">
  </style>
    <style name="Theme.Design.BottomSheetDialog" parent="Theme.AppCompat.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
  </style>
    <style name="Theme.Design.Light" parent="Theme.AppCompat.Light">
  </style>
    <style name="Theme.Design.Light.BottomSheetDialog" parent="Theme.AppCompat.Light.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
  </style>
    <style name="Theme.Design.Light.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.Design.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.Material3.Dark" parent="Base.Theme.Material3.Dark"/>
    <style name="Theme.Material3.Dark.BottomSheetDialog" parent="Base.Theme.Material3.Dark.BottomSheetDialog"/>
    <style name="Theme.Material3.Dark.Dialog" parent="Base.Theme.Material3.Dark.Dialog"/>
    <style name="Theme.Material3.Dark.Dialog.Alert" parent="Base.Theme.Material3.Dark.Dialog">
    <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.Material3.Dark.Dialog.MinWidth" parent="Base.Theme.Material3.Dark.Dialog">
    <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.Material3.Dark.DialogWhenLarge" parent="Base.Theme.Material3.Dark.Dialog"/>
    <style name="Theme.Material3.Dark.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.Material3.DayNight" parent="Theme.Material3.Light"/>
    <style name="Theme.Material3.DayNight.BottomSheetDialog" parent="Theme.Material3.Light.BottomSheetDialog"/>
    <style name="Theme.Material3.DayNight.Dialog" parent="Theme.Material3.Light.Dialog"/>
    <style name="Theme.Material3.DayNight.Dialog.Alert" parent="Theme.Material3.Light.Dialog.Alert"/>
    <style name="Theme.Material3.DayNight.Dialog.MinWidth" parent="Theme.Material3.Light.Dialog.MinWidth"/>
    <style name="Theme.Material3.DayNight.DialogWhenLarge" parent="Theme.Material3.Light.DialogWhenLarge"/>
    <style name="Theme.Material3.DayNight.NoActionBar" parent="Theme.Material3.Light.NoActionBar"/>
    <style name="Theme.Material3.DynamicColors.Dark" parent="Theme.Material3.Dark"/>
    <style name="Theme.Material3.DynamicColors.DayNight" parent="Theme.Material3.DynamicColors.Light"/>
    <style name="Theme.Material3.DynamicColors.Light" parent="Theme.Material3.Light"/>
    <style name="Theme.Material3.Light" parent="Base.Theme.Material3.Light"/>
    <style name="Theme.Material3.Light.BottomSheetDialog" parent="Base.Theme.Material3.Light.BottomSheetDialog"/>
    <style name="Theme.Material3.Light.Dialog" parent="Base.Theme.Material3.Light.Dialog"/>
    <style name="Theme.Material3.Light.Dialog.Alert" parent="Base.Theme.Material3.Light.Dialog">
    <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.Material3.Light.Dialog.MinWidth" parent="Base.Theme.Material3.Light.Dialog">
    <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.Material3.Light.DialogWhenLarge" parent="Base.Theme.Material3.Light.Dialog"/>
    <style name="Theme.Material3.Light.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents" parent="Base.Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.BottomSheetDialog" parent="Theme.MaterialComponents.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
  </style>
    <style name="Theme.MaterialComponents.Bridge" parent="Base.Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.CompactMenu" parent="Base.Theme.MaterialComponents.CompactMenu"/>
    <style name="Theme.MaterialComponents.DayNight" parent="Theme.MaterialComponents.Light"/>
    <style name="Theme.MaterialComponents.DayNight.BottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog"/>
    <style name="Theme.MaterialComponents.DayNight.Bridge" parent="Theme.MaterialComponents.Light.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar" parent="Theme.MaterialComponents.Light.DarkActionBar"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" parent="Theme.MaterialComponents.Light.DarkActionBar.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog" parent="Theme.MaterialComponents.Light.Dialog"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert" parent="Theme.MaterialComponents.Light.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" parent="Theme.MaterialComponents.Light.Dialog.Alert.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Bridge" parent="Theme.MaterialComponents.Light.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" parent="Theme.MaterialComponents.Light.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" parent="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" parent="Theme.MaterialComponents.Light.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" parent="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DialogWhenLarge" parent="Theme.MaterialComponents.Light.DialogWhenLarge"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar" parent="Theme.MaterialComponents.Light.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge"/>
    <style name="Theme.MaterialComponents.Dialog" parent="Base.Theme.MaterialComponents.Dialog"/>
    <style name="Theme.MaterialComponents.Dialog.Alert" parent="Base.Theme.MaterialComponents.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.Dialog.Alert.Bridge" parent="Base.Theme.MaterialComponents.Dialog.Bridge">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Dialog.Bridge" parent="Base.Theme.MaterialComponents.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.Dialog.FixedSize" parent="Base.Theme.MaterialComponents.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" parent="Base.Theme.MaterialComponents.Dialog.Bridge">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Dialog.MinWidth" parent="Base.Theme.MaterialComponents.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" parent="Base.Theme.MaterialComponents.Dialog.Bridge">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.MaterialComponents.DialogWhenLarge" parent="Base.Theme.MaterialComponents.DialogWhenLarge">
  </style>
    <style name="Theme.MaterialComponents.Light" parent="Base.Theme.MaterialComponents.Light"/>
    <style name="Theme.MaterialComponents.Light.BarSize">
    <item name="actionBarSize">@dimen/action_bar_size</item>
  </style>
    <style name="Theme.MaterialComponents.Light.BottomSheetDialog" parent="Theme.MaterialComponents.Light.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
  </style>
    <style name="Theme.MaterialComponents.Light.Bridge" parent="Base.Theme.MaterialComponents.Light.Bridge"/>
    <style name="Theme.MaterialComponents.Light.DarkActionBar" parent="Base.Theme.MaterialComponents.Light.DarkActionBar"/>
    <style name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge"/>
    <style name="Theme.MaterialComponents.Light.Dialog" parent="Base.Theme.MaterialComponents.Light.Dialog"/>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert" parent="Base.Theme.MaterialComponents.Light.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" parent="Base.Theme.MaterialComponents.Light.Dialog.Bridge">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Bridge" parent="Base.Theme.MaterialComponents.Light.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.Light.Dialog.FixedSize" parent="Base.Theme.MaterialComponents.Light.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" parent="Base.Theme.MaterialComponents.Light.Dialog.Bridge">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth" parent="Base.Theme.MaterialComponents.Light.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" parent="Base.Theme.MaterialComponents.Light.Dialog.Bridge">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Light.DialogWhenLarge" parent="Base.Theme.MaterialComponents.Light.DialogWhenLarge">
  </style>
    <style name="Theme.MaterialComponents.Light.LargeTouch">
    <item name="minTouchTargetSize">@dimen/mtrl_large_touch_target</item>
  </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar.Bridge" parent="Theme.MaterialComponents.Light.Bridge">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.NoActionBar.Bridge" parent="Theme.MaterialComponents.Bridge">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="ThemeOverlay.Design.TextInputEditText" parent=""/>
    <style name="ThemeOverlay.Material3" parent="ThemeOverlay.MaterialComponents"/>
    <style name="ThemeOverlay.Material3.ActionBar" parent="ThemeOverlay.MaterialComponents.ActionBar"/>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView" parent="Base.ThemeOverlay.Material3.AutoCompleteTextView">
    <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView.FilledBox">
    <item name="autoCompleteTextViewStyle">@style/Widget.Material3.AutoCompleteTextView.FilledBox</item>
  </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense">
    <item name="autoCompleteTextViewStyle">@style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense</item>
  </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox">
    <item name="autoCompleteTextViewStyle">@style/Widget.Material3.AutoCompleteTextView.OutlinedBox</item>
  </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense">
    <item name="autoCompleteTextViewStyle">@style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense</item>
  </style>
    <style name="ThemeOverlay.Material3.BottomAppBar" parent="">
    <item name="colorControlNormal">?attr/colorOnSurface</item>
    <item name="actionMenuTextColor">?attr/colorOnSurface</item>
  </style>
    <style name="ThemeOverlay.Material3.BottomSheetDialog" parent="Base.ThemeOverlay.Material3.BottomSheetDialog"/>
    <style name="ThemeOverlay.Material3.Button" parent="">
    <!-- The colors used by the color selectors -->
    <item name="colorOnContainer">?attr/colorOnPrimary</item>
    <item name="colorContainer">?attr/colorPrimary</item>
  </style>
    <style name="ThemeOverlay.Material3.Button.ElevatedButton" parent="ThemeOverlay.Material3.Button.TextButton">
    <!-- The colors used by the color selectors -->
    <item name="colorContainer">?attr/colorSurface</item>
  </style>
    <style name="ThemeOverlay.Material3.Button.TextButton" parent="">
    <!-- The colors used by the color selectors -->
    <item name="colorOnContainer">?attr/colorPrimary</item>
    <item name="colorContainer">@android:color/transparent</item>
  </style>
    <style name="ThemeOverlay.Material3.Button.TextButton.Snackbar">
    <!-- The colors used by the color selectors -->
    <item name="colorOnContainer">?attr/colorPrimaryInverse</item>
  </style>
    <style name="ThemeOverlay.Material3.Button.TonalButton">
    <!-- The colors used by the color selectors -->
    <item name="colorOnContainer">?attr/colorOnSecondaryContainer</item>
    <item name="colorContainer">?attr/colorSecondaryContainer</item>
  </style>
    <style name="ThemeOverlay.Material3.Chip" parent="">
    <!-- To tint vector drawables used as leading icons -->
    <item name="colorControlNormal">@color/m3_chip_text_color</item>
  </style>
    <style name="ThemeOverlay.Material3.Chip.Assist" parent="">
    <!-- To tint vector drawables used as leading icons -->
    <item name="colorControlNormal">@color/m3_assist_chip_icon_tint_color</item>
  </style>
    <style name="ThemeOverlay.Material3.Dark" parent="ThemeOverlay.MaterialComponents.Dark">
    <!-- Color palettes -->
    <item name="android:colorBackground">@color/m3_sys_color_dark_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_dark_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_dark_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_dark_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_dark_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_dark_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_dark_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_dark_inverse_on_surface</item>
    <item name="colorOutline">@color/m3_sys_color_dark_outline</item>
    <item name="colorError">@color/m3_sys_color_dark_error</item>
    <item name="colorOnError">@color/m3_sys_color_dark_on_error</item>
    <item name="colorErrorContainer">@color/m3_sys_color_dark_error_container</item>
    <item name="colorOnErrorContainer">@color/m3_sys_color_dark_on_error_container</item>

    <!-- Default Framework Text Colors. -->
    <item name="android:textColorPrimary">@color/m3_dark_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_dark_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_dark_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_dark_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_highlighted_text</item>
    <item name="android:textColorLink">?attr/colorPrimary</item>
    <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_dark_default_color_primary_text</item>
  </style>
    <style name="ThemeOverlay.Material3.Dark.ActionBar" parent="ThemeOverlay.MaterialComponents.Dark.ActionBar"/>
    <style name="ThemeOverlay.Material3.DayNight.BottomSheetDialog" parent="ThemeOverlay.Material3.BottomSheetDialog"/>
    <style name="ThemeOverlay.Material3.Dialog" parent="Base.ThemeOverlay.Material3.Dialog"/>
    <style name="ThemeOverlay.Material3.Dialog.Alert" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
    <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
    <item name="android:windowElevation" ns1:ignore="NewApi">@dimen/m3_alert_dialog_elevation</item>
  </style>
    <style name="ThemeOverlay.Material3.DynamicColors.Dark" parent="ThemeOverlay.Material3.Dark"/>
    <style name="ThemeOverlay.Material3.DynamicColors.DayNight" parent="ThemeOverlay.Material3.DynamicColors.Light"/>
    <style name="ThemeOverlay.Material3.DynamicColors.Light" parent="ThemeOverlay.Material3.Light"/>
    <style name="ThemeOverlay.Material3.FloatingActionButton.Primary" parent="">
    <item name="colorContainer">?attr/colorPrimaryContainer</item>
    <item name="colorOnContainer">?attr/colorOnPrimaryContainer</item>
  </style>
    <style name="ThemeOverlay.Material3.FloatingActionButton.Secondary" parent="">
    <item name="colorContainer">?attr/colorSecondaryContainer</item>
    <item name="colorOnContainer">?attr/colorOnSecondaryContainer</item>
  </style>
    <style name="ThemeOverlay.Material3.FloatingActionButton.Surface" parent="">
    <item name="colorContainer">?attr/colorSurface</item>
    <item name="colorOnContainer">?attr/colorPrimary</item>
  </style>
    <style name="ThemeOverlay.Material3.FloatingActionButton.Tertiary" parent="">
    <item name="colorContainer">?attr/colorTertiaryContainer</item>
    <item name="colorOnContainer">?attr/colorOnTertiaryContainer</item>
  </style>
    <style name="ThemeOverlay.Material3.Light" parent="ThemeOverlay.MaterialComponents.Light">
    <!-- Color palettes -->
    <item name="android:colorBackground">@color/m3_sys_color_light_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_light_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_light_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_light_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_light_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_light_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_light_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_light_inverse_on_surface</item>
    <item name="colorOutline">@color/m3_sys_color_light_outline</item>
    <item name="colorError">@color/m3_sys_color_light_error</item>
    <item name="colorOnError">@color/m3_sys_color_light_on_error</item>
    <item name="colorErrorContainer">@color/m3_sys_color_light_error_container</item>
    <item name="colorOnErrorContainer">@color/m3_sys_color_light_on_error_container</item>

    <!-- Default Framework Text Colors. -->
    <item name="android:textColorPrimary">@color/m3_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_dark_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_dark_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dark_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_dark_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_dark_highlighted_text</item>
    <item name="android:textColorLink">?attr/colorPrimary</item>
    <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_default_color_primary_text</item>
  </style>
    <style name="ThemeOverlay.Material3.MaterialAlertDialog" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
    <item name="alertDialogStyle">@style/MaterialAlertDialog.Material3</item>
    <item name="android:windowElevation" ns1:ignore="NewApi">@dimen/m3_alert_dialog_elevation</item>
    <item name="android:checkedTextViewStyle" ns1:ignore="NewApi">@style/Widget.Material3.CheckedTextView</item>
    <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
    <item name="buttonBarPositiveButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
    <item name="buttonBarNegativeButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
    <item name="buttonBarNeutralButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
    <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.Material3.Title.Panel</item>
    <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.Material3.Title.Icon</item>
    <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.Material3.Title.Text</item>
    <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.Material3.Body.Text</item>
  </style>
    <style name="ThemeOverlay.Material3.MaterialAlertDialog.Centered">
    <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked</item>
    <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked</item>
    <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.Material3.Title.Text.CenterStacked</item>
  </style>
    <style name="ThemeOverlay.Material3.MaterialCalendar" parent="ThemeOverlay.Material3.Dialog">
    <item name="android:windowElevation" ns1:ignore="NewApi">@dimen/m3_datepicker_elevation</item>
    <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar</item>

    <!-- Header Styles -->
    <item name="materialCalendarHeaderLayout">@style/Widget.Material3.MaterialCalendar.HeaderLayout</item>
    <item name="materialCalendarHeaderDivider">@style/Widget.Material3.MaterialCalendar.HeaderDivider</item>
    <item name="materialCalendarHeaderTitle">@style/Widget.Material3.MaterialCalendar.HeaderTitle</item>
    <item name="materialCalendarHeaderSelection">@style/Widget.Material3.MaterialCalendar.HeaderSelection</item>
    <item name="materialCalendarHeaderCancelButton">@style/Widget.Material3.MaterialCalendar.HeaderCancelButton</item>
    <item name="materialCalendarHeaderConfirmButton">@style/Widget.Material3.Button.TextButton</item>
    <item name="materialCalendarHeaderToggleButton">@style/Widget.Material3.MaterialCalendar.HeaderToggleButton</item>

    <!-- Navigation Styles -->
    <item name="materialCalendarYearNavigationButton">@style/Widget.Material3.MaterialCalendar.YearNavigationButton</item>
    <item name="materialCalendarMonthNavigationButton">@style/Widget.Material3.MaterialCalendar.MonthNavigationButton</item>

    <!-- Grid Styles -->
    <item name="materialCalendarDayOfWeekLabel">@style/Widget.Material3.MaterialCalendar.DayOfWeekLabel</item>
    <item name="materialCalendarDay">@style/Widget.Material3.MaterialCalendar.DayTextView</item>
    <item name="materialCalendarMonth">@style/Widget.Material3.MaterialCalendar.MonthTextView</item>

    <!-- Action Styles -->
    <item name="buttonBarPositiveButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
    <item name="buttonBarNegativeButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
  </style>
    <style name="ThemeOverlay.Material3.MaterialCalendar.Fullscreen">
    <item name="android:windowElevation" ns1:ignore="NewApi">@dimen/m3_datepicker_elevation</item>
    <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarHeaderSelection">@style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen</item>
  </style>
    <style name="ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton" parent="ThemeOverlay.Material3.Button.TextButton">
    <item name="colorOnContainer">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="ThemeOverlay.Material3.MaterialTimePicker" parent="ThemeOverlay.Material3.Dialog">
    <item name="android:windowElevation" ns1:ignore="NewApi">@dimen/m3_timepicker_window_elevation</item>
    <item name="chipStyle">@style/Widget.Material3.MaterialTimePicker.Display</item>
    <item name="textInputStyle">@style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout</item>
    <item name="materialDisplayDividerStyle">@style/Widget.Material3.MaterialTimePicker.Display.Divider</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.Material3.MaterialTimePicker.Button</item>
    <item name="materialClockStyle">@style/Widget.Material3.MaterialTimePicker.Clock</item>
    <item name="materialTimePickerStyle">@style/Widget.Material3.MaterialTimePicker</item>
    <item name="imageButtonStyle">@style/Widget.Material3.MaterialTimePicker.ImageButton</item>
    <item name="materialTimePickerTitleStyle">@style/TextAppearance.Material3.MaterialTimePicker.Title</item>
  </style>
    <style name="ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText" parent="ThemeOverlay.Material3.TextInputEditText.OutlinedBox">
    <item name="editTextStyle">@style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText</item>
    <!-- NOTE: the only text view used in TimePicker text input is custom helper text view. -->
    <item name="android:textViewStyle">@style/Widget.Material3.MaterialTimePicker.Display.HelperText</item>
  </style>
    <style name="ThemeOverlay.Material3.NavigationView" parent="">
    <item name="android:listDivider">?attr/colorOutline</item>
  </style>
    <style name="ThemeOverlay.Material3.Snackbar" parent="">
    <item name="colorOnSurface">?attr/colorOnBackground</item>
  </style>
    <style name="ThemeOverlay.Material3.TextInputEditText" parent="Base.ThemeOverlay.Material3.TextInputEditText">
    <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="ThemeOverlay.Material3.TextInputEditText.FilledBox">
    <item name="editTextStyle">@style/Widget.Material3.TextInputEditText.FilledBox</item>
  </style>
    <style name="ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense">
    <item name="editTextStyle">@style/Widget.Material3.TextInputEditText.FilledBox.Dense</item>
  </style>
    <style name="ThemeOverlay.Material3.TextInputEditText.OutlinedBox">
    <item name="editTextStyle">@style/Widget.Material3.TextInputEditText.OutlinedBox</item>
  </style>
    <style name="ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense">
    <item name="editTextStyle">@style/Widget.Material3.TextInputEditText.OutlinedBox.Dense</item>
  </style>
    <style name="ThemeOverlay.Material3.Toolbar.Surface" parent="">
    <item name="actionMenuTextColor">?attr/colorOnSurfaceVariant</item>
    <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon" parent="">
    <item name="colorControlNormal">?attr/colorSecondary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents" parent="ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.MaterialComponents.ActionBar" parent="ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Primary" parent="ThemeOverlay.AppCompat.ActionBar">
    <item name="android:textColorPrimary">?attr/colorOnPrimary</item>
    <item name="android:textColorSecondary">@color/material_on_primary_emphasis_medium</item>
    <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
    <item name="colorControlNormal">?attr/colorOnPrimary</item>
    <!-- Used to tint the ActionMode background and preserve the underline. -->
    <item name="android:colorBackground">?attr/colorPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Surface" parent="ThemeOverlay.AppCompat.ActionBar">
    <item name="android:textColorPrimary">@color/material_on_surface_emphasis_high_type</item>
    <item name="android:textColorSecondary">@color/material_on_surface_emphasis_medium</item>
    <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
    <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    <!-- Used to tint the ActionMode background and preserve the underline. -->
    <item name="android:colorBackground">?attr/colorSurface</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" parent="">
    <item name="colorControlActivated">?attr/colorPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox">
    <item name="autoCompleteTextViewStyle">
      @style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense">
    <item name="autoCompleteTextViewStyle">
      @style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox">
    <item name="autoCompleteTextViewStyle">
      @style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense">
    <item name="autoCompleteTextViewStyle">
      @style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" parent="">
    <item name="colorControlNormal">?attr/colorOnPrimary</item>
    <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" parent="">
    <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog"/>
    <style name="ThemeOverlay.MaterialComponents.Dark" parent="ThemeOverlay.AppCompat.Dark">
    <item name="android:colorBackground">@color/design_dark_default_color_background</item>
    <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
    <item name="colorSurface">@color/design_dark_default_color_surface</item>
    <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
    <item name="colorError">@color/design_dark_default_color_error</item>
    <item name="colorOnError">@color/design_dark_default_color_on_error</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Dark.ActionBar" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" parent="ThemeOverlay.MaterialComponents.BottomSheetDialog"/>
    <style name="ThemeOverlay.MaterialComponents.Dialog" parent="Base.ThemeOverlay.MaterialComponents.Dialog"/>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert" parent="Base.ThemeOverlay.MaterialComponents.Dialog.Alert"/>
    <style name="ThemeOverlay.MaterialComponents.Light" parent="ThemeOverlay.AppCompat.Light">
    <item name="android:colorBackground">@color/design_default_color_background</item>
    <item name="colorOnBackground">@color/design_default_color_on_background</item>
    <item name="colorSurface">@color/design_default_color_surface</item>
    <item name="colorOnSurface">@color/design_default_color_on_surface</item>
    <item name="colorError">@color/design_default_color_error</item>
    <item name="colorOnError">@color/design_default_color_on_error</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
    <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel</item>
    <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon</item>
    <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" parent="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
    <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked</item>
    <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked</item>
    <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog"/>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar"/>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner"/>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar" parent="ThemeOverlay.MaterialComponents.Dialog">
    <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>

    <!-- Header Styles -->
    <item name="materialCalendarHeaderLayout">@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout</item>
    <item name="materialCalendarHeaderDivider">@style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider</item>
    <item name="materialCalendarHeaderTitle">@style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle</item>
    <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection</item>
    <item name="materialCalendarHeaderCancelButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton</item>
    <item name="materialCalendarHeaderConfirmButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton</item>
    <item name="materialCalendarHeaderToggleButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton</item>

    <!-- Navigation Styles -->
    <item name="materialCalendarYearNavigationButton">@style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton</item>
    <item name="materialCalendarMonthNavigationButton">@style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton</item>

    <!-- Grid Styles -->
    <item name="materialCalendarDayOfWeekLabel">@style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel</item>
    <item name="materialCalendarDay">@style/Widget.MaterialComponents.MaterialCalendar.DayTextView</item>
    <item name="materialCalendarMonth">@style/Widget.MaterialComponents.MaterialCalendar.MonthTextView</item>

    <!-- Action Styles -->
    <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
    <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText" parent="ThemeOverlay.Design.TextInputEditText">
    <item name="colorControlActivated">?attr/colorPrimary</item>
    <item name="android:editTextBackground">@null</item>
    <item name="editTextBackground">@null</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker" parent="ThemeOverlay.MaterialComponents.Dialog">
    <item name="chipStyle">@style/Widget.MaterialComponents.TimePicker.Display</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout</item>
    <item name="materialDisplayDividerStyle">@style/Widget.MaterialComponents.TimePicker.Display.Divider</item>
    <item name="materialClockStyle">@style/Widget.MaterialComponents.TimePicker.Clock</item>
    <item name="imageButtonStyle">@style/Widget.MaterialComponents.TimePicker.ImageButton</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.TimePicker.Button</item>
    <item name="materialTimePickerTitleStyle">@style/TextAppearance.MaterialComponents.TimePicker.Title</item>
    <item name="elevationOverlayEnabled">false</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker.Display" parent="">
    <item name="elevationOverlayEnabled">false</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText" parent="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText</item>
    <!-- NOTE: the only text view used in TimePicker text input is custom helper text view. -->
    <item name="android:textViewStyle">@style/Widget.MaterialComponents.TimePicker.Display.HelperText</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary" parent="">
    <item name="colorControlNormal">?attr/colorOnSurface</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Primary" parent="">
    <item name="colorControlNormal">?attr/colorOnPrimary</item>
    <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Surface" parent="">
    <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="ThemeOverlayColorAccentRed" ns1:ignore="NewApi">
    <item name="android:colorAccent">#FFFF0000</item>
  </style>
    <style name="Widget.Design.AppBarLayout" parent="android:Widget">
    <item name="android:background">?attr/colorPrimary</item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/design_appbar_state_list_animator
    </item>
    <item name="android:keyboardNavigationCluster" ns1:ignore="NewApi">true</item>
    <item name="android:touchscreenBlocksFocus" ns1:ignore="NewApi">true</item>
  </style>
    <style name="Widget.Design.BottomNavigationView" parent="">
    <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
    <item name="enforceTextAppearance">false</item>
    <item name="enforceMaterialTheme">false</item>
    <item name="itemBackground">?attr/selectableItemBackgroundBorderless</item>
    <item name="itemHorizontalTranslationEnabled">true</item>
    <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
    <item name="labelVisibilityMode">auto</item>
    <item name="itemPaddingTop">@dimen/design_bottom_navigation_margin</item>
    <item name="itemPaddingBottom">@dimen/design_bottom_navigation_label_padding</item>
    <item name="itemActiveIndicatorStyle">@null</item>
    <item name="android:minHeight">@dimen/design_bottom_navigation_height</item>
  </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="android:Widget">
    <item name="enforceMaterialTheme">false</item>
    <item name="android:background">?android:attr/colorBackground</item>
    <item name="android:elevation" ns1:ignore="NewApi">
      @dimen/design_bottom_sheet_modal_elevation
    </item>
    <item name="behavior_peekHeight">auto</item>
    <item name="behavior_hideable">true</item>
    <item name="behavior_skipCollapsed">false</item>
    <item name="shapeAppearance">@null</item>
    <item name="shapeAppearanceOverlay">@null</item>
    <item name="backgroundTint">?android:attr/colorBackground</item>
  </style>
    <style name="Widget.Design.CollapsingToolbar" parent="android:Widget">
    <item name="expandedTitleMargin">32dp</item>
    <item name="statusBarScrim">?attr/colorPrimaryDark</item>
  </style>
    <style name="Widget.Design.FloatingActionButton" parent="android:Widget">
    <item name="android:background">@drawable/design_fab_background</item>
    <item name="android:clickable">true</item>
    <item name="android:focusable">true</item>
    <item name="backgroundTint">?attr/colorAccent</item>
    <item name="fabSize">auto</item>
    <item name="elevation">@dimen/design_fab_elevation</item>
    <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
    <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
    <item name="rippleColor">?attr/colorControlHighlight</item>
    <item name="borderWidth">@dimen/design_fab_border_width</item>
    <item name="maxImageSize">@dimen/design_fab_image_size</item>
    <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
  </style>
    <style name="Widget.Design.NavigationView" parent="Widget.Design.ScrimInsetsFrameLayout">
    <item name="elevation">@dimen/design_navigation_elevation</item>
    <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
    <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
    <item name="android:background">?android:attr/windowBackground</item>
    <item name="android:fitsSystemWindows">true</item>
    <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
    <item name="subheaderInsetStart">?attr/listPreferredItemPaddingLeft</item>
    <item name="subheaderInsetEnd">?attr/listPreferredItemPaddingRight</item>
    <item name="drawerLayoutCornerSize">0dp</item>
  </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout" parent="">
    <item name="insetForeground">#4000</item>
  </style>
    <style name="Widget.Design.Snackbar" parent="android:Widget">
    <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
    <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
    <item name="android:background">@drawable/design_snackbar_background</item>
    <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
    <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
    <item name="elevation">@dimen/design_snackbar_elevation</item>
    <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
    <item name="animationMode">slide</item>
    <item name="actionTextColorAlpha">@dimen/design_snackbar_action_text_color_alpha</item>
  </style>
    <style name="Widget.Design.TabLayout" parent="Base.Widget.Design.TabLayout">
    <item name="tabGravity">fill</item>
    <item name="tabMode">fixed</item>
    <item name="tabIndicatorFullWidth">true</item>
  </style>
    <style name="Widget.Design.TextInputEditText" parent="Widget.AppCompat.EditText">
    <item name="enforceMaterialTheme">false</item>
    <item name="enforceTextAppearance">false</item>
  </style>
    <style name="Widget.Design.TextInputLayout" parent="android:Widget">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Design.TextInputEditText</item>
    <item name="enforceMaterialTheme">false</item>
    <item name="enforceTextAppearance">false</item>

    <item name="boxBackgroundMode">none</item>
    <item name="boxStrokeColor">@color/design_box_stroke_color</item>
    <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
    <item name="passwordToggleTint">@color/design_icon_tint</item>
    <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
    <item name="errorIconDrawable">@null</item>
    <item name="endIconTint">@color/design_icon_tint</item>
    <item name="startIconTint">@color/design_icon_tint</item>

    <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
    <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
    <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
    <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
    <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
    <item name="placeholderTextAppearance">@style/TextAppearance.Design.Placeholder</item>
    <item name="prefixTextAppearance">@style/TextAppearance.Design.Prefix</item>
    <item name="suffixTextAppearance">@style/TextAppearance.Design.Suffix</item>

    <item name="counterTextColor">@null</item>
    <item name="counterOverflowTextColor">@null</item>
    <item name="errorTextColor">@null</item>
    <item name="helperTextTextColor">@null</item>
    <item name="hintTextColor">@null</item>
    <item name="placeholderTextColor">@null</item>
    <item name="prefixTextColor">@null</item>
    <item name="suffixTextColor">@null</item>

    <item name="shapeAppearance">@null</item>
    <item name="shapeAppearanceOverlay">@null</item>
  </style>
    <style name="Widget.Material3.ActionBar.Solid" parent="Base.Widget.Material3.ActionBar.Solid"/>
    <style name="Widget.Material3.ActionMode" parent="Base.Widget.Material3.ActionMode"/>
    <style name="Widget.Material3.AppBarLayout" parent="Widget.MaterialComponents.AppBarLayout.Surface">
    <item name="liftOnScroll">true</item>

    <!-- On newer API levels, hide shadows while keeping elevation. -->
    <item name="android:outlineAmbientShadowColor" ns1:ignore="NewApi">@android:color/transparent</item>
    <item name="android:outlineSpotShadowColor" ns1:ignore="NewApi">@android:color/transparent</item>
  </style>
    <style name="Widget.Material3.AutoCompleteTextView.FilledBox" parent="Widget.MaterialComponents.AutoCompleteTextView.FilledBox">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
  </style>
    <style name="Widget.Material3.AutoCompleteTextView.FilledBox.Dense" parent="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
  </style>
    <style name="Widget.Material3.AutoCompleteTextView.OutlinedBox" parent="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
  </style>
    <style name="Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense" parent="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
  </style>
    <style name="Widget.Material3.Badge" parent="Widget.MaterialComponents.Badge">
    <!-- Badge "dot" without text -->
    <item name="badgeRadius">@dimen/m3_badge_radius</item>
    <item name="horizontalOffset">@dimen/m3_badge_horizontal_offset</item>
    <item name="verticalOffset">@dimen/m3_badge_vertical_offset</item>

    <!-- Badge with text -->
    <item name="badgeWithTextRadius">@dimen/m3_badge_with_text_radius</item>
    <item name="horizontalOffsetWithText">@dimen/m3_badge_with_text_horizontal_offset</item>
    <item name="verticalOffsetWithText">@dimen/m3_badge_with_text_vertical_offset</item>
  </style>
    <style name="Widget.Material3.BottomAppBar" parent="Widget.MaterialComponents.BottomAppBar">
    <item name="fabAnimationMode">slide</item>
    <item name="fabCradleMargin">@dimen/m3_bottomappbar_fab_cradle_margin</item>
    <item name="fabCradleRoundedCornerRadius">
      @dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius
    </item>
    <item name="fabCradleVerticalOffset">
      @dimen/m3_bottomappbar_fab_cradle_vertical_offset
    </item>

    <item name="elevation">@dimen/m3_sys_elevation_level2</item>

    <item name="backgroundTint">?attr/colorSurface</item>
    <item name="navigationIconTint">?attr/colorOnSurfaceVariant</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.BottomAppBar</item>
  </style>
    <style name="Widget.Material3.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
    <item name="android:background">?attr/colorSurface</item>
    <item name="backgroundTint">@null</item>
    <item name="elevation">@dimen/m3_sys_elevation_level2</item>
    <item name="itemActiveIndicatorStyle">@style/Widget.Material3.BottomNavigationView.ActiveIndicator</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceTitleSmall</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceTitleSmall</item>
    <item name="itemRippleColor">@color/m3_navigation_bar_ripple_color_selector</item>
    <item name="itemIconTint">@color/m3_navigation_bar_item_with_indicator_icon_tint</item>
    <item name="itemTextColor">@color/m3_navigation_bar_item_with_indicator_label_tint</item>
    <item name="itemPaddingTop">@dimen/m3_bottom_nav_item_padding_top</item>
    <item name="itemPaddingBottom">@dimen/m3_bottom_nav_item_padding_bottom</item>
    <item name="android:minHeight">@dimen/m3_bottom_nav_min_height</item>
  </style>
    <style name="Widget.Material3.BottomNavigationView.ActiveIndicator" parent="">
    <item name="android:width">@dimen/m3_bottom_nav_item_active_indicator_width</item>
    <item name="android:height">@dimen/m3_bottom_nav_item_active_indicator_height</item>
    <item name="marginHorizontal">@dimen/m3_bottom_nav_item_active_indicator_margin_horizontal</item>
    <item name="shapeAppearance">@style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator</item>
    <item name="android:color">?attr/colorSecondaryContainer</item>
  </style>
    <style name="Widget.Material3.BottomSheet" parent="Widget.MaterialComponents.BottomSheet">
    <item name="android:elevation" ns1:ignore="NewApi">@dimen/m3_bottom_sheet_elevation</item>
  </style>
    <style name="Widget.Material3.BottomSheet.Modal">
    <item name="android:elevation" ns1:ignore="NewApi">@dimen/m3_bottom_sheet_modal_elevation</item>
  </style>
    <style name="Widget.Material3.Button" parent="Widget.MaterialComponents.Button">
    <item name="enforceTextAppearance">false</item>
    <item name="android:maxWidth">@dimen/m3_btn_max_width</item>
    <item name="android:paddingLeft">@dimen/m3_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_padding_right</item>
    <item name="android:paddingTop">@dimen/m3_btn_padding_top</item>
    <item name="android:paddingBottom">@dimen/m3_btn_padding_bottom</item>
    <item name="android:insetTop">@dimen/m3_btn_inset</item>
    <item name="android:insetBottom">@dimen/m3_btn_inset</item>
    <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.Button</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button</item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">@animator/m3_btn_state_list_anim</item>
    <item name="elevation">@dimen/m3_btn_elevation</item>
    <item name="android:textColor">@color/m3_button_foreground_color_selector</item>
    <item name="iconTint">@color/m3_button_foreground_color_selector</item>
    <item name="iconSize">18dp</item>
    <item name="backgroundTint">@color/m3_button_background_color_selector</item>
    <item name="rippleColor">@color/m3_button_ripple_color_selector</item>
  </style>
    <style name="Widget.Material3.Button.ElevatedButton">
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_btn_elevated_btn_state_list_anim
    </item>
    <item name="android:textColor">@color/m3_text_button_foreground_color_selector</item>
    <item name="elevation">@dimen/m3_btn_elevated_btn_elevation</item>
    <item name="iconTint">@color/m3_text_button_foreground_color_selector</item>
    <item name="rippleColor">@color/m3_text_button_ripple_color_selector</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.ElevatedButton</item>
  </style>
    <style name="Widget.Material3.Button.ElevatedButton.Icon">
    <item name="android:paddingLeft">@dimen/m3_btn_icon_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_icon_btn_padding_right</item>
  </style>
    <style name="Widget.Material3.Button.Icon">
    <item name="android:paddingLeft">@dimen/m3_btn_icon_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_icon_btn_padding_right</item>
  </style>
    <style name="Widget.Material3.Button.IconButton" parent="Widget.Material3.Button.TextButton">
    <item name="android:minWidth">@dimen/m3_btn_icon_only_min_width</item>
    <item name="android:paddingLeft">@dimen/m3_btn_icon_only_default_padding</item>
    <item name="android:paddingRight">@dimen/m3_btn_icon_only_default_padding</item>
    <item name="android:paddingTop">@dimen/m3_btn_icon_only_default_padding</item>
    <item name="android:paddingBottom">@dimen/m3_btn_icon_only_default_padding</item>
    <item name="android:insetLeft">@dimen/m3_btn_inset</item>
    <item name="android:insetRight">@dimen/m3_btn_inset</item>
    <item name="iconSize">@dimen/m3_btn_icon_only_default_size</item>
    <item name="iconPadding">@dimen/m3_btn_icon_only_icon_padding</item>
  </style>
    <style name="Widget.Material3.Button.OutlinedButton" parent="Widget.Material3.Button.TextButton">
    <item name="android:paddingLeft">@dimen/m3_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_padding_right</item>
    <item name="strokeColor">@color/m3_button_outline_color_selector</item>
    <item name="strokeWidth">@dimen/m3_btn_stroke_size</item>
  </style>
    <style name="Widget.Material3.Button.OutlinedButton.Icon">
    <item name="android:paddingLeft">@dimen/m3_btn_icon_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_icon_btn_padding_right</item>
  </style>
    <style name="Widget.Material3.Button.TextButton" parent="Widget.MaterialComponents.Button.TextButton">
    <item name="enforceTextAppearance">false</item>
    <item name="android:maxWidth">@dimen/m3_btn_max_width</item>
    <item name="android:paddingLeft">@dimen/m3_btn_text_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_text_btn_padding_right</item>
    <item name="android:paddingTop">@dimen/m3_btn_padding_top</item>
    <item name="android:paddingBottom">@dimen/m3_btn_padding_bottom</item>
    <item name="android:insetTop">@dimen/m3_btn_inset</item>
    <item name="android:insetBottom">@dimen/m3_btn_inset</item>
    <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.Button</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.TextButton</item>
    <item name="android:textColor">@color/m3_text_button_foreground_color_selector</item>
    <item name="iconTint">@color/m3_text_button_foreground_color_selector</item>
    <item name="backgroundTint">@color/m3_text_button_background_color_selector</item>
    <item name="rippleColor">@color/m3_text_button_ripple_color_selector</item>
  </style>
    <style name="Widget.Material3.Button.TextButton.Dialog">
    <item name="android:minWidth">@dimen/m3_btn_dialog_btn_min_width</item>
    <item name="android:layout_marginLeft">@dimen/m3_btn_dialog_btn_spacing</item>
    <item name="android:layout_marginStart">@dimen/m3_btn_dialog_btn_spacing</item>
    <item name="android:lines">1</item>
    <item name="android:ellipsize">end</item>
    <item name="android:singleLine">true</item>
  </style>
    <style name="Widget.Material3.Button.TextButton.Dialog.Flush">
    <item name="android:layout_marginStart">0dp</item>
    <item name="android:layout_marginLeft">0dp</item>
  </style>
    <style name="Widget.Material3.Button.TextButton.Dialog.Icon">
    <item name="android:paddingLeft">@dimen/m3_btn_text_btn_icon_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_text_btn_icon_padding_right</item>
  </style>
    <style name="Widget.Material3.Button.TextButton.Icon">
    <item name="android:paddingLeft">@dimen/m3_btn_text_btn_icon_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_text_btn_icon_padding_right</item>
  </style>
    <style name="Widget.Material3.Button.TextButton.Snackbar">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.TextButton.Snackbar</item>
  </style>
    <style name="Widget.Material3.Button.TonalButton">
    <item name="rippleColor">@color/m3_tonal_button_ripple_color_selector</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.TonalButton</item>
  </style>
    <style name="Widget.Material3.Button.TonalButton.Icon">
    <item name="android:paddingLeft">@dimen/m3_btn_icon_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/m3_btn_icon_btn_padding_right</item>
  </style>
    <style name="Widget.Material3.Button.UnelevatedButton">
    <item name="android:stateListAnimator" ns1:ignore="NewApi">@animator/mtrl_btn_unelevated_state_list_anim</item>
    <item name="elevation">0dp</item>
  </style>
    <style name="Widget.Material3.CardView.Elevated" parent="Base.Widget.Material3.CardView">
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_card_elevated_state_list_anim
    </item>
    <item name="cardElevation">@dimen/m3_card_elevated_elevation</item>
    <item name="strokeWidth">0dp</item>
  </style>
    <style name="Widget.Material3.CardView.Filled" parent="Base.Widget.Material3.CardView">
    <item name="strokeWidth">0dp</item>
    <item name="cardBackgroundColor">?attr/colorSurfaceVariant</item>
  </style>
    <style name="Widget.Material3.CardView.Outlined" parent="Base.Widget.Material3.CardView"/>
    <style name="Widget.Material3.CheckedTextView" parent="Widget.MaterialComponents.CheckedTextView">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="Widget.Material3.Chip.Assist" parent="Base.Widget.Material3.Chip">
    <item name="android:checkable">false</item>
    <item name="chipIconVisible">true</item>
    <item name="checkedIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="android:textColor">@color/m3_chip_assist_text_color</item>

    <item name="chipMinHeight">32dp</item>

    <item name="chipStartPadding">8dp</item>
    <item name="iconStartPadding">0dp</item>
    <item name="iconEndPadding">0dp</item>
    <item name="textStartPadding">8dp</item>
    <item name="textEndPadding">8dp</item>
    <item name="closeIconStartPadding">0dp</item>
    <item name="closeIconEndPadding">0dp</item>
    <item name="chipEndPadding">8dp</item>

    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Chip.Assist</item>
  </style>
    <style name="Widget.Material3.Chip.Assist.Elevated">
    <item name="android:elevation" ns1:ignore="NewApi">
      @dimen/m3_chip_elevated_elevation
    </item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_elevated_chip_state_list_anim
    </item>
    <item name="chipStrokeColor">@android:color/transparent</item>
    <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
  </style>
    <style name="Widget.Material3.Chip.Filter" parent="Base.Widget.Material3.Chip">
    <item name="android:checkable">true</item>
    <item name="chipIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="checkedIcon">@drawable/ic_m3_chip_check</item>

    <item name="chipMinHeight">32dp</item>

    <item name="chipStartPadding">8dp</item>
    <item name="iconStartPadding">0dp</item>
    <!-- The existence of a chip icon takes away padding before the text. -->
    <item name="iconEndPadding">-2dp</item>
    <item name="textStartPadding">8dp</item>
    <item name="textEndPadding">6dp</item>
    <!-- The existence of a chip icon takes away padding after the text. -->
    <item name="closeIconStartPadding">-2dp</item>
    <item name="closeIconEndPadding">0dp</item>
    <item name="chipEndPadding">10dp</item>
  </style>
    <style name="Widget.Material3.Chip.Filter.Elevated">
    <item name="android:elevation" ns1:ignore="NewApi">
      @dimen/m3_chip_elevated_elevation</item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_elevated_chip_state_list_anim
    </item>
    <item name="chipStrokeColor">@android:color/transparent</item>
    <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
  </style>
    <style name="Widget.Material3.Chip.Input" parent="Base.Widget.Material3.Chip">
    <item name="android:checkable">true</item>

    <item name="chipMinHeight">32dp</item>
    <item name="chipIconSize">24dp</item>

    <item name="chipStartPadding">4dp</item>
    <item name="iconStartPadding">0dp</item>
    <!-- The existence of a chip icon takes away padding before the text. -->
    <item name="iconEndPadding">0dp</item>
    <item name="textStartPadding">8dp</item>
    <item name="textEndPadding">8dp</item>
    <item name="closeIconStartPadding">0dp</item>
    <item name="closeIconEndPadding">4dp</item>
    <item name="chipEndPadding">4dp</item>
  </style>
    <style name="Widget.Material3.Chip.Input.Elevated">
    <item name="android:elevation" ns1:ignore="NewApi">
      @dimen/m3_chip_elevated_elevation
    </item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_elevated_chip_state_list_anim
    </item>
    <item name="chipStrokeColor">@android:color/transparent</item>
    <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
  </style>
    <style name="Widget.Material3.Chip.Input.Icon" parent="Widget.Material3.Chip.Input">
    <item name="chipIconSize">@dimen/m3_chip_icon_size</item>
    <item name="iconStartPadding">4dp</item>
  </style>
    <style name="Widget.Material3.Chip.Input.Icon.Elevated">
    <item name="android:elevation" ns1:ignore="NewApi">
      @dimen/m3_chip_elevated_elevation
    </item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_elevated_chip_state_list_anim
    </item>
    <item name="chipStrokeColor">@android:color/transparent</item>
    <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
  </style>
    <style name="Widget.Material3.Chip.Suggestion" parent="Base.Widget.Material3.Chip">
    <item name="android:checkable">true</item>

    <item name="chipIconVisible">false</item>
    <item name="checkedIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="chipMinHeight">32dp</item>

    <item name="chipStartPadding">8dp</item>
    <item name="iconStartPadding">0dp</item>
    <!-- The existence of a chip icon takes away padding before the text. -->
    <item name="iconEndPadding">-2dp</item>
    <item name="textStartPadding">8dp</item>
    <item name="textEndPadding">6dp</item>
    <!-- The existence of a chip icon takes away padding after the text. -->
    <item name="closeIconStartPadding">-2dp</item>
    <item name="closeIconEndPadding">0dp</item>
    <item name="chipEndPadding">10dp</item>
  </style>
    <style name="Widget.Material3.Chip.Suggestion.Elevated">
    <item name="android:elevation" ns1:ignore="NewApi">
      @dimen/m3_chip_elevated_elevation
    </item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/m3_elevated_chip_state_list_anim
    </item>
    <item name="chipStrokeColor">@android:color/transparent</item>
    <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
  </style>
    <style name="Widget.Material3.ChipGroup" parent="Widget.MaterialComponents.ChipGroup">
    <item name="chipSpacingVertical">8dp</item>
  </style>
    <style name="Widget.Material3.CircularProgressIndicator" parent="Widget.MaterialComponents.CircularProgressIndicator"/>
    <style name="Widget.Material3.CircularProgressIndicator.ExtraSmall" parent="Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall"/>
    <style name="Widget.Material3.CircularProgressIndicator.Medium" parent="Widget.MaterialComponents.CircularProgressIndicator.Medium"/>
    <style name="Widget.Material3.CircularProgressIndicator.Small" parent="Widget.MaterialComponents.CircularProgressIndicator.Small"/>
    <style name="Widget.Material3.CollapsingToolbar" parent="Base.Widget.Material3.CollapsingToolbar">
    <item name="scrimVisibleHeightTrigger">@dimen/m3_appbar_scrim_height_trigger</item>
    <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadlineSmall</item>
  </style>
    <style name="Widget.Material3.CollapsingToolbar.Large" parent="Base.Widget.Material3.CollapsingToolbar">
    <item name="scrimVisibleHeightTrigger">@dimen/m3_appbar_scrim_height_trigger_large</item>
    <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadlineMedium</item>
  </style>
    <style name="Widget.Material3.CollapsingToolbar.Medium" parent="Base.Widget.Material3.CollapsingToolbar">
    <item name="scrimVisibleHeightTrigger">@dimen/m3_appbar_scrim_height_trigger_medium</item>
    <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadlineSmall</item>
  </style>
    <style name="Widget.Material3.CompoundButton.CheckBox" parent="Base.Widget.Material3.CompoundButton.CheckBox"/>
    <style name="Widget.Material3.CompoundButton.RadioButton" parent="Base.Widget.Material3.CompoundButton.RadioButton"/>
    <style name="Widget.Material3.CompoundButton.Switch" parent="Base.Widget.Material3.CompoundButton.Switch"/>
    <style name="Widget.Material3.DrawerLayout" parent="android:Widget">
    <item name="elevation">@dimen/m3_sys_elevation_level1</item>
  </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Icon.Primary" parent="Base.Widget.Material3.ExtendedFloatingActionButton.Icon">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Primary</item>
  </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary" parent="Base.Widget.Material3.ExtendedFloatingActionButton.Icon">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Secondary</item>
  </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Icon.Surface" parent="Base.Widget.Material3.ExtendedFloatingActionButton.Icon">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Surface</item>
  </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary" parent="Base.Widget.Material3.ExtendedFloatingActionButton.Icon">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Tertiary</item>
  </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Primary" parent="Base.Widget.Material3.ExtendedFloatingActionButton">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Primary</item>
  </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Secondary" parent="Base.Widget.Material3.ExtendedFloatingActionButton">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Secondary</item>
  </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Surface" parent="Base.Widget.Material3.ExtendedFloatingActionButton">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Surface</item>
  </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Tertiary" parent="Base.Widget.Material3.ExtendedFloatingActionButton">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Tertiary</item>
  </style>
    <style name="Widget.Material3.FloatingActionButton.Large.Primary" parent="Base.Widget.Material3.FloatingActionButton.Large">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Primary</item>
  </style>
    <style name="Widget.Material3.FloatingActionButton.Large.Secondary" parent="Base.Widget.Material3.FloatingActionButton.Large">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Secondary</item>
  </style>
    <style name="Widget.Material3.FloatingActionButton.Large.Surface" parent="Base.Widget.Material3.FloatingActionButton.Large">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Surface</item>
  </style>
    <style name="Widget.Material3.FloatingActionButton.Large.Tertiary" parent="Base.Widget.Material3.FloatingActionButton.Large">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Tertiary</item>
  </style>
    <style name="Widget.Material3.FloatingActionButton.Primary" parent="Base.Widget.Material3.FloatingActionButton">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Primary</item>
  </style>
    <style name="Widget.Material3.FloatingActionButton.Secondary" parent="Base.Widget.Material3.FloatingActionButton">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Secondary</item>
  </style>
    <style name="Widget.Material3.FloatingActionButton.Surface" parent="Base.Widget.Material3.FloatingActionButton">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Surface</item>
  </style>
    <style name="Widget.Material3.FloatingActionButton.Tertiary" parent="Base.Widget.Material3.FloatingActionButton">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Tertiary</item>
  </style>
    <style name="Widget.Material3.Light.ActionBar.Solid" parent="Base.Widget.Material3.Light.ActionBar.Solid"/>
    <style name="Widget.Material3.LinearProgressIndicator" parent="Widget.MaterialComponents.LinearProgressIndicator">
    <item name="trackColor">?attr/colorSurfaceVariant</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar" parent="Widget.MaterialComponents.MaterialCalendar">
    <item name="android:windowFullscreen">false</item>
    <item name="dayStyle">@style/Widget.Material3.MaterialCalendar.Day</item>
    <item name="dayInvalidStyle">@style/Widget.Material3.MaterialCalendar.Day.Invalid</item>
    <item name="daySelectedStyle">@style/Widget.Material3.MaterialCalendar.Day.Selected</item>
    <item name="dayTodayStyle">@style/Widget.Material3.MaterialCalendar.Day.Today</item>
    <item name="yearStyle">@style/Widget.Material3.MaterialCalendar.Year</item>
    <item name="yearSelectedStyle">@style/Widget.Material3.MaterialCalendar.Year.Selected</item>
    <item name="yearTodayStyle">@style/Widget.Material3.MaterialCalendar.Year.Today</item>
    <item name="rangeFillColor">?attr/colorSurfaceVariant</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Day" parent="Widget.Material3.MaterialCalendar.Item">
    <item name="android:width">@dimen/mtrl_calendar_day_width</item>
    <item name="android:height">@dimen/mtrl_calendar_day_height</item>
    <item name="android:insetTop">@dimen/mtrl_calendar_day_vertical_padding</item>
    <item name="android:insetBottom">@dimen/mtrl_calendar_day_vertical_padding</item>
    <item name="android:insetLeft">@dimen/mtrl_calendar_day_horizontal_padding</item>
    <item name="android:insetRight">@dimen/mtrl_calendar_day_horizontal_padding</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Day.Invalid">
    <item name="itemTextColor">@color/m3_calendar_item_disabled_text</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Day.Selected">
    <item name="itemFillColor">?attr/colorPrimary</item>
    <item name="itemTextColor">?attr/colorOnPrimary</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Day.Today">
    <item name="itemTextColor">?attr/colorPrimary</item>
    <item name="itemStrokeColor">?attr/colorPrimary</item>
    <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.DayOfWeekLabel" parent="Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel">
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.DayTextView" parent="Widget.MaterialComponents.MaterialCalendar.DayTextView">
    <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Fullscreen">
    <item name="android:windowFullscreen">true</item>
    <item name="shapeAppearanceOverlay">
      @style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen
    </item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderCancelButton" parent="Widget.Material3.Button.TextButton">
    <item name="iconTint">?attr/colorOnSurfaceVariant</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderDivider" parent="Widget.MaterialComponents.MaterialCalendar.HeaderDivider">
    <item name="android:visibility">gone</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderLayout" parent="Widget.MaterialComponents.MaterialCalendar.HeaderLayout">
    <item name="android:background">@android:color/transparent</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderSelection" parent="Widget.MaterialComponents.MaterialCalendar.HeaderSelection">
    <item name="android:textAppearance">?attr/textAppearanceHeadlineLarge</item>
    <item name="android:textColor">?attr/colorOnSurface</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen">
    <item name="android:textAppearance">?attr/textAppearanceHeadlineSmall</item>
    <item name="android:maxLines">1</item>
    <item name="autoSizeMaxTextSize">20sp</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderTitle" parent="Widget.MaterialComponents.MaterialCalendar.HeaderTitle">
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    <item name="android:textAppearance">?attr/textAppearanceLabelMedium</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderToggleButton" parent="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
    <item name="android:tint">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Item" parent="">
    <item name="itemFillColor">@android:color/transparent</item>
    <item name="itemTextColor">?attr/colorOnSurface</item>
    <item name="itemStrokeColor">@color/m3_calendar_item_stroke_color</item>
    <item name="itemStrokeWidth">1dp</item>
    <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.MonthNavigationButton" parent="Base.Widget.Material3.MaterialCalendar.NavigationButton">
    <item name="iconPadding">0dp</item>
    <item name="iconGravity">textStart</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.MonthTextView" parent="Widget.MaterialComponents.MaterialCalendar.MonthTextView">
    <item name="android:textAppearance">?attr/textAppearanceTitleSmall</item>
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Year" parent="Widget.Material3.MaterialCalendar.Item">
    <item name="itemShapeAppearanceOverlay">
      @style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year
    </item>
    <item name="android:width">@dimen/mtrl_calendar_year_width</item>
    <item name="android:height">@dimen/mtrl_calendar_year_height</item>
    <item name="android:insetTop">@dimen/mtrl_calendar_year_vertical_padding</item>
    <item name="android:insetBottom">@dimen/mtrl_calendar_year_vertical_padding</item>
    <item name="android:insetLeft">@dimen/mtrl_calendar_year_horizontal_padding</item>
    <item name="android:insetRight">@dimen/mtrl_calendar_year_horizontal_padding</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Year.Selected">
    <item name="itemFillColor">?attr/colorPrimary</item>
    <item name="itemTextColor">?attr/colorOnPrimary</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.Year.Today">
    <item name="itemTextColor">?attr/colorPrimary</item>
    <item name="itemStrokeColor">?attr/colorPrimary</item>
    <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
  </style>
    <style name="Widget.Material3.MaterialCalendar.YearNavigationButton" parent="Base.Widget.Material3.MaterialCalendar.NavigationButton"/>
    <style name="Widget.Material3.MaterialDivider" parent="Widget.MaterialComponents.MaterialDivider">
    <item name="dividerColor">?attr/colorOutline</item>
  </style>
    <style name="Widget.Material3.MaterialDivider.Heavy">
    <item name="dividerThickness">@dimen/m3_divider_heavy_thickness</item>
  </style>
    <style name="Widget.Material3.MaterialTimePicker" parent="Widget.MaterialComponents.TimePicker"/>
    <style name="Widget.Material3.MaterialTimePicker.Button" parent="Widget.MaterialComponents.TimePicker.Button">
    <item name="android:textAppearance">?attr/textAppearanceTitleMedium</item>
    <item name="android:textColor">@color/m3_timepicker_button_text_color</item>
    <item name="backgroundTint">@color/m3_timepicker_button_background_color</item>
    <item name="iconTint">@color/m3_timepicker_button_text_color</item>
    <item name="rippleColor">@color/m3_timepicker_button_ripple_color</item>
    <item name="strokeColor">?attr/colorOutline</item>
    <!-- Apply theme overlay to disable elevation overlays so we can get a purely colorSurface chip. -->
    <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TimePicker.Display</item>
  </style>
    <style name="Widget.Material3.MaterialTimePicker.Clock" parent="Widget.MaterialComponents.TimePicker.Clock">
    <item name="clockFaceBackgroundColor">?attr/colorSurfaceVariant</item>
    <item name="clockHandColor">?attr/colorPrimary</item>
    <item name="clockNumberTextColor">@color/m3_timepicker_clock_text_color</item>
  </style>
    <style name="Widget.Material3.MaterialTimePicker.Display" parent="Widget.MaterialComponents.TimePicker.Display">
    <item name="android:textAppearance">?attr/textAppearanceDisplayLarge</item>
    <item name="android:textColor">@color/m3_timepicker_display_text_color</item>
    <item name="chipBackgroundColor">@color/m3_timepicker_display_background_color</item>
    <item name="rippleColor">@color/m3_timepicker_display_ripple_color</item>
    <item name="chipStrokeColor">@color/m3_timepicker_display_stroke_color</item>
    <item name="chipStrokeWidth">@dimen/m3_timepicker_display_stroke_width</item>
  </style>
    <style name="Widget.Material3.MaterialTimePicker.Display.Divider" parent="Widget.MaterialComponents.TimePicker.Display.Divider">
    <item name="android:textAppearance">?attr/textAppearanceHeadlineMedium</item>
  </style>
    <style name="Widget.Material3.MaterialTimePicker.Display.HelperText" parent="Widget.MaterialComponents.TimePicker.Display.HelperText">
    <item name="android:textAppearance">?attr/textAppearanceBodySmall</item>
    <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
  </style>
    <style name="Widget.Material3.MaterialTimePicker.Display.TextInputEditText" parent="Widget.Material3.TextInputEditText.OutlinedBox">
    <item name="android:textAppearance">?attr/textAppearanceHeadlineMedium</item>
    <item name="android:textSize" ns1:ignore="SpUsage">56dp</item>
    <item name="android:paddingTop">4dp</item>
    <item name="android:paddingBottom">0dp</item>
    <item name="android:inputType">number</item>
    <item name="android:maxLength">2</item>
    <item name="android:textAlignment">center</item>
    <item name="android:minEms">2</item>
    <item name="android:gravity">center</item>
    <item name="android:paddingStart">0dp</item>
    <item name="android:paddingEnd">0dp</item>
  </style>
    <style name="Widget.Material3.MaterialTimePicker.Display.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText</item>
  </style>
    <style name="Widget.Material3.MaterialTimePicker.ImageButton" parent="Widget.MaterialComponents.TimePicker.ImageButton">
    <item name="iconTint">@color/m3_timepicker_secondary_text_button_text_color</item>
    <item name="rippleColor">@color/m3_timepicker_secondary_text_button_ripple_color</item>
  </style>
    <style name="Widget.Material3.NavigationRailView" parent="Widget.MaterialComponents.NavigationRailView">
    <item name="android:minWidth">@dimen/m3_navigation_rail_default_width</item>
    <item name="android:background">?attr/colorSurface</item>
    <item name="backgroundTint">@null</item>
    <item name="elevation">@dimen/m3_sys_elevation_level0</item>
    <item name="itemActiveIndicatorStyle">@style/Widget.Material3.NavigationRailView.ActiveIndicator</item>
    <item name="itemRippleColor">@color/m3_navigation_bar_ripple_color_selector</item>
    <item name="itemIconTint">@color/m3_navigation_bar_item_with_indicator_icon_tint</item>
    <item name="itemTextColor">@color/m3_navigation_bar_item_with_indicator_label_tint</item>
    <item name="itemPaddingTop">@dimen/m3_navigation_rail_item_padding_top</item>
    <item name="itemPaddingBottom">@dimen/m3_navigation_rail_item_padding_bottom</item>
    <item name="itemMinHeight">@dimen/m3_navigation_rail_item_min_height</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceTitleSmall</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceTitleSmall</item>
  </style>
    <style name="Widget.Material3.NavigationRailView.ActiveIndicator" parent="">
    <item name="android:width">@dimen/m3_navigation_rail_item_active_indicator_width</item>
    <item name="android:height">@dimen/m3_navigation_rail_item_active_indicator_height</item>
    <item name="marginHorizontal">@dimen/m3_navigation_rail_item_active_indicator_margin_horizontal</item>
    <item name="shapeAppearance">@style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator</item>
    <item name="android:color">?attr/colorSecondaryContainer</item>
  </style>
    <style name="Widget.Material3.NavigationView" parent="Widget.MaterialComponents.NavigationView">
    <item name="subheaderColor">?attr/colorOnSurfaceVariant</item>
    <item name="subheaderTextAppearance">?attr/textAppearanceTitleSmall</item>
    <item name="elevation">@dimen/m3_sys_elevation_level0</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.NavigationView</item>
    <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.NavigationView.Item</item>
    <item name="itemShapeInsetStart">@dimen/m3_navigation_item_shape_inset_start</item>
    <item name="itemShapeInsetTop">@dimen/m3_navigation_item_shape_inset_top</item>
    <item name="itemShapeInsetEnd">@dimen/m3_navigation_item_shape_inset_end</item>
    <item name="itemShapeInsetBottom">@dimen/m3_navigation_item_shape_inset_bottom</item>
    <item name="itemIconPadding">@dimen/m3_navigation_item_icon_padding</item>
    <item name="itemHorizontalPadding">@dimen/m3_navigation_item_horizontal_padding</item>
    <item name="itemVerticalPadding">@dimen/m3_navigation_item_vertical_padding</item>
    <item name="itemTextAppearance">?attr/textAppearanceLabelLarge</item>
    <item name="itemIconTint">@color/m3_navigation_item_icon_tint</item>
    <item name="itemShapeFillColor">@color/m3_navigation_item_background_color</item>
    <item name="itemTextColor">@color/m3_navigation_item_text_color</item>
    <item name="topInsetScrimEnabled">false</item>
    <item name="bottomInsetScrimEnabled">false</item>
    <item name="dividerInsetStart">@dimen/m3_navigation_menu_divider_horizontal_padding</item>
    <item name="dividerInsetEnd">@dimen/m3_navigation_menu_divider_horizontal_padding</item>
    <item name="subheaderInsetStart">@dimen/m3_navigation_menu_headline_horizontal_padding</item>
    <item name="subheaderInsetEnd">@dimen/m3_navigation_menu_headline_horizontal_padding</item>
    <item name="drawerLayoutCornerSize">@dimen/m3_navigation_drawer_layout_corner_size</item>
  </style>
    <style name="Widget.Material3.PopupMenu" parent="Widget.MaterialComponents.PopupMenu">
    <item name="android:popupElevation" ns1:ignore="NewApi">@dimen/m3_menu_elevation</item>
  </style>
    <style name="Widget.Material3.PopupMenu.ContextMenu" parent="Widget.MaterialComponents.PopupMenu.ContextMenu">
    <item name="android:popupElevation" ns1:ignore="NewApi">@dimen/m3_menu_elevation</item>
  </style>
    <style name="Widget.Material3.PopupMenu.ListPopupWindow" parent="Widget.MaterialComponents.PopupMenu.ListPopupWindow">
    <item name="android:popupElevation" ns1:ignore="NewApi">@dimen/m3_menu_elevation</item>
  </style>
    <style name="Widget.Material3.PopupMenu.Overflow" parent="Widget.MaterialComponents.PopupMenu.Overflow">
    <item name="android:popupElevation" ns1:ignore="NewApi">@dimen/m3_menu_elevation</item>
  </style>
    <style name="Widget.Material3.Slider" parent="Widget.MaterialComponents.Slider">
    <item name="haloColor">@color/m3_slider_halo_color</item>
    <item name="labelStyle">@style/Widget.Material3.Tooltip</item>
    <item name="thumbColor">@color/m3_slider_thumb_color</item>
    <item name="tickColorActive">@color/m3_slider_inactive_track_color</item>
    <item name="tickColorInactive">@color/m3_slider_active_track_color</item>
    <item name="trackColorActive">@color/m3_slider_active_track_color</item>
    <item name="trackColorInactive">@color/m3_slider_inactive_track_color</item>
    <item name="thumbElevation">@dimen/m3_slider_thumb_elevation</item>
  </style>
    <style name="Widget.Material3.Snackbar" parent="Base.Widget.Material3.Snackbar">
    <!-- Null out the background here so the programmatically defined default Snackbar background
         will be used, which supports the Material color theming attributes. -->
    <item name="android:background">@null</item>
    <item name="android:layout_margin">@dimen/m3_snackbar_margin</item>
    <item name="animationMode">fade</item>
  </style>
    <style name="Widget.Material3.Snackbar.FullWidth" parent="Base.Widget.Material3.Snackbar"/>
    <style name="Widget.Material3.Snackbar.TextView" parent="Widget.MaterialComponents.Snackbar.TextView">
    <item name="android:alpha">@dimen/m3_snackbar_action_text_color_alpha</item>
    <item name="android:textColor">?attr/colorOnSurfaceInverse</item>
    <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
  </style>
    <style name="Widget.Material3.TabLayout" parent="Base.Widget.Material3.TabLayout"/>
    <style name="Widget.Material3.TabLayout.OnSurface" parent="Base.Widget.Material3.TabLayout.OnSurface"/>
    <style name="Widget.Material3.TabLayout.Secondary" parent="Base.Widget.Material3.TabLayout.Secondary"/>
    <style name="Widget.Material3.TextInputEditText.FilledBox" parent="Widget.MaterialComponents.TextInputEditText.FilledBox">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
    <item name="android:textColor">@color/m3_textfield_input_text_color</item>
  </style>
    <style name="Widget.Material3.TextInputEditText.FilledBox.Dense" parent="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
    <item name="android:textColor">@color/m3_textfield_input_text_color</item>
  </style>
    <style name="Widget.Material3.TextInputEditText.OutlinedBox" parent="Widget.MaterialComponents.TextInputEditText.OutlinedBox">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
    <item name="android:textColor">@color/m3_textfield_input_text_color</item>
  </style>
    <style name="Widget.Material3.TextInputEditText.OutlinedBox.Dense" parent="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
    <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
    <item name="android:textColor">@color/m3_textfield_input_text_color</item>
  </style>
    <style name="Widget.Material3.TextInputLayout.FilledBox" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TextInputEditText.FilledBox</item>

    <item name="boxStrokeColor">@color/m3_textfield_stroke_color</item>
    <item name="boxStrokeErrorColor">?attr/colorError</item>
    <item name="boxBackgroundColor">@color/m3_textfield_filled_background_color</item>
    <item name="helperTextTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="counterTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="counterOverflowTextColor">?attr/colorError</item>
    <item name="hintTextColor">@color/m3_textfield_label_color</item>
    <item name="android:textColorHint">@color/m3_textfield_label_color</item>
    <item name="placeholderTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="prefixTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="suffixTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="errorTextColor">?attr/colorError</item>
    <item name="errorIconTint">?attr/colorError</item>
    <item name="startIconTint">@color/m3_textfield_indicator_text_color</item>
    <item name="endIconTint">@color/m3_textfield_indicator_text_color</item>

    <item name="counterTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="counterOverflowTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="errorTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="helperTextTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="hintTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="placeholderTextAppearance">?attr/textAppearanceBodyLarge</item>
    <item name="prefixTextAppearance">?attr/textAppearanceTitleMedium</item>
    <item name="suffixTextAppearance">?attr/textAppearanceTitleMedium</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.TextField.Filled</item>
  </style>
    <style name="Widget.Material3.TextInputLayout.FilledBox.Dense">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense</item>
  </style>
    <style name="Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense</item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox</item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.Material3.TextInputLayout.OutlinedBox" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox</item>

    <item name="boxStrokeColor">@color/m3_textfield_stroke_color</item>
    <item name="boxStrokeErrorColor">?attr/colorError</item>
    <item name="helperTextTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="counterTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="counterOverflowTextColor">?attr/colorError</item>
    <item name="hintTextColor">@color/m3_textfield_label_color</item>
    <item name="android:textColorHint">@color/m3_textfield_label_color</item>
    <item name="placeholderTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="prefixTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="suffixTextColor">@color/m3_textfield_indicator_text_color</item>
    <item name="errorTextColor">?attr/colorError</item>
    <item name="errorIconTint">?attr/colorError</item>
    <item name="startIconTint">@color/m3_textfield_indicator_text_color</item>
    <item name="endIconTint">@color/m3_textfield_indicator_text_color</item>

    <item name="counterTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="counterOverflowTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="errorTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="helperTextTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="hintTextAppearance">?attr/textAppearanceBodySmall</item>
    <item name="placeholderTextAppearance">?attr/textAppearanceBodyLarge</item>
    <item name="prefixTextAppearance">?attr/textAppearanceTitleMedium</item>
    <item name="suffixTextAppearance">?attr/textAppearanceTitleMedium</item>
    <item name="shapeAppearanceOverlay">?attr/shapeAppearanceSmallComponent</item>
  </style>
    <style name="Widget.Material3.TextInputLayout.OutlinedBox.Dense">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense</item>
  </style>
    <style name="Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense</item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox</item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.Material3.Toolbar" parent="Widget.AppCompat.Toolbar">
    <item name="titleTextAppearance">?attr/textAppearanceTitleLarge</item>
    <item name="subtitleTextAppearance">?attr/textAppearanceTitleMedium</item>
    <item name="contentInsetStartWithNavigation">0dp</item>
  </style>
    <style name="Widget.Material3.Toolbar.OnSurface">
    <item name="titleTextColor">?attr/colorOnSurface</item>
    <item name="subtitleTextColor">?attr/colorOnSurfaceVariant</item>
    <item name="navigationIconTint">?attr/colorOnSurface</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Toolbar.Surface</item>
  </style>
    <style name="Widget.Material3.Toolbar.Surface" parent="Widget.Material3.Toolbar.OnSurface">
    <item name="android:background">?attr/colorSurface</item>

    <!-- On newer API levels, hide shadows while keeping elevation. -->
    <item name="android:outlineAmbientShadowColor" ns1:ignore="NewApi">@android:color/transparent</item>
    <item name="android:outlineSpotShadowColor" ns1:ignore="NewApi">@android:color/transparent</item>
  </style>
    <style name="Widget.Material3.Tooltip" parent="Widget.MaterialComponents.Tooltip">
    <item name="android:textAppearance">?attr/textAppearanceBodySmall</item>
    <item name="android:textColor">?attr/colorOnPrimary</item>
    <item name="backgroundTint">?attr/colorPrimary</item>
    <item name="android:padding">4dp</item>
    <item name="android:minWidth">28dp</item>
    <item name="android:minHeight">28dp</item>
    <item name="shapeAppearance">@style/ShapeAppearance.Material3.Tooltip</item>
  </style>
    <style name="Widget.MaterialComponents.ActionBar.Primary" parent="Widget.AppCompat.ActionBar.Solid">
    <item name="background">?attr/colorPrimary</item>
    <item name="elevation">@dimen/design_appbar_elevation</item>
  </style>
    <style name="Widget.MaterialComponents.ActionBar.PrimarySurface" parent="Widget.MaterialComponents.ActionBar.Primary"/>
    <style name="Widget.MaterialComponents.ActionBar.Solid" parent="Widget.AppCompat.ActionBar.Solid">
    <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
    <!-- Overrides minimum height in landscape to avoid headline6 and subtitle1 height concerns. -->
    <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
    <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
  </style>
    <style name="Widget.MaterialComponents.ActionBar.Surface" parent="Widget.AppCompat.Light.ActionBar.Solid">
    <item name="background">?attr/colorSurface</item>
    <item name="elevation">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.AppBarLayout.Primary" parent="Widget.Design.AppBarLayout"/>
    <style name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" parent="Widget.MaterialComponents.AppBarLayout.Primary"/>
    <style name="Widget.MaterialComponents.AppBarLayout.Surface" parent="Widget.Design.AppBarLayout">
    <item name="android:background">?attr/colorSurface</item>
  </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" parent="Base.Widget.MaterialComponents.AutoCompleteTextView">
    <!-- Padding values that total 34dp to visually match the spec. -->
    <item name="android:paddingTop">24dp</item>
    <item name="android:paddingBottom">10dp</item>
  </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense">
    <!-- Dense layout height is 54dp so we need a total of 32dp of top and bottom padding. -->
    <item name="android:paddingTop">24dp</item>
    <item name="android:paddingBottom">8dp</item>
  </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" parent="Base.Widget.MaterialComponents.AutoCompleteTextView"/>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense">
    <item name="android:paddingTop">13dp</item>
    <item name="android:paddingBottom">13dp</item>
  </style>
    <style name="Widget.MaterialComponents.Badge" parent="android:Widget">
    <item name="badgeRadius">@dimen/mtrl_badge_radius</item>
    <item name="badgeWidePadding">@dimen/mtrl_badge_long_text_horizontal_padding</item>
    <item name="badgeWithTextRadius">@dimen/mtrl_badge_with_text_radius</item>
    <item name="backgroundColor">?attr/colorError</item>
    <item name="maxCharacterCount">@integer/mtrl_badge_max_character_count</item>
    <item name="badgeGravity">TOP_END</item>
  </style>
    <style name="Widget.MaterialComponents.BottomAppBar" parent="Widget.AppCompat.Toolbar">
    <item name="enforceMaterialTheme">true</item>
    <item name="backgroundTint">?attr/colorSurface</item>
    <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
    <item name="fabCradleRoundedCornerRadius">
      @dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius
    </item>
    <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
    <item name="android:minHeight">@dimen/mtrl_bottomappbar_height</item>
    <item name="maxButtonHeight">@dimen/mtrl_bottomappbar_height</item>
    <item name="elevation">8dp</item>
    <item name="paddingBottomSystemWindowInsets">true</item>
    <item name="paddingLeftSystemWindowInsets">true</item>
    <item name="paddingRightSystemWindowInsets">true</item>
  </style>
    <style name="Widget.MaterialComponents.BottomAppBar.Colored" parent="Widget.MaterialComponents.BottomAppBar">
    <item name="backgroundTint">?attr/colorPrimary</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary</item>
  </style>
    <style name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" parent="Widget.MaterialComponents.BottomAppBar.Colored"/>
    <style name="Widget.MaterialComponents.BottomNavigationView" parent="Widget.Design.BottomNavigationView">
    <item name="enforceTextAppearance">true</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">?attr/colorSurface</item>
    <item name="itemBackground">@null</item>
    <item name="itemHorizontalTranslationEnabled">false</item>
    <item name="itemIconTint">@color/mtrl_navigation_bar_item_tint</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
    <item name="itemTextColor">@color/mtrl_navigation_bar_item_tint</item>
    <item name="itemRippleColor">@color/mtrl_navigation_bar_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.Colored">
    <item name="enforceTextAppearance">true</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">?attr/colorPrimary</item>
    <item name="itemIconTint">@color/mtrl_navigation_bar_colored_item_tint</item>
    <item name="itemRippleColor">@color/mtrl_navigation_bar_colored_ripple_color</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
    <item name="itemTextColor">@color/mtrl_navigation_bar_colored_item_tint</item>
  </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" parent="Widget.MaterialComponents.BottomNavigationView.Colored"/>
    <style name="Widget.MaterialComponents.BottomSheet" parent="Widget.Design.BottomSheet.Modal">
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">@null</item>
    <item name="android:layout_gravity">center_horizontal</item>
    <item name="shapeAppearance">?attr/shapeAppearanceLargeComponent</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet</item>
    <item name="backgroundTint">?attr/colorSurface</item>
    <item name="android:elevation" ns1:ignore="NewApi">
      @dimen/design_bottom_sheet_elevation
    </item>
    <item name="android:maxWidth">@dimen/material_bottom_sheet_max_width</item>
  </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal" parent="Widget.MaterialComponents.BottomSheet">
    <item name="android:elevation" ns1:ignore="NewApi">
      @dimen/design_bottom_sheet_modal_elevation
    </item>
  </style>
    <style name="Widget.MaterialComponents.Button" parent="Widget.AppCompat.Button">
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">@empty</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:textAppearance">?attr/textAppearanceButton</item>
    <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
    <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
    <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
    <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
    <item name="android:insetLeft">0dp</item>
    <item name="android:insetRight">0dp</item>
    <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
    <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
    <item name="android:maxWidth">@dimen/mtrl_btn_max_width</item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">@animator/mtrl_btn_state_list_anim</item>
    <item name="cornerRadius">@null</item>
    <item name="elevation">@dimen/mtrl_btn_elevation</item>
    <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
    <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
    <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
    <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
  </style>
    <style name="Widget.MaterialComponents.Button.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton" parent="Widget.MaterialComponents.Button.TextButton">
    <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
    <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
    <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
  </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton" parent="Widget.MaterialComponents.Button.UnelevatedButton">
    <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
    <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
    <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
    <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
    <item name="backgroundTint">@color/mtrl_btn_text_btn_bg_color_selector</item>
    <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog">
    <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
    <item name="android:lines">1</item>
    <item name="android:ellipsize">end</item>
    <item name="android:singleLine">true</item>
    <item name="android:layout_marginStart">@dimen/mtrl_btn_text_btn_padding_left</item>
    <item name="android:layout_marginLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush">
    <item name="android:layout_marginStart">0dp</item>
    <item name="android:layout_marginLeft">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon">
    <!-- Icon text button has the same padding as a regular text button -->
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Icon">
    <!-- Icon text button has the same padding as a regular text button -->
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Snackbar">
    <item name="android:textColor">?attr/colorPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton">
    <item name="android:stateListAnimator" ns1:ignore="NewApi">@animator/mtrl_btn_unelevated_state_list_anim</item>
    <item name="elevation">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.CardView" parent="CardView">
    <item name="enforceMaterialTheme">true</item>

    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/mtrl_card_state_list_anim
    </item>
    <item name="cardBackgroundColor">?attr/colorSurface</item>
    <item name="cardCornerRadius">@null</item>
    <item name="cardElevation">@dimen/mtrl_card_elevation</item>
    <item name="cardForegroundColor">@color/mtrl_card_view_foreground</item>
    <item name="checkedIcon">@drawable/ic_mtrl_checked_circle</item>
    <item name="checkedIconTint">?attr/colorPrimary</item>
    <item name="checkedIconSize">@dimen/mtrl_card_checked_icon_size</item>
    <item name="checkedIconMargin">@dimen/mtrl_card_checked_icon_margin</item>
    <item name="rippleColor">@color/mtrl_card_view_ripple</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="Widget.MaterialComponents.CheckedTextView" parent="Base.Widget.MaterialComponents.CheckedTextView">
    <item name="android:textAppearance">?attr/textAppearanceBody1</item>
    <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Action" parent="Base.Widget.MaterialComponents.Chip">
    <item name="closeIconVisible">false</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Choice" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>

    <item name="chipIconVisible">false</item>
    <item name="checkedIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>

    <item name="android:textColor">@color/mtrl_choice_chip_text_color</item>

    <item name="chipBackgroundColor">@color/mtrl_choice_chip_background_color</item>
    <item name="rippleColor">@color/mtrl_choice_chip_ripple_color</item>

  </style>
    <style name="Widget.MaterialComponents.Chip.Entry" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Filter" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>

    <item name="chipIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
  </style>
    <style name="Widget.MaterialComponents.ChipGroup" parent="android:Widget">
    <item name="chipSpacingHorizontal">8dp</item>
    <item name="singleLine">false</item>
    <item name="singleSelection">false</item>
  </style>
    <style name="Widget.MaterialComponents.CircularProgressIndicator" parent="Widget.MaterialComponents.ProgressIndicator">
    <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_medium</item>
    <item name="indicatorSize">@dimen/mtrl_progress_circular_size_medium</item>
    <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_medium</item>
    <item name="trackColor">@android:color/transparent</item>
    <item name="indicatorDirectionCircular">clockwise</item>
  </style>
    <style name="Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall">
    <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_extra_small</item>
    <item name="indicatorSize">@dimen/mtrl_progress_circular_size_extra_small</item>
    <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_extra_small</item>
  </style>
    <style name="Widget.MaterialComponents.CircularProgressIndicator.Medium"/>
    <style name="Widget.MaterialComponents.CircularProgressIndicator.Small">
    <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_small</item>
    <item name="indicatorSize">@dimen/mtrl_progress_circular_size_small</item>
    <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_small</item>
  </style>
    <style name="Widget.MaterialComponents.CollapsingToolbar" parent="Widget.Design.CollapsingToolbar"/>
    <style name="Widget.MaterialComponents.CompoundButton.CheckBox" parent="Widget.AppCompat.CompoundButton.CheckBox">
    <item name="enforceMaterialTheme">true</item>
    <item name="useMaterialThemeColors">true</item>
    <item name="android:minWidth">?attr/minTouchTargetSize</item>
    <item name="android:minHeight">?attr/minTouchTargetSize</item>
  </style>
    <style name="Widget.MaterialComponents.CompoundButton.RadioButton" parent="Widget.AppCompat.CompoundButton.RadioButton">
    <item name="enforceMaterialTheme">true</item>
    <item name="useMaterialThemeColors">true</item>
    <item name="android:minWidth">?attr/minTouchTargetSize</item>
    <item name="android:minHeight">?attr/minTouchTargetSize</item>
  </style>
    <style name="Widget.MaterialComponents.CompoundButton.Switch" parent="Widget.AppCompat.CompoundButton.Switch">
    <item name="enforceMaterialTheme">true</item>
    <item name="useMaterialThemeColors">true</item>
    <item name="android:minWidth">?attr/minTouchTargetSize</item>
    <item name="android:minHeight">?attr/minTouchTargetSize</item>
  </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton" parent="Widget.MaterialComponents.Button">
    <item name="android:insetTop">0dp</item>
    <item name="android:insetBottom">0dp</item>
    <item name="android:maxLines">1</item>
    <item name="android:minHeight">@dimen/mtrl_extended_fab_min_height</item>
    <item name="android:minWidth">@dimen/mtrl_extended_fab_min_width</item>
    <item name="android:paddingTop">@dimen/mtrl_extended_fab_top_padding</item>
    <item name="android:paddingBottom">@dimen/mtrl_extended_fab_bottom_padding</item>
    <item name="android:paddingStart" ns1:ignore="NewApi">
      @dimen/mtrl_extended_fab_start_padding
    </item>
    <item name="android:paddingEnd" ns1:ignore="NewApi">
      @dimen/mtrl_extended_fab_end_padding
    </item>
    <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding</item>
    <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding</item>
    <item name="android:stateListAnimator" ns1:ignore="NewApi">
      @animator/mtrl_extended_fab_state_list_animator
    </item>
    <item name="android:textColor">@color/mtrl_fab_icon_text_color_selector</item>
    <item name="backgroundTint">@color/mtrl_fab_bg_color_selector</item>
    <item name="elevation">@dimen/mtrl_extended_fab_elevation</item>
    <item name="iconPadding">@dimen/mtrl_extended_fab_icon_text_spacing</item>
    <item name="iconSize">@dimen/mtrl_extended_fab_icon_size</item>
    <item name="iconTint">@color/mtrl_fab_icon_text_color_selector</item>
    <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
    <item name="shapeAppearanceOverlay">
      @style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton
    </item>
    <item name="collapsedSize">@dimen/design_fab_size_normal</item>
  </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" parent="Widget.MaterialComponents.ExtendedFloatingActionButton">
    <item name="android:gravity">start|center_vertical</item>
    <item name="android:paddingStart" ns1:ignore="NewApi">
      @dimen/mtrl_extended_fab_start_padding_icon
    </item>
    <item name="android:paddingEnd" ns1:ignore="NewApi">
      @dimen/mtrl_extended_fab_end_padding_icon
    </item>
    <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding_icon</item>
    <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding_icon</item>
  </style>
    <style name="Widget.MaterialComponents.FloatingActionButton" parent="Widget.Design.FloatingActionButton">
    <item name="android:background">@null</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="ensureMinTouchTargetSize">true</item>
    <item name="elevation">@dimen/mtrl_fab_elevation</item>
    <item name="backgroundTint">@color/mtrl_fab_bg_color_selector</item>
    <item name="tint">@color/mtrl_fab_icon_text_color_selector</item>
    <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
    <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
    <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
    <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
    <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
    <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="shapeAppearanceOverlay">
      @style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton
    </item>
  </style>
    <style name="Widget.MaterialComponents.Light.ActionBar.Solid" parent="Widget.AppCompat.Light.ActionBar.Solid">
    <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
    <!-- Overrides minimum height in landscape to avoid headline6 and subtitle1 height concerns. -->
    <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
    <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
  </style>
    <style name="Widget.MaterialComponents.LinearProgressIndicator" parent="Widget.MaterialComponents.ProgressIndicator">
    <item name="indeterminateAnimationType">disjoint</item>
    <item name="indicatorDirectionLinear">startToEnd</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialButtonToggleGroup" parent="android:Widget">
    <item name="singleSelection">false</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar" parent="android:Widget">
    <item name="android:windowFullscreen">false</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    <item name="dayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="dayInvalidStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid</item>
    <item name="daySelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
    <item name="dayTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Today</item>
    <item name="yearStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year</item>
    <item name="yearSelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Selected</item>
    <item name="yearTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Today</item>
    <item name="rangeFillColor">@color/mtrl_calendar_selected_range</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day" parent="Widget.MaterialComponents.MaterialCalendar.Item">
    <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    <item name="android:width">@dimen/mtrl_calendar_day_width</item>
    <item name="android:height">@dimen/mtrl_calendar_day_height</item>
    <item name="android:insetTop">@dimen/mtrl_calendar_day_vertical_padding</item>
    <item name="android:insetBottom">@dimen/mtrl_calendar_day_vertical_padding</item>
    <item name="android:insetLeft">@dimen/mtrl_calendar_day_horizontal_padding</item>
    <item name="android:insetRight">@dimen/mtrl_calendar_day_horizontal_padding</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid">
    <item name="itemTextColor">@color/material_on_surface_disabled</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Selected">
    <item name="itemFillColor">?attr/colorPrimary</item>
    <item name="itemTextColor">?attr/colorOnPrimary</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Today">
    <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel" parent="">
    <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    <item name="android:gravity">center</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.DayTextView" parent="Widget.AppCompat.TextView">
    <item name="android:textAppearance">?attr/textAppearanceCaption</item>
    <item name="android:gravity">center</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" parent="Widget.MaterialComponents.MaterialCalendar">
    <item name="android:windowFullscreen">true</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton" parent="Widget.MaterialComponents.Button.TextButton">
    <item name="iconTint">?attr/colorOnPrimary</item>
    <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" parent="Widget.MaterialComponents.Button.TextButton">
    <item name="android:textColor">@color/mtrl_on_primary_text_btn_text_color_selector</item>
    <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" parent="android:Widget">
    <item name="android:visibility">gone</item>
    <item name="android:background">?attr/colorOnPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" parent="android:Widget">
    <item name="android:background">?attr/colorPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" parent="Widget.AppCompat.TextView">
    <item name="android:textAppearance">?attr/textAppearanceHeadline4</item>
    <item name="android:textColor">?attr/colorOnPrimary</item>
    <item name="android:maxLines">@integer/mtrl_calendar_selection_text_lines</item>
    <item name="android:ellipsize">end</item>
    <item name="autoSizeTextType">uniform</item>
    <item name="autoSizeMaxTextSize">34sp</item>
    <item name="autoSizeMinTextSize">2sp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen">
    <item name="android:textAppearance">?attr/textAppearanceHeadline6</item>
    <item name="android:maxLines">1</item>
    <item name="autoSizeMaxTextSize">20sp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" parent="Widget.AppCompat.TextView">
    <item name="android:textAppearance">?attr/textAppearanceOverline</item>
    <item name="android:textColor">?attr/colorOnPrimary</item>
    <item name="android:maxLines">1</item>
    <item name="android:ellipsize">end</item>
    <item name="autoSizeTextType">uniform</item>
    <item name="autoSizeMaxTextSize">10sp</item>
    <item name="autoSizeMinTextSize">2sp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" parent="Widget.AppCompat.ImageButton">
    <item name="android:background">?attr/actionBarItemBackground</item>
    <item name="android:tint">?attr/colorOnPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Item" parent="">
    <item name="itemFillColor">@android:color/transparent</item>
    <item name="itemTextColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="itemStrokeColor">@color/mtrl_calendar_item_stroke_color</item>
    <item name="itemStrokeWidth">1dp</item>
    <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton" parent="Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton"/>
    <style name="Widget.MaterialComponents.MaterialCalendar.MonthTextView" parent="Widget.AppCompat.TextView">
    <item name="android:textAppearance">?attr/textAppearanceSubtitle2</item>
    <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year" parent="Widget.MaterialComponents.MaterialCalendar.Item">
    <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year</item>
    <item name="android:width">@dimen/mtrl_calendar_year_width</item>
    <item name="android:height">@dimen/mtrl_calendar_year_height</item>
    <item name="android:insetTop">@dimen/mtrl_calendar_year_vertical_padding</item>
    <item name="android:insetBottom">@dimen/mtrl_calendar_year_vertical_padding</item>
    <item name="android:insetLeft">@dimen/mtrl_calendar_year_horizontal_padding</item>
    <item name="android:insetRight">@dimen/mtrl_calendar_year_horizontal_padding</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" parent="Widget.MaterialComponents.MaterialCalendar.Year">
    <item name="itemFillColor">?attr/colorPrimary</item>
    <item name="itemTextColor">?attr/colorOnPrimary</item>
    <item name="itemStrokeColor">?attr/colorOnPrimary</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Today" parent="Widget.MaterialComponents.MaterialCalendar.Year">
    <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.YearNavigationButton" parent="Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton"/>
    <style name="Widget.MaterialComponents.MaterialDivider" parent="android:Widget">
    <item name="dividerColor">@color/material_divider_color</item>
    <item name="dividerThickness">@dimen/material_divider_thickness</item>
  </style>
    <style name="Widget.MaterialComponents.NavigationRailView" parent="">
    <item name="elevation">@dimen/mtrl_navigation_rail_elevation</item>
    <item name="enforceTextAppearance">true</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">?attr/colorSurface</item>
    <item name="android:minWidth">@dimen/mtrl_navigation_rail_default_width</item>
    <item name="itemActiveIndicatorStyle">@null</item>
    <item name="itemPaddingTop">@dimen/mtrl_navigation_rail_icon_margin</item>
    <item name="itemPaddingBottom">@dimen/mtrl_navigation_rail_text_bottom_margin</item>
    <item name="itemBackground">@null</item>
    <item name="itemIconSize">@dimen/mtrl_navigation_rail_icon_size</item>
    <item name="itemIconTint">@color/mtrl_navigation_bar_item_tint</item>
    <item name="itemMinHeight">@null</item>
    <item name="itemRippleColor">@color/mtrl_navigation_bar_ripple_color</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
    <item name="itemTextColor">@color/mtrl_navigation_bar_item_tint</item>
    <item name="labelVisibilityMode">auto</item>
    <item name="menuGravity">top</item>
  </style>
    <style name="Widget.MaterialComponents.NavigationRailView.Colored">
    <item name="android:background">?attr/colorPrimary</item>
    <item name="itemIconTint">@color/mtrl_navigation_bar_colored_item_tint</item>
    <item name="itemRippleColor">@color/mtrl_navigation_bar_colored_ripple_color</item>
    <item name="itemTextColor">@color/mtrl_navigation_bar_colored_item_tint</item>
  </style>
    <style name="Widget.MaterialComponents.NavigationRailView.Colored.Compact">
    <item name="android:minWidth">@dimen/mtrl_navigation_rail_compact_width</item>
    <item name="labelVisibilityMode">unlabeled</item>
  </style>
    <style name="Widget.MaterialComponents.NavigationRailView.Compact">
    <item name="android:minWidth">@dimen/mtrl_navigation_rail_compact_width</item>
    <item name="labelVisibilityMode">unlabeled</item>
  </style>
    <style name="Widget.MaterialComponents.NavigationRailView.PrimarySurface" parent="Widget.MaterialComponents.NavigationRailView.Colored"/>
    <style name="Widget.MaterialComponents.NavigationView" parent="Widget.Design.NavigationView">
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">?attr/colorSurface</item>
    <!-- itemBackground is set to @null to use a shaped background programmatically generated by
         NavigationView when itemShapeAppearance and/or itemShapeAppearanceOverlay is set. This
         background is styled using the itemShape* attributes below. Setting itemBackground will
         overwrite the programmatic background and cause values set in the itemShape* attributes
         to be ignored. -->
    <item name="itemBackground">@null</item>
    <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
    <item name="itemIconTint">@color/mtrl_navigation_item_icon_tint</item>
    <item name="itemIconSize">@dimen/mtrl_navigation_item_icon_size</item>
    <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
    <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="itemShapeFillColor">@color/mtrl_navigation_item_background_color</item>
    <item name="itemShapeInsetStart">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
    <item name="itemShapeInsetTop">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
    <item name="itemShapeInsetEnd">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
    <item name="itemShapeInsetBottom">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
    <item name="itemTextAppearance">?attr/textAppearanceSubtitle2</item>
    <item name="itemTextColor">@color/mtrl_navigation_item_text_color</item>
  </style>
    <style name="Widget.MaterialComponents.PopupMenu" parent="Base.Widget.MaterialComponents.PopupMenu"/>
    <style name="Widget.MaterialComponents.PopupMenu.ContextMenu" parent="Base.Widget.MaterialComponents.PopupMenu.ContextMenu"/>
    <style name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow"/>
    <style name="Widget.MaterialComponents.PopupMenu.Overflow" parent="Base.Widget.MaterialComponents.PopupMenu.Overflow"/>
    <style name="Widget.MaterialComponents.ProgressIndicator" parent="android:Widget">
    <item name="indicatorColor">?attr/colorPrimary</item>
    <item name="trackThickness">@dimen/mtrl_progress_track_thickness</item>
    <item name="android:indeterminateOnly">false</item>
  </style>
    <style name="Widget.MaterialComponents.ShapeableImageView" parent="android:Widget">
    <item name="strokeColor">@color/material_on_surface_stroke</item>
  </style>
    <style name="Widget.MaterialComponents.Slider" parent="Base.Widget.MaterialComponents.Slider"/>
    <style name="Widget.MaterialComponents.Snackbar" parent="Base.Widget.MaterialComponents.Snackbar">
    <!-- Null out the background here so the programmatically defined default Snackbar background
         will be used, which supports the Material color theming attributes. -->
    <item name="android:background">@null</item>
    <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
    <item name="animationMode">fade</item>
  </style>
    <style name="Widget.MaterialComponents.Snackbar.FullWidth" parent="Base.Widget.MaterialComponents.Snackbar"/>
    <style name="Widget.MaterialComponents.Snackbar.TextView" parent="Widget.AppCompat.TextView">
    <item name="android:alpha">@dimen/material_emphasis_high_type</item>
    <item name="android:ellipsize">end</item>
    <item name="android:maxLines">@integer/design_snackbar_text_max_lines</item>
    <item name="android:textAlignment" ns1:ignore="NewApi">viewStart</item>
    <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    <item name="android:textColor">?attr/colorSurface</item>
    <item name="android:paddingTop">@dimen/design_snackbar_padding_vertical</item>
    <item name="android:paddingBottom">@dimen/design_snackbar_padding_vertical</item>
    <item name="android:layout_marginLeft">@dimen/mtrl_snackbar_message_margin_horizontal</item>
    <item name="android:layout_marginRight">@dimen/mtrl_snackbar_message_margin_horizontal</item>
  </style>
    <style name="Widget.MaterialComponents.TabLayout" parent="Widget.Design.TabLayout">
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:background">?attr/colorSurface</item>
    <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
    <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
    <item name="tabIndicatorColor">?attr/colorPrimary</item>
    <item name="tabTextAppearance">?attr/textAppearanceButton</item>
    <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
    <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
    <item name="tabUnboundedRipple">true</item>
  </style>
    <style name="Widget.MaterialComponents.TabLayout.Colored">
    <item name="android:background">?attr/colorPrimary</item>
    <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector_colored</item>
    <item name="tabIndicatorColor">?attr/colorOnPrimary</item>
    <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector_colored</item>
    <item name="tabRippleColor">@color/mtrl_tabs_colored_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.TabLayout.PrimarySurface" parent="Widget.MaterialComponents.TabLayout.Colored"/>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox" parent="Base.Widget.MaterialComponents.TextInputEditText">
    <!-- Padding values that total 34dp to visually match the spec. -->
    <item name="android:paddingTop">24dp</item>
    <item name="android:paddingBottom">10dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense">
    <!-- Dense layout height is 54dp so we need a total of 32dp of top and bottom padding. -->
    <item name="android:paddingTop">24dp</item>
    <item name="android:paddingBottom">8dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" parent="Base.Widget.MaterialComponents.TextInputEditText"/>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
    <item name="android:paddingTop">13dp</item>
    <item name="android:paddingBottom">13dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox" parent="Base.Widget.MaterialComponents.TextInputLayout">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox
    </item>
    <item name="boxBackgroundMode">filled</item>
    <item name="boxBackgroundColor">@color/mtrl_filled_background_color</item>
    <item name="endIconTint">@color/mtrl_filled_icon_tint</item>
    <item name="startIconTint">@color/mtrl_filled_icon_tint</item>
    <item name="boxCollapsedPaddingTop">10dp</item>
    <item name="boxStrokeColor">@color/mtrl_filled_stroke_color</item>
    <item name="shapeAppearanceOverlay">
      @style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox
    </item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense
    </item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense
    </item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox
    </item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" parent="Base.Widget.MaterialComponents.TextInputLayout">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox
    </item>
    <item name="boxCollapsedPaddingTop">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense
    </item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense
    </item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox
    </item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.MaterialComponents.TextView" parent="Base.Widget.MaterialComponents.TextView"/>
    <style name="Widget.MaterialComponents.TimePicker" parent="">
    <item name="shapeAppearance">?shapeAppearanceMediumComponent</item>
    <item name="keyboardIcon">@drawable/ic_keyboard_black_24dp</item>
    <item name="clockIcon">@drawable/ic_clock_black_24dp</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.Button" parent="Widget.MaterialComponents.Button.OutlinedButton">
    <item name="backgroundTint">
      @color/material_timepicker_button_background
    </item>
    <item name="android:layout_height">48dp</item>
    <item name="android:padding">0dp</item>
    <item name="android:insetTop">0dp</item>
    <item name="android:insetBottom">0dp</item>
    <item name="android:textAppearance">?attr/textAppearanceSubtitle2</item>
    <item name="android:textSize">16sp</item>
    <item name="android:textAlignment">center</item>
    <item name="strokeColor">@color/material_timepicker_button_stroke</item>
    <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.Clock" parent="">
    <item name="clockFaceBackgroundColor">@color/material_timepicker_clockface</item>
    <item name="clockHandColor">?attr/colorPrimary</item>
    <item name="clockNumberTextColor">@color/material_timepicker_clock_text_color</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.Display" parent="Widget.MaterialComponents.Chip.Choice">
    <item name="ensureMinTouchTargetSize">false</item>
    <item name="android:textAlignment">center</item>
    <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
    <!-- No need to scale here since the text is already readable -->
    <item name="android:textSize" ns1:ignore="SpUsage">56dp</item>
    <item name="shapeAppearanceOverlay">?shapeAppearanceMediumComponent</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TimePicker.Display</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.Divider" parent="">
    <item name="android:includeFontPadding">false</item>
    <item name="android:lineSpacingExtra">0dp</item>
    <item name="android:maxEms">1</item>
    <item name="android:text">@string/material_clock_display_divider</item>
    <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
    <item name="android:textColor">?attr/colorOnSurface</item>
    <item name="android:textSize">56dp</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.HelperText" parent="Widget.MaterialComponents.TextView">
    <item name="android:textAppearance">?attr/textAppearanceCaption</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.TextInputEditText" parent="Widget.MaterialComponents.TextInputEditText.OutlinedBox">
    <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
    <item name="android:textSize" ns1:ignore="SpUsage">56dp</item>
    <item name="android:paddingTop">4dp</item>
    <item name="android:paddingBottom">0dp</item>
    <item name="android:inputType">number</item>
    <item name="android:maxLength">2</item>
    <item name="android:textAlignment">center</item>
    <item name="android:minEms">2</item>
    <item name="android:gravity">center</item>
    <item name="android:paddingStart">0dp</item>
    <item name="android:paddingEnd">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
    <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.ImageButton" parent="">
    <item name="android:insetTop">0dp</item>
    <item name="android:insetBottom">0dp</item>
    <item name="android:minWidth">?attr/minTouchTargetSize</item>
    <item name="android:minHeight">?attr/minTouchTargetSize</item>
    <item name="iconGravity">textStart</item>
    <item name="iconPadding">0dp</item>
    <item name="iconTint">@color/material_timepicker_modebutton_tint</item>
    <item name="rippleColor">@color/mtrl_on_surface_ripple_color</item>
    <item name="shapeAppearance">@style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance</item>
  </style>
    <style name="Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance" parent="">
    <item name="cornerSize">50%</item>
    <item name="cornerFamily">rounded</item>
  </style>
    <style name="Widget.MaterialComponents.Toolbar" parent="Widget.AppCompat.Toolbar">
    <item name="titleTextAppearance">?attr/textAppearanceHeadline6</item>
    <item name="titleTextColor">?android:attr/textColorPrimary</item>
    <item name="subtitleTextAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="subtitleTextColor">?android:attr/textColorSecondary</item>
    <!-- Overrides minimum height in landscape to avoid headline6 and subtitle1 height concerns. -->
    <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
    <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
  </style>
    <style name="Widget.MaterialComponents.Toolbar.Primary">
    <item name="android:elevation" ns1:ignore="NewApi">@dimen/design_appbar_elevation</item>
    <item name="android:background">?attr/colorPrimary</item>
    <item name="titleTextColor">?attr/colorOnPrimary</item>
    <item name="subtitleTextColor">@color/material_on_primary_emphasis_medium</item>
    <!-- Note: this theme overlay will only work if the style is applied directly to a Toolbar. -->
    <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Toolbar.Primary</item>
    <item name="popupTheme">@style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary</item>
  </style>
    <style name="Widget.MaterialComponents.Toolbar.PrimarySurface" parent="Widget.MaterialComponents.Toolbar.Primary"/>
    <style name="Widget.MaterialComponents.Toolbar.Surface">
    <item name="android:background">?attr/colorSurface</item>
    <item name="titleTextColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="subtitleTextColor">@color/material_on_surface_emphasis_medium</item>
    <!-- Note: this theme overlay will only work if the style is applied directly to a Toolbar. -->
    <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Toolbar.Surface</item>
  </style>
    <style name="Widget.MaterialComponents.Tooltip" parent="android:Widget">
    <item name="android:layout_margin">8dp</item>
    <item name="android:minWidth">@dimen/mtrl_tooltip_minWidth</item>
    <item name="android:minHeight">@dimen/mtrl_tooltip_minHeight</item>
    <item name="android:padding">@dimen/mtrl_tooltip_padding</item>
    <item name="android:textAppearance">@style/TextAppearance.MaterialComponents.Tooltip</item>
    <item name="shapeAppearance">@style/ShapeAppearance.MaterialComponents.Tooltip</item>
  </style>
    <declare-styleable name="AppBarLayout">
    <!-- Deprecated. Elevation is now controlled via a state list animator. -->
    <attr name="elevation"/>
    <attr name="android:background"/>
    <!-- The initial expanded state for the AppBarLayout. This only takes effect when this
         view is a direct child of a CoordinatorLayout. -->
    <attr format="boolean" name="expanded"/>
    <attr name="android:keyboardNavigationCluster"/>
    <attr name="android:touchscreenBlocksFocus"/>
    <!-- Whether the {@link AppBarLayout} should lift on scroll. If set to
         true, the {@link AppBarLayout} will animate to the lifted, or
         elevated, state when content is scrolled beneath it. Requires
         `app:layout_behavior="@string/appbar_scrolling_view_behavior` to be
         set on the scrolling sibling (e.g., `NestedScrollView`,
         `RecyclerView`, etc.). Default is false. -->
    <attr format="boolean" name="liftOnScroll"/>
    <!-- The id of the view that the {@link AppBarLayout} should use to determine whether
         it should be lifted (i.e., only if {@code liftOnScroll} is set to true).
         If this id is not set, the {@link AppBarLayout} will use the target view provided
         by nested scrolling to determine whether it should be lifted. -->
    <attr format="reference" name="liftOnScrollTargetViewId"/>
    <!-- The drawable to display in front of the layout's content, but behind the status bar.
         Only works on Lollipop when used together with android:fitSystemWindows="true". -->
    <attr format="color" name="statusBarForeground"/>
  </declare-styleable>
    <declare-styleable name="AppBarLayoutStates">
    <!-- @deprecated in favor of {@code state_lifted}. -->
    <attr format="boolean" name="state_collapsed"/>
    <!-- @deprecated in favor of {@code state_liftable}. -->
    <attr format="boolean" name="state_collapsible"/>
    <!-- State value for {@link com.google.android.material.appbar.AppBarLayout} set when the view
         is lifted (elevated). -->
    <attr format="boolean" name="state_lifted"/>
    <!-- State value for {@link com.google.android.material.appbar.AppBarLayout} set when the view
         is liftable (e.g., elevates on collapse or scroll). -->
    <attr format="boolean" name="state_liftable"/>
  </declare-styleable>
    <declare-styleable name="AppBarLayout_Layout">
    <attr name="layout_scrollFlags">
      <!-- Disable scrolling on the view. This flag should not be combined with any of the other
           scroll flags. -->
      <flag name="noScroll" value="0x0"/>

      <!-- The view will be scroll in direct relation to scroll events. This flag needs to be
           set for any of the other flags to take effect. If any sibling views
           before this one do not have this flag, then this value has no effect. -->
      <flag name="scroll" value="0x1"/>

      <!-- When exiting (scrolling off screen) the view will be scrolled until it is
           'collapsed'. The collapsed height is defined by the view's minimum height. -->
      <flag name="exitUntilCollapsed" value="0x2"/>

      <!-- When entering (scrolling on screen) the view will scroll on any downwards
           scroll event, regardless of whether the scrolling view is also scrolling. This
           is commonly referred to as the 'quick return' pattern. -->
      <flag name="enterAlways" value="0x4"/>

      <!-- An additional flag for 'enterAlways' which modifies the returning view to
           only initially scroll back to it's collapsed height. Once the scrolling view has
           reached the end of it's scroll range, the remainder of this view will be scrolled
           into view. -->
      <flag name="enterAlwaysCollapsed" value="0x8"/>

      <!-- Upon a scroll ending, if the view is only partially visible then it will be
           snapped and scrolled to it's closest edge. -->
      <flag name="snap" value="0x10"/>

      <!-- An additional flag to be used with 'snap'. If set, the view will be snapped to its
           top and bottom margins, as opposed to the edges of the view itself. -->
      <flag name="snapMargins" value="0x20"/>
    </attr>

    <attr format="enum" name="layout_scrollEffect">
      <!-- No effect will be applied to this child when its parent
           AppBarLayout's offset changes.-->
      <enum name="none" value="0"/>
      <!-- This view will be compressed (masked and parallaxed) when it reaches
           the top of the screen and continues to scroll out of view.-->
      <enum name="compress" value="1"/>
    </attr>

    <!-- An interpolator to use when scrolling this View. Only takes effect when View
         is scrollable. -->
    <attr format="reference" name="layout_scrollInterpolator"/>
  </declare-styleable>
    <declare-styleable name="Badge">
    <!-- The radius of a badge without text (a "dot"). -->
    <attr format="dimension" name="badgeRadius"/>
    <!-- Start and end padding for a badge that is oblong due to long text. -->
    <attr format="dimension" name="badgeWidePadding"/>
    <!-- Radius of a badge with text. -->
    <attr format="dimension" name="badgeWithTextRadius"/>
    <attr format="color" name="backgroundColor"/>
    <attr format="color" name="badgeTextColor"/>
    <attr format="integer" name="maxCharacterCount"/>
    <attr format="integer" name="number"/>
    <attr name="badgeGravity">
      <!-- Gravity.TOP | Gravity.END -->
      <enum name="TOP_END" value="8388661"/>
      <!-- Gravity.TOP | Gravity.START -->
      <enum name="TOP_START" value="8388659"/>
      <!-- Gravity.BOTTOM | Gravity.END -->
      <enum name="BOTTOM_END" value="8388693"/>
      <!-- Gravity.BOTTOM | Gravity.START -->
      <enum name="BOTTOM_START" value="8388691"/>
    </attr>

    <!-- Offsets the badge horizontally towards the center of its anchor when
        the badge doesn't have text (is a "dot"). Defaults to 0. -->
    <attr format="dimension" name="horizontalOffset"/>
    <!-- Offsets the badge vertically towards the center of its anchor when the
         badge doesn't have text (is a "dot"). Defaults to 0. -->
    <attr format="dimension" name="verticalOffset"/>
    <!-- Offsets the badge horizontally towards the center of its anchor when
         the badge has text. If this is not defined, it will default to
         horizontalOffset's value. -->
    <attr format="dimension" name="horizontalOffsetWithText"/>
    <!-- Offsets the badge vertically towards the center of its anchor when the
         badge has text. If this is not defined, it will default to
         verticalOffset's value. -->
    <attr format="dimension" name="verticalOffsetWithText"/>
  </declare-styleable>
    <declare-styleable name="BaseProgressIndicator">
    <!-- Whether the progress indicator should be indeterminate mode. -->
    <attr name="android:indeterminate"/>
    <!-- The thickness of the progress track and indicator. -->
    <attr format="dimension" name="trackThickness"/>
    <!--
      The radius of each corner of both the indicator and the track. A radius
      larger than half of the track width will throw exceptions during
      initialization.
    -->
    <attr format="dimension" name="trackCornerRadius"/>
    <!--
      The indicator color (or colors in an array). By default, it uses theme
      primary color.
    -->
    <attr format="color|reference" name="indicatorColor"/>
    <!--
      The color used for the progress track. If not defined, it will be set to
      the indicatorColor and apply the android:disabledAlpha from the theme.
    -->
    <attr name="trackColor"/>
    <!-- The animation behavior to show the indicator and track. -->
    <attr name="showAnimationBehavior">
      <!-- No animation used; appears immediately. -->
      <enum name="none" value="0"/>
      <!--
        Expands from the bottom edge to the top edge for the linear type;
        expands from the inner edge to the outer edge for the circular type.
      -->
      <enum name="outward" value="1"/>
      <!--
        Expands from the top edge to the bottom edge for the linear type;
        expands from the outer edge to the inner edge for the circular type.
      -->
      <enum name="inward" value="2"/>
    </attr>
    <!-- The animation behavior to hide the indicator and track. -->
    <attr name="hideAnimationBehavior">
      <!-- No animation used; disappears immediately. -->
      <enum name="none" value="0"/>
      <!--
        Collapses from the bottom edge to the top edge for the linear type;
        collapses from the inner edge to the outer edge for the circular type.
      -->
      <enum name="outward" value="1"/>
      <!--
        Collapses from the top edge to the bottom edge for the linear type;
        collapses from the outer edge to the inner edge for the circular type.
      -->
      <enum name="inward" value="2"/>
    </attr>
    <!--
      The time, in milliseconds, that the progress indicator will wait to show
      once show() is called. If set to zero or negative values (-1 as default),
      the show action will start immediately.
    -->
    <attr format="integer" name="showDelay"/>
    <!--
      The minimum time, in milliseconds, that the requested hide action will
      wait to start once show action is started. If set to zero or negative
      values (-1 as default), the requested hide action will start immediately.
      This value is capped to a limit defined in ProgressIndicator class.
    -->
    <attr format="integer" name="minHideDelay"/>
  </declare-styleable>
    <declare-styleable name="BottomAppBar">
    <!-- Background for the BottomAppBar. -->
    <attr name="backgroundTint"/>
    <!-- The tint color for the navigation button icon drawable corresponding to
         the "app:navigationIcon" attribute. -->
    <attr name="navigationIconTint"/>
    <!-- Elevation for the BottomAppBar. -->
    <attr name="elevation"/>
    <!-- The alignment of the fab relative to the BottomAppBar. -->
    <attr name="fabAlignmentMode">
      <!-- Mode that aligns the fab to the center. -->
      <enum name="center" value="0"/>
      <!-- Mode that aligns the fab to the end. -->
      <enum name="end" value="1"/>
    </attr>
    <!-- The animation mode that should be used when the fab animates between alignment modes. -->
    <attr name="fabAnimationMode">
      <!-- Mode that scales the fab down to a point, moves it, then scales the fab back to its normal size. -->
      <enum name="scale" value="0"/>
      <!-- Mode that slides the fab from one alignment mode to the next. -->
      <enum name="slide" value="1"/>
    </attr>
    <!-- The margin between the semi-circular cradle for the fab and the fab. -->
    <attr format="dimension" name="fabCradleMargin"/>
    <!-- The radius of the rounded corners on each side of the cradle. -->
    <attr format="dimension" name="fabCradleRoundedCornerRadius"/>
    <!-- The vertical offset between the fab from the cradle. -->
    <attr format="dimension" name="fabCradleVerticalOffset"/>
    <!-- Whether the BottomAppBar should hide when a NestedScrollView is scrolled. -->
    <attr format="boolean" name="hideOnScroll"/>
    <!-- Whether the BottomAppBar should apply padding to be above the bottom window insets. -->
    <attr name="paddingBottomSystemWindowInsets"/>
    <!-- Whether the BottomAppBar should apply padding to be to the right of the left window insets. -->
    <attr name="paddingLeftSystemWindowInsets"/>
    <!-- Whether the BottomAppBar should apply padding to be to the left of the right window insets. -->
    <attr name="paddingRightSystemWindowInsets"/>
  </declare-styleable>
    <declare-styleable name="BottomNavigationView">
    <!-- Whether the items translate horizontally when in "selected" label visibility mode. -->
    <attr format="boolean" name="itemHorizontalTranslationEnabled"/>
    <!-- The min height this view should maintain. -->
    <attr name="android:minHeight"/>
  </declare-styleable>
    <declare-styleable name="BottomSheetBehavior_Layout">
    <!-- The height of the bottom sheet when it is collapsed. -->
    <attr format="dimension" name="behavior_peekHeight">
      <!-- Peek at the 16:9 ratio keyline of its parent -->
      <enum name="auto" value="-1"/>
    </attr>
    <!-- Whether this bottom sheet can be hidden by dragging it further downwards -->
    <attr format="boolean" name="behavior_hideable"/>
    <!-- Skip the collapsed state once expanded; no effect unless it is hideable -->
    <attr format="boolean" name="behavior_skipCollapsed"/>
    <!-- Whether height of expanded sheet wraps content or not -->
    <attr format="boolean" name="behavior_fitToContents"/>
    <!-- Whether this bottom sheet is draggable. If not, the app will have to supply different
         means to expand and collapse the sheet -->
    <attr format="boolean" name="behavior_draggable"/>
    <!-- The ratio to be used to set the height of half-expanded state in proportion to parent, when
         fitToContents is false. Defaults to true half, 0.5, if not explicitly set. Ratio must be a
         float value between 0 and 1 and produce a half-expanded state height larger than the
         peek height for the half-expanded state to be operational -->
    <attr format="reference|float" name="behavior_halfExpandedRatio"/>
    <!-- The top offset of the BottomSheet in the expanded-state when fitsToContent is false.
         The default value is 0, which results in the sheet matching the parent's top. -->
    <attr format="reference|dimension" name="behavior_expandedOffset"/>
    <!-- Shape appearance style reference for BottomSheet. Attribute declaration is in the shape
         package. -->
     <attr name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference for BottomSheet. To be used to augment attributes
         declared in the shapeAppearance. Attribute declaration is in the shape package. -->
    <attr name="shapeAppearanceOverlay"/>
    <!-- Background color used by the BottomSheetBehavior background drawable when shape theming is
         enabled. Accepts a ColorStateList or ColorInt. If shape theming is not enabled,
         android:background should instead be utilized to set the background resource. -->
    <attr name="backgroundTint"/>
    <!-- Behavior properties will be saved and restored by evaluating each flag.
          usage: app:behavior_saveFlags=”hideable|skipCollapsed” -->
    <attr name="behavior_saveFlags">
      <!-- This flag will preserve the peekHeight on configuration change. -->
      <flag name="peekHeight" value="0x1"/>
      <!-- This flag will preserve the fitToContents boolean value on configuration change. -->
      <flag name="fitToContents" value="0x2"/>
      <!-- This flag will preserve the hideable boolean value on configuration change. -->
      <flag name="hideable" value="0x4"/>
      <!-- This flag will preserve the skipCollapsed boolean value on configuration change. -->
      <flag name="skipCollapsed" value="0x8"/>
      <!-- This flag will preserve the all the aforementioned values on configuration change. -->
      <flag name="all" value="-1"/>
      <!-- This flag will not preserve the aforementioned values on configuration change. The only
           value preserved will be the positional state, e.g. collapsed, hidden, expanded, etc.
           This is the default behavior. -->
      <flag name="none" value="0"/>
    </attr>
    <!-- Sets whether this bottom sheet should adjust it's position based on the system gesture area
    on Android Q and above. Value is false (insets respected) by default. -->
    <attr format="boolean" name="gestureInsetBottomIgnored"/>
    <attr name="paddingBottomSystemWindowInsets"/>
    <attr name="paddingLeftSystemWindowInsets"/>
    <attr name="paddingRightSystemWindowInsets"/>
    <attr name="paddingTopSystemWindowInsets"/>
    <attr name="android:elevation"/>
    <attr name="android:maxWidth"/>
    <attr name="android:maxHeight"/>

  </declare-styleable>
    <declare-styleable name="Chip">
    <!-- Surface layer color to apply to the chip. -->
    <!-- Do not expose publicly because there is no public getter/setter and the user doesn't need
         to configure this.-->
    <attr format="color" name="chipSurfaceColor"/>
    <!-- Background color to apply to the chip. -->
    <attr format="color" name="chipBackgroundColor"/>
    <!-- Min height to apply to the chip. Total height includes stroke width. -->
    <attr format="dimension" name="chipMinHeight"/>
    <!-- Corner radius to apply to the chip's shape. -->
    <attr format="dimension" name="chipCornerRadius"/>
    <!-- Stroke color to apply to the chip's outline. -->
    <attr format="color" name="chipStrokeColor"/>
    <!-- Stroke width to apply to the chip's outline. -->
    <attr format="dimension" name="chipStrokeWidth"/>
    <!-- Ripple color to apply to the chip. -->
    <attr name="rippleColor"/>
    <!-- Minimum size of chip's touch target, by default, Android recommended 48dp. -->
    <attr format="dimension" name="chipMinTouchTargetSize"/>
    <!-- Whether to extend the bounds of chip to meet chipMinTouchTargetSize. -->
    <attr name="ensureMinTouchTargetSize"/>

    <!-- Text to display on the chip. -->
    <attr name="android:text"/>
    <!-- Text color. -->
    <attr name="android:textColor"/>
    <!-- Default appearance of text: color, typeface, size, and style. -->
    <attr name="android:textAppearance"/>
    <!-- Text size. Overrides the size set in the textAppearance -->
    <attr name="android:textSize"/>
    <!-- If set, causes words that are longer than the view is wide to be ellipsized instead of
         truncated at the end. -->
    <attr name="android:ellipsize"/>
    <!-- Make the Chip to be at most this many pixels wide. -->
    <attr name="android:maxWidth"/>

    <!-- Whether to show the chip icon. -->
    <attr format="boolean" name="chipIconVisible"/>
    <!-- Deprecated. Use chipIconVisible instead. -->
    <attr format="boolean" name="chipIconEnabled"/>
    <!-- Icon drawable to display at the start of the chip. -->
    <attr format="reference" name="chipIcon"/>
    <!-- Tint to apply to the chip icon. -->
    <attr format="color" name="chipIconTint"/>
    <!-- Size of the chip's icon and checked icon. -->
    <attr format="dimension" name="chipIconSize"/>

    <!-- Whether to show the close icon. -->
    <attr format="boolean" name="closeIconVisible"/>
    <!-- Deprecated. Use closeIconVisible instead. -->
    <attr format="boolean" name="closeIconEnabled"/>
    <!-- Close icon drawable to display at the end of the chip. -->
    <attr format="reference" name="closeIcon"/>
    <!-- Tint to apply to the chip's close icon. -->
    <attr format="color" name="closeIconTint"/>
    <!-- Size of the chip's close icon. -->
    <attr format="dimension" name="closeIconSize"/>

    <!-- Whether the chip can be checked. If false, the chip will act as a button. -->
    <attr name="android:checkable"/>
    <!-- Whether to show the checked icon. -->
    <attr format="boolean" name="checkedIconVisible"/>
    <!-- Deprecated. Use checkedIconVisible instead -->
    <attr format="boolean" name="checkedIconEnabled"/>
    <!-- Check icon drawable to overlay the chip's icon. -->
    <attr name="checkedIcon"/>
    <!-- Tint to apply to the chip's checked icon. -->
    <attr name="checkedIconTint"/>

    <!-- Motion spec for show animation. This should be a MotionSpec resource. -->
    <attr name="showMotionSpec"/>
    <!-- Motion spec for hide animation. This should be a MotionSpec resource. -->
    <attr name="hideMotionSpec"/>

    <!-- Shape appearance style reference for Chip. Attribute declaration is in the Shape
     package. -->
    <attr name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference for Chip. To be used to augment attributes
         declared in the shapeAppearance. Attribute declaration is in the Shape package. -->
    <attr name="shapeAppearanceOverlay"/>

    <!-- The following attributes are adjustable padding on the chip, listed from start to end. -->

    <!-- Chip starts here. -->

    <!-- Padding at the start of the chip, before the icon. -->
    <attr format="dimension" name="chipStartPadding"/>
    <!-- Padding at the start of the icon, after the start of the chip. If icon exists. -->
    <attr format="dimension" name="iconStartPadding"/>

    <!-- Icon is here. -->

    <!-- Padding at the end of the icon, before the text. If icon exists. -->
    <attr format="dimension" name="iconEndPadding"/>
    <!-- Padding at the start of the text, after the icon. -->
    <attr format="dimension" name="textStartPadding"/>

    <!-- Text is here. -->

    <!-- Padding at the end of the text, before the close icon. -->
    <attr format="dimension" name="textEndPadding"/>
    <!-- Padding at the start of the close icon, after the text. If close icon exists. -->
    <attr format="dimension" name="closeIconStartPadding"/>

    <!-- Close icon is here. -->

    <!-- Padding at the end of the close icon, before the end of the chip. If close icon exists. -->
    <attr format="dimension" name="closeIconEndPadding"/>
    <!-- Padding at the end of the chip, after the close icon. -->
    <attr format="dimension" name="chipEndPadding"/>

    <!-- Chip ends here. -->
  </declare-styleable>
    <declare-styleable name="ChipGroup">

    <!-- Horizontal and vertical spacing between chips in this group. -->
    <attr format="dimension" name="chipSpacing"/>
    <!-- Horizontal spacing between chips in this group. -->
    <attr format="dimension" name="chipSpacingHorizontal"/>
    <!-- Vertical spacing between chips in this group. -->
    <attr format="dimension" name="chipSpacingVertical"/>

    <!-- Constrains the chips in this group to a single horizontal line. By default, this is false
         and the chips in this group will reflow to multiple lines.

         If you set this to true, you'll usually want to wrap this ChipGroup in a
         HorizontalScrollView. -->
    <attr format="boolean" name="singleLine"/>

    <!-- Whether only a single chip in this group is allowed to be checked at any time. By default,
         this is false and multiple chips in this group are allowed to be checked at once. -->
    <attr name="singleSelection"/>
    <!-- Whether we prevent all child chips from being deselected.
         It's false by default. -->
    <attr name="selectionRequired"/>
    <!-- The id of the child chip that should be checked by default within this chip group. -->
    <attr format="reference" name="checkedChip"/>

  </declare-styleable>
    <declare-styleable name="CircularProgressIndicator">
    <!--
      Defines the size (outer diameter) of the circular progress indicator.
    -->
    <attr format="dimension" name="indicatorSize"/>
    <!--
      The extra space from the outer edge of the indicator to the edge of the
      canvas.
    -->
    <attr format="dimension" name="indicatorInset"/>
    <!--
      The direction in which the circular indicator progresses, in the
      determinate mode, and is animated, in the indeterminate mode.
    -->
    <attr name="indicatorDirectionCircular">
      <!--
        In the indeterminate mode, the spinner will spin clockwise; in the
        determinate mode, the indicator will progress from the top (12 o'clock)
        clockwise.
      -->
      <enum name="clockwise" value="0"/>
      <!--
        In the indeterminate mode, the spinner will spin counter-clockwise; in
        the determinate mode, the indicator will progress from the top (12
        o'clock) counter-clockwise.
      -->
      <enum name="counterclockwise" value="1"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="ClockFaceView">

    <attr format="color" name="clockNumberTextColor"/>
    <attr format="color" name="clockFaceBackgroundColor"/>

  </declare-styleable>
    <declare-styleable name="ClockHandView">

    <attr format="color" name="clockHandColor"/>

    <attr name="materialCircleRadius"/>

    <attr format="dimension" name="selectorSize"/>
  </declare-styleable>
    <declare-styleable name="CollapsingToolbarLayout">
    <!--  Specifies extra space on the start, top, end and bottom
          sides of the the expanded title text. Margin values should be positive. -->
    <attr format="dimension" name="expandedTitleMargin"/>
    <!--  Specifies extra space on the start side of the the expanded title text.
          Margin values should be positive. -->
    <attr format="dimension" name="expandedTitleMarginStart"/>
    <!--  Specifies extra space on the top side of the the expanded title text.
          Margin values should be positive. -->
    <attr format="dimension" name="expandedTitleMarginTop"/>
    <!--  Specifies extra space on the end side of the the expanded title text.
          Margin values should be positive. -->
    <attr format="dimension" name="expandedTitleMarginEnd"/>
    <!--  Specifies extra space on the bottom side of the the expanded title text.
          Margin values should be positive. -->
    <attr format="dimension" name="expandedTitleMarginBottom"/>
    <!-- The text appearance of the CollapsingToolbarLayout's title when it is fully
         'expanded' -->
    <attr format="reference" name="expandedTitleTextAppearance"/>
    <!-- The text appearance of the CollapsingToolbarLayouts title when it is fully
         'collapsed' -->
    <attr format="reference" name="collapsedTitleTextAppearance"/>
    <!-- The text color of the CollapsingToolbarLayout's title when it is fully
         'expanded' -->
    <attr format="color|reference" name="expandedTitleTextColor"/>
    <!-- The text color of the CollapsingToolbarLayouts title when it is fully
         'collapsed' -->
    <attr format="color|reference" name="collapsedTitleTextColor"/>
    <!-- The drawable to use as a scrim on top of the CollapsingToolbarLayouts content when
         it has been scrolled sufficiently off screen. -->
    <attr format="color" name="contentScrim"/>
    <!-- The drawable to use as a scrim for the status bar content when the
         CollapsingToolbarLayout has been scrolled sufficiently off screen. Only works on
         Lollipop when used together with android:fitSystemWindows="true". -->
    <attr format="color" name="statusBarScrim"/>
    <!-- The id of the primary Toolbar child that you wish to use for the purpose of collapsing.
         This Toolbar descendant view does not need to be a direct child of the layout.
         If you do not set this, the first direct Toolbar child found will be used. -->
    <attr format="reference" name="toolbarId"/>
    <!-- Specifies the amount of visible height in pixels used to define when to trigger a
         scrim visibility change. -->
    <attr format="dimension" name="scrimVisibleHeightTrigger"/>
    <!-- Specifies the duration used for scrim visibility animations. -->
    <attr format="integer" name="scrimAnimationDuration"/>

    <!-- Specifies how the title should be positioned when collapsed. -->
    <attr name="collapsedTitleGravity">
      <!-- Push title to the top of its container, not changing its size. -->
      <flag name="top" value="0x30"/>
      <!-- Push title to the bottom of its container, not changing its size. -->
      <flag name="bottom" value="0x50"/>
      <!-- Push title to the left of its container, not changing its size. -->
      <flag name="left" value="0x03"/>
      <!-- Push title to the right of its container, not changing its size. -->
      <flag name="right" value="0x05"/>
      <!-- Place title in the vertical center of its container, not changing its size. -->
      <flag name="center_vertical" value="0x10"/>
      <!-- Grow the vertical size of the title if needed so it completely fills its container. -->
      <flag name="fill_vertical" value="0x70"/>
      <!-- Place title in the horizontal center of its container, not changing its size. -->
      <flag name="center_horizontal" value="0x01"/>
      <!-- Place the title in the center of its container in both the vertical and horizontal axis, not changing its size. -->
      <flag name="center" value="0x11"/>
      <!-- Push title to the beginning of its container, not changing its size. -->
      <flag name="start" value="0x00800003"/>
      <!-- Push title to the end of its container, not changing its size. -->
      <flag name="end" value="0x00800005"/>
    </attr>

    <!-- Specifies how the title should be positioned when expanded. -->
    <attr name="expandedTitleGravity">
      <!-- Push title to the top of its container, not changing its size. -->
      <flag name="top" value="0x30"/>
      <!-- Push title to the bottom of its container, not changing its size. -->
      <flag name="bottom" value="0x50"/>
      <!-- Push title to the left of its container, not changing its size. -->
      <flag name="left" value="0x03"/>
      <!-- Push title to the right of its container, not changing its size. -->
      <flag name="right" value="0x05"/>
      <!-- Place title in the vertical center of its container, not changing its size. -->
      <flag name="center_vertical" value="0x10"/>
      <!-- Grow the vertical size of the title if needed so it completely fills its container. -->
      <flag name="fill_vertical" value="0x70"/>
      <!-- Place title in the horizontal center of its container, not changing its size. -->
      <flag name="center_horizontal" value="0x01"/>
      <!-- Place the title in the center of its container in both the vertical and horizontal axis, not changing its size. -->
      <flag name="center" value="0x11"/>
      <!-- Push title to the beginning of its container, not changing its size. -->
      <flag name="start" value="0x00800003"/>
      <!-- Push title to the end of its container, not changing its size. -->
      <flag name="end" value="0x00800005"/>
    </attr>

    <!-- Whether the CollapsingToolbarLayout should draw its own collapsing title. -->
    <attr format="boolean" name="titleEnabled"/>
    <!-- The title to show when titleEnabled is set to true. -->
    <attr name="title"/>
    <!-- Mode to specify the effect used to collapse and expand the title text. -->
    <attr name="titleCollapseMode">
      <!-- The expanded title will continuously scale and translate to its final collapsed position. -->
      <enum name="scale" value="0"/>
      <!-- The expanded title will fade out and translate, and the collapsed title will fade in. -->
      <enum name="fade" value="1"/>
    </attr>
    <!-- The maximum number of lines to display in the expanded state. Experimental Feature. -->
    <attr format="integer" name="maxLines"/>
    <!-- Whether the system window inset top should be applied regardless of
         what the layout_height is set to. Experimental Feature. -->
    <attr format="boolean" name="forceApplySystemWindowInsetTop"/>
    <!-- Whether extra height should be added when the title text spans across
         multiple lines. Experimental Feature. -->
    <attr format="boolean" name="extraMultilineHeightEnabled"/>

    <!-- The interpolator to use when animating the title position from collapsed to expanded and
         vice versa. -->
    <attr format="reference" name="titlePositionInterpolator"/>
  </declare-styleable>
    <declare-styleable name="CollapsingToolbarLayout_Layout">
    <attr name="layout_collapseMode">
      <!-- The view will act as normal with no collapsing behavior. -->
      <enum name="none" value="0"/>
      <!-- The view will pin in place. -->
      <enum name="pin" value="1"/>
      <!-- The view will scroll in a parallax fashion. See the
           layout_collapseParallaxMultiplier attribute to change the multiplier. -->
      <enum name="parallax" value="2"/>
    </attr>

    <!-- The multiplier used when layout_collapseMode is set to 'parallax'. The value should
         be between 0.0 and 1.0. -->
    <attr format="float" name="layout_collapseParallaxMultiplier"/>
  </declare-styleable>
    <declare-styleable name="ExtendedFloatingActionButton">
    <!-- Elevation for the ExtendedFloatingActionButton. -->
    <attr name="elevation"/>
    <!-- Motion spec for show animation. This should be a MotionSpec resource. -->
    <attr name="showMotionSpec"/>
    <!-- Motion spec for hide animation. This should be a MotionSpec resource. -->
    <attr name="hideMotionSpec"/>
    <!-- Motion spec for extend animation. This should be a MotionSpec resource. -->
    <attr format="reference" name="extendMotionSpec"/>
    <!-- Motion spec for shrink animation. This should be a MotionSpec resource. -->
    <attr format="reference" name="shrinkMotionSpec"/>
    <!-- FAB size when collapsed. -->
    <attr format="dimension" name="collapsedSize"/>
  </declare-styleable>
    <declare-styleable name="ExtendedFloatingActionButton_Behavior_Layout">
    <!-- Whether the extended FAB should automatically hide when there is no space for it. -->
    <attr name="behavior_autoHide"/>
    <!-- Whether the extended FAB should automatically shrink when there is no space for it. -->
    <attr format="boolean" name="behavior_autoShrink"/>
  </declare-styleable>
    <declare-styleable name="FloatingActionButton">
    <!-- Whether the FloatingActionButton is enabled -->
    <attr name="android:enabled"/>
    <!-- Background for the FloatingActionButton -->
    <attr name="backgroundTint"/>
    <attr name="backgroundTintMode"/>

    <!-- Ripple color for the FAB. -->
    <attr name="rippleColor"/>

    <!-- Size for the FAB. If fabCustomSize is set, this will be ignored. -->
    <attr name="fabSize">
      <!-- A size which will change based on the window size. -->
      <enum name="auto" value="-1"/>
      <!-- The normal sized button. -->
      <enum name="normal" value="0"/>
      <!-- The mini sized button. -->
      <enum name="mini" value="1"/>
    </attr>
    <!-- Custom size for the FAB. If this is set, fabSize will be ignored. -->
    <attr format="dimension" name="fabCustomSize"/>
    <!-- Elevation value for the FAB -->
    <attr name="elevation"/>
    <!-- Whether to extend the bounds of the FloatingActionButton to meet
        @dimen/mtrl_fab_min_touch_target. -->
    <attr name="ensureMinTouchTargetSize"/>
    <!-- TranslationZ value for the FAB when hovered, focused, or hovered and focused. -->
    <attr format="dimension" name="hoveredFocusedTranslationZ"/>
    <!-- TranslationZ value for the FAB when pressed-->
    <attr format="dimension" name="pressedTranslationZ"/>
    <!-- The width of the border around the FAB. -->
    <attr format="dimension" name="borderWidth"/>
    <!-- Enable compat padding. -->
    <attr format="boolean" name="useCompatPadding"/>
    <!-- Maximum icon image size. -->
    <attr format="dimension" name="maxImageSize"/>
    <!-- Motion spec for show animation. This should be a MotionSpec resource.
         If this attr is set to @null, a default animation will be used which
         respects duration and easing theme values.-->
    <attr name="showMotionSpec"/>
    <!-- Motion spec for hide animation. This should be a MotionSpec resource.
         If this attr is set to @null, a default animation will be used which
         respects duration and easing theme values.-->
    <attr name="hideMotionSpec"/>
    <!-- Shape appearance style reference for FloatingActionButton. Attribute declaration
         is in the shape package. -->
    <attr name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference for FloatingActionButton. -->
    <attr name="shapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="FloatingActionButton_Behavior_Layout">
    <!-- Whether the FAB should automatically hide when there is no space for it. -->
    <attr format="boolean" name="behavior_autoHide"/>
  </declare-styleable>
    <declare-styleable name="FlowLayout">
    <!-- Horizontal spacing between two items being laid out. -->
    <attr format="dimension" name="itemSpacing"/>
    <!-- Vertical Spacing between two lines of items being laid out. -->
    <attr format="dimension" name="lineSpacing"/>
  </declare-styleable>
    <declare-styleable name="ForegroundLinearLayout">
    <attr name="android:foreground"/>
    <attr name="android:foregroundGravity"/>
    <!-- Do not expose publicly, used by ForegroundLinearLayout but never set by any style.  -->
    <attr format="boolean" name="foregroundInsidePadding"/>
  </declare-styleable>
    <declare-styleable name="Insets">
    <!-- Just adding the parameters that we need for now. We can add others if we need them, but
         ideally we'll be able to use https://github.com/chrisbanes/insetter once it's ready. -->
    <attr format="boolean" name="paddingBottomSystemWindowInsets"/>
    <attr format="boolean" name="paddingLeftSystemWindowInsets"/>
    <attr format="boolean" name="paddingRightSystemWindowInsets"/>
    <attr format="boolean" name="paddingTopSystemWindowInsets"/>
  </declare-styleable>
    <declare-styleable name="LinearProgressIndicator">
    <!-- The animation style of the indeterminate mode. -->
    <attr name="indeterminateAnimationType">
      <!--
        The track will be filled with three adjacent segments in iterative different colors.
        This type is only available when there are three or more indicator
        colors.
      -->
      <enum name="contiguous" value="0"/>
      <!--
        There will be two disjoint segments in the same color per cycle. The color iterates between cycles.
      -->
      <enum name="disjoint" value="1"/>
    </attr>
    <!--
      The direction in which the linear indicator progresses, in the determinate
      mode, and is animated, in the indeterminate mode.
    -->
    <attr name="indicatorDirectionLinear">
      <!-- Animated from the left end to the right end of the track. -->
      <enum name="leftToRight" value="0"/>
      <!-- Animated from the right end to the left end of the track. -->
      <enum name="rightToLeft" value="1"/>
      <!--
        Animated from the start position to the end position of the track.
        This will be same as the leftToRight for API before 17.
      -->
      <enum name="startToEnd" value="2"/>
      <!--
        Animated from the end position to the start position of the track.
        This will be same as the rightToLeft for API before 17.
      -->
      <enum name="endToStart" value="3"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="MaterialAlertDialog">
    <attr format="dimension" name="backgroundInsetStart"/>
    <attr format="dimension" name="backgroundInsetTop"/>
    <attr format="dimension" name="backgroundInsetEnd"/>
    <attr format="dimension" name="backgroundInsetBottom"/>
  </declare-styleable>
    <declare-styleable name="MaterialAlertDialogTheme">
    <attr format="reference" name="materialAlertDialogTheme"/>
    <attr format="reference" name="materialAlertDialogTitlePanelStyle"/>
    <attr format="reference" name="materialAlertDialogTitleIconStyle"/>
    <attr format="reference" name="materialAlertDialogTitleTextStyle"/>
    <attr format="reference" name="materialAlertDialogBodyTextStyle"/>
    <attr format="integer" name="materialAlertDialogButtonSpacerVisibility"/>
  </declare-styleable>
    <declare-styleable name="MaterialAutoCompleteTextView" parent="AppCompatAutoCompleteTextView">
    <attr name="android:inputType"/>
  </declare-styleable>
    <declare-styleable name="MaterialButton">
    <!-- Whether the button can be checked. -->
    <attr name="android:checkable"/>
    <attr name="android:insetLeft"/>
    <attr name="android:insetRight"/>
    <attr name="android:insetTop"/>
    <attr name="android:insetBottom"/>
    <!-- Background for the MaterialButton. If this is set to a drawable or color, MaterialButton
         will respect this background and not create its own MaterialShapeDrawable for the
         background. This means that the insets, shape appearance, and stroke will be ignored.
         If this attribute is not set or is @empty or @null, a MaterialShapeDrawable will be used
         for the background based on the insets, shape appearance, and stroke. -->
    <attr name="android:background"/>
    <attr name="backgroundTint"/>
    <attr name="backgroundTintMode"/>
    <!-- Elevation for the MaterialButton. -->
    <attr name="elevation"/>
    <!-- Icon drawable to display at the start of this view. -->
    <attr format="reference" name="icon"/>
    <!-- Specifies the width and height to use for the icon drawable. -->
    <attr format="dimension" name="iconSize"/>
    <!-- Padding between icon and button text. -->
    <attr format="dimension" name="iconPadding"/>
    <!-- Specifies how the icon should be positioned on the X axis. -->
    <attr name="iconGravity">
      <!-- Push icon to the start of the button. -->
      <flag name="start" value="0x1"/>
      <!-- Push the icon to the start of the text keeping a distance equal to
           {@code iconPadding} from the text. -->
      <flag name="textStart" value="0x2"/>
      <!-- Push icon to the end of the button. -->
      <flag name="end" value="0x3"/>
      <!-- Push the icon to the end of the text keeping a distance equal to
           {@code iconPadding} from the text. -->
      <flag name="textEnd" value="0x4"/>
      <!-- Push the icon to the top of the button. -->
      <flag name="top" value="0x10"/>
      <!-- Push the icon to the top of the text keeping a distance equal to
           {@code iconPadding} from the text. -->
      <flag name="textTop" value="0x20"/>
    </attr>
    <!-- Tint for icon drawable to display. -->
    <attr format="color" name="iconTint"/>
    <!-- Tint mode for icon drawable to display. -->
    <attr name="iconTintMode"/>
    <!-- Shape appearance style reference for MaterialButton. Attribute declaration is in the Shape
     package. -->
    <attr name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference for MaterialButton. To be used to augment
         attributes declared in the shapeAppearance. Attribute declaration is in the Shape package.
    -->
    <attr name="shapeAppearanceOverlay"/>
    <!-- Specifies the color used to draw the path outline of the button. Attribute type definition
         is in resources package. -->
    <attr name="strokeColor"/>
    <!-- Width of the stroke path of the button. Default is 0. Attribute type definition is in
         resources package. -->
    <attr name="strokeWidth"/>
    <!--
        Specifies the radius for the corners of the button. Default is 0, for non-rounded corners.
    -->
    <attr format="dimension" name="cornerRadius"/>
    <!-- Ripple color for the button. This may be a color state list, if the desired ripple color
         should be stateful. Attribute type definition is in resources package. -->
    <attr name="rippleColor"/>
  </declare-styleable>
    <declare-styleable name="MaterialButtonToggleGroup">
    <!-- Whether only a single button in this group is allowed to be checked at any time. By
         default, this is false and multiple buttons in this group are allowed to be checked at
         once. -->
    <attr name="singleSelection"/>

    <!-- Whether we prevent all child buttons from being deselected.
         It's false by default. -->
    <attr name="selectionRequired"/>
    <!-- The id of the child button that should be checked by default within this button group. -->
    <attr format="reference" name="checkedButton"/>
  </declare-styleable>
    <declare-styleable name="MaterialCalendar">
    <attr name="android:windowFullscreen"/>
    <attr format="reference" name="dayStyle"/>
    <attr format="reference" name="dayInvalidStyle"/>
    <attr format="reference" name="daySelectedStyle"/>
    <attr format="reference" name="dayTodayStyle"/>
    <attr format="reference" name="yearStyle"/>
    <attr format="reference" name="yearSelectedStyle"/>
    <attr format="reference" name="yearTodayStyle"/>
    <attr format="color" name="rangeFillColor"/>
    <attr format="boolean" name="nestedScrollable"/>
  </declare-styleable>
    <declare-styleable name="MaterialCalendarItem">
    <attr name="android:insetLeft"/>
    <attr name="android:insetTop"/>
    <attr name="android:insetRight"/>
    <attr name="android:insetBottom"/>
    <attr format="color" name="itemFillColor"/>
    <attr name="itemTextColor"/>
    <attr format="color" name="itemStrokeColor"/>
    <attr format="dimension" name="itemStrokeWidth"/>
    <attr name="itemShapeAppearance"/>
    <attr name="itemShapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="MaterialCardView">
    <!-- Whether the card can be checked. -->
    <attr name="android:checkable"/>
    <!-- Foreground color for CardView. -->
    <attr format="color" name="cardForegroundColor"/>
    <!-- Check icon drawable. -->
    <attr name="checkedIcon"/>
    <!-- Tint color for the checked icon. -->
    <attr name="checkedIconTint"/>
    <!-- Check icon size for Checkable Cards-->
    <attr format="dimension" name="checkedIconSize"/>
    <!-- Check icon margin for Checkable Cards-->
    <attr format="dimension" name="checkedIconMargin"/>
    <!-- Ripple color for the Card. -->
    <attr name="rippleColor"/>
    <!-- State when a Card is being dragged. -->
    <attr format="boolean" name="state_dragged"/>
    <!-- Specifies the color used to draw the path outline of the card. Attribute type definition is
         in resources package. -->
    <attr name="strokeColor"/>
    <!-- Width of the stroke path of the card. Default is 0. Attribute type definition is in
         resources package. -->
    <attr name="strokeWidth"/>

    <!-- Shape appearance style reference for MaterialCardView. Attribute declaration is in the
         shape package. -->
    <attr name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference for MaterialCardView. To be used to augment
         attributes declared in the shapeAppearance. Attribute declaration is in the shape package.
         -->
    <attr name="shapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="MaterialCheckBox">
    <!-- When set to true, MaterialCheckBox will tint itself according to
      Material Theme colors. When set to false, Material Theme colors will
      be ignored. This value should be set to false when using custom drawables
      that should not be tinted. This value is ignored if a buttonTint is set. -->
    <attr name="useMaterialThemeColors"/>
    <!-- Tint for the checkbox. -->
    <attr name="buttonTint"/>
  </declare-styleable>
    <declare-styleable name="MaterialDivider">
    <!-- Color of the divider. -->
    <attr format="color|reference" name="dividerColor"/>
    <!-- Thickness of the divider. -->
    <attr format="dimension" name="dividerThickness"/>
    <!-- Start indent of the divider. -->
    <attr name="dividerInsetEnd"/>
    <!-- End indent of the divider. -->
    <attr name="dividerInsetStart"/>
  </declare-styleable>
    <declare-styleable name="MaterialRadioButton">
    <!-- When set to true, MaterialRadioButton will tint itself according to
      Material Theme colors. When set to false, Material Theme colors will
      be ignored. This value should be set to false when using custom drawables
      that should not be tinted. This value is ignored if a buttonTint is set. -->
    <attr name="useMaterialThemeColors"/>
    <!-- Tint for the radio. -->
    <attr name="buttonTint"/>
  </declare-styleable>
    <declare-styleable name="MaterialShape">
    <!-- Shape appearance style reference to be used to construct a ShapeAppearanceModel. -->
    <attr format="reference" name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference to be used to modify the shapeAppearance. -->
    <attr format="reference" name="shapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="MaterialTextAppearance" parent="TextAppearance">
    <attr name="android:lineHeight"/>
    <attr format="dimension" name="lineHeight"/>
    <attr name="android:letterSpacing"/>
  </declare-styleable>
    <declare-styleable name="MaterialTextView" parent="AppCompatTextView">
    <attr name="android:textAppearance"/>
    <attr name="android:lineHeight"/>
    <attr name="lineHeight"/>
  </declare-styleable>
    <declare-styleable name="MaterialTimePicker">

    <attr name="keyboardIcon"/>
    <attr name="clockIcon"/>

  </declare-styleable>
    <declare-styleable name="MaterialToolbar">
    <!-- The tint color for the navigation button icon drawable corresponding to
         the "app:navigationIcon" attribute. -->
    <attr name="navigationIconTint"/>
    <!-- Whether the title text corresponding to the "app:title" attribute
         should be centered horizontally within the toolbar. Default is false.
    -->
    <attr format="boolean" name="titleCentered"/>
    <!-- Whether the subtitle text corresponding to the "app:subtitle" attribute
         should be centered horizontally within the toolbar. Default is false.
    -->
    <attr format="boolean" name="subtitleCentered"/>
  </declare-styleable>
    <declare-styleable name="NavigationBarActiveIndicator">
    <!--The color to be used for the navigation item's active indicator. -->
    <attr name="android:color"/>
    <!--The shape appearance style to be used for the navigation items' active indicator. -->
    <attr name="android:width"/>
    <!--The desired height of the navigation items' active indicator. -->
    <attr name="android:height"/>
    <!--The start and end margin used by the navigation items' active indicator. -->
    <attr name="shapeAppearance"/>
    <!--The desired width of the navigation items' active indicator. -->
    <attr format="dimension" name="marginHorizontal"/>
  </declare-styleable>
    <declare-styleable name="NavigationBarView">
    <!-- Background tint for the navigation bar. -->
    <attr name="backgroundTint"/>
    <!-- The menu resource to inflate and populate items from. Attribute type definition is in
         navigation package. -->
    <attr name="menu"/>
    <!-- Whether navigation items display with a label, without a label, or with a label during
         selected state. Can also be "auto", which uses the item count to determine whether to show
         or hide the label. -->
    <attr name="labelVisibilityMode">
      <!-- Label behaves as "labeled" when there are 3 items or less, or "selected" when there are
           4 items or more. -->
      <enum name="auto" value="-1"/>
      <!-- Label is shown on the selected navigation item. -->
      <enum name="selected" value="0"/>
      <!-- Label is shown on all navigation items. -->
      <enum name="labeled" value="1"/>
      <!-- Label is not shown on any navigation items. -->
      <enum name="unlabeled" value="2"/>
    </attr>
    <!-- The background for the navigation items. Attribute type definition is in navigation
         package. -->
    <attr name="itemBackground"/>
    <!-- The ColorStateList to use for a ripple background. This only exists because creating
         ripples in drawable xml based on theme colors is not supported pre-23. This will be ignored
         if itemBackground is set.-->
    <attr format="color" name="itemRippleColor"/>
    <!-- The size to provide for the navigation item icons. -->
    <attr name="itemIconSize"/>
    <!-- The tint to apply to the navigation item icons. Attribute type definition is in navigation
         package. -->
    <attr name="itemIconTint"/>
    <!-- The text appearance to apply to the inactive navigation item labels. Setting
         android:textColor in itemTextAppearanceInactive will take precedence over android:textColor
         in itemTextAppearanceActive. Instead, set itemTextColor with a ColorStateList to make
         the text color stateful. -->
    <attr format="reference" name="itemTextAppearanceInactive"/>
    <!-- The text appearance to apply to the active navigation item label. You should not set
         android:textColor in itemTextAppearanceActive. Instead, set itemTextColor to a
         ColorStateList to make the text color stateful. -->
    <attr format="reference" name="itemTextAppearanceActive"/>
    <!-- The color to apply to the navigation items' text. Setting itemTextColor will take
         precedence over android:textColor in itemTextAppearanceInactive or
         itemTextAppearanceActive. Attribute type definition is in navigation package. -->
    <attr name="itemTextColor"/>
    <!-- The distance from the top of the icon/active indicator to the top of
         the navigation bar item. -->
    <attr format="dimension" name="itemPaddingTop"/>
    <!-- The distance from the bottom of the label to the bottom of the navigation
         bar item.-->
    <attr format="dimension" name="itemPaddingBottom"/>

    <!-- The style used for each navigation item's active indicator-->
    <attr format="reference" name="itemActiveIndicatorStyle"/>

    <!-- The elevation to use for the navigation bar view -->
    <attr name="elevation"/>
  </declare-styleable>
    <declare-styleable name="NavigationRailView">
    <!-- The minimum height of each menu item. If not set, the min height  -->
    <!-- will be the same as the width of the rail. -->
    <attr format="dimension" name="itemMinHeight"/>
    <!-- Specifies the layout that will be used to create the header view, if any -->
    <attr name="headerLayout"/>
   <!-- Specifies how the navigation rail destinations should be aligned as a group. -->
    <attr name="menuGravity">
      <!-- Navigation rail destinations will be aligned as a group at the top. This is the default behavior. -->
      <!-- Gravity.TOP | Gravity.CENTER_HORIZONTAL-->
      <enum name="top" value="49"/>
      <!-- Navigation rail destinations will be aligned as a group at the center. -->
      <!-- Gravity.CENTER -->
      <enum name="center" value="17"/>
      <!-- Navigation rail destinations will be aligned as a group at the bottom. -->
      <!-- Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL -->
      <enum name="bottom" value="81"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="NavigationView">
    <attr name="android:background"/>
    <attr name="android:fitsSystemWindows"/>
    <attr name="android:layout_gravity"/>
    <attr name="android:maxWidth"/>
    <attr name="elevation"/>
    <!-- The menu resource to inflate and populate items from. -->
    <attr format="reference" name="menu"/>
    <attr format="reference|color" name="subheaderColor"/>
    <attr format="reference" name="subheaderTextAppearance"/>
    <attr format="color" name="itemIconTint"/>
    <attr name="itemTextColor"/>
    <!-- A background drawable to use for navigation items. If this is set, this overrides the
         default background drawable for items and the itemShape* attributes will be ignored -->
    <attr format="reference" name="itemBackground"/>
    <attr format="reference" name="itemTextAppearance"/>
    <!-- Layout resource to inflate as the header -->
    <attr name="headerLayout"/>
    <!-- Horizontal padding (left and right) of navigation items, around the icon & text -->
    <attr format="dimension" name="itemHorizontalPadding"/>
    <!-- Vertical padding (top and bottom) of navigation items. -->
    <attr format="dimension" name="itemVerticalPadding"/>
    <!-- Padding between the icon and the text for navigation items that display an icon  -->
    <attr format="dimension" name="itemIconPadding"/>
    <!-- The size of the icon navigation items that display an icon  -->
    <attr format="dimension" name="itemIconSize"/>
    <!-- Makes the TextView of the item text be at most this many lines tall.  -->
    <attr format="integer" min="1" name="itemMaxLines"/>
    <!-- Shape appearance style reference for the shaped item background. To use the shaped
         item background, either itemShapeAppearance or itemShapeAppearanceOverlay must be set and
         itemBackground must be null so that it can be set programmatically. -->
    <attr name="itemShapeAppearance"/>
    <!-- Shape appearance overlay style reference for item background. To be used to augment
         attributes declared in itemShapeAppearance. To use the shaped item background, either
         itemShapeAppearance or itemShapeAppearanceOverlay must be set and itemBackground must be
         null so that it can be set programmatically. -->
    <attr name="itemShapeAppearanceOverlay"/>
    <!-- Inset start margin for the item background shape. Used if itemBackground isn't set and
         there is an itemShapeAppearance or itemShapeAppearanceOverlay. -->
    <attr format="dimension" name="itemShapeInsetStart"/>
    <!-- Inset top margin for the item background shape. Used if itemBackground isn't set and there
         is an itemShapeAppearance or itemShapeAppearanceOverlay. -->
    <attr format="dimension" name="itemShapeInsetTop"/>
    <!-- Inset end margin for the item background shape. Used if itemBackground isn't set and there
         and there is an itemShapeAppearance or itemShapeAppearanceOverlay. -->
    <attr format="dimension" name="itemShapeInsetEnd"/>
    <!-- Inset bottom margin for the item background shape. Used if itemBackground isn't set and
         there is an itemShapeAppearance or itemShapeAppearanceOverlay. -->
    <attr format="dimension" name="itemShapeInsetBottom"/>
    <!-- Fill color for the item background shape. Used if itemBackground isn't set and there is an
         itemShapeAppearance or itemShapeAppearanceOverlay. -->
    <attr format="color" name="itemShapeFillColor"/>
    <!-- Shape appearance style reference for NavigationView. Attribute declaration is in the Shape
         package. -->
    <attr name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference for NavigationView. To be used to augment
         attributes declared in the shapeAppearance. Attribute declaration is in the Shape package.
    -->
    <attr name="shapeAppearanceOverlay"/>
    <!-- Whether or not the navigation view should draw a scrim behind the top window
         inset, usually the status bar, when drawing edge to edge. -->
    <attr format="boolean" name="topInsetScrimEnabled"/>
    <!-- Whether or not the navigation view should draw a scrim behind the bottom window
         inset, usually the navigation area, when drawing edge to edge. -->
    <attr format="boolean" name="bottomInsetScrimEnabled"/>
    <!-- The distance between the start of the navigation view container and a menu divider. -->
    <attr name="dividerInsetStart"/>
    <!-- The distance between the end of the navigation view container and a menu divider. -->
    <attr name="dividerInsetEnd"/>
    <!-- The distance between the start of the navigation view container and a menu group label. -->
    <attr format="dimension" name="subheaderInsetStart"/>
    <!-- The distance between the end of the navigation view container and a menu group label. -->
    <attr format="dimension" name="subheaderInsetEnd"/>
    <!-- Corner size for the exposed corners (those not attatched to the side
         of the screen) when the navigation view is placed inside a drawer layout.-->
    <attr format="dimension" name="drawerLayoutCornerSize"/>
  </declare-styleable>
    <declare-styleable name="RadialViewGroup">

    <!-- Radius indicating how far away from the center of the group the
         children are positioned -->
    <attr name="materialCircleRadius"/>
  </declare-styleable>
    <declare-styleable name="RangeSlider">
    <attr format="reference" name="values"/>

    <!-- If there is more than one thumb, and the slider is
         not discrete the thumbs will be separated by this dimen -->
    <attr format="dimension" name="minSeparation"/>
  </declare-styleable>
    <declare-styleable name="ScrimInsetsFrameLayout">
    <attr format="color|reference" name="insetForeground"/>
  </declare-styleable>
    <declare-styleable name="ScrollingViewBehavior_Layout">
    <!-- The amount that the scrolling view should overlap the bottom of any AppBarLayout -->
    <attr format="dimension" name="behavior_overlapTop"/>
  </declare-styleable>
    <declare-styleable name="ShapeAppearance">
    <!-- Corner size to be used in the ShapeAppearance. All corners default to this value -->
    <attr format="dimension|fraction" name="cornerSize"/>
    <!-- Top left corner size to be used in the ShapeAppearance. -->
    <attr format="dimension|fraction" name="cornerSizeTopLeft"/>
    <!-- Top right corner size to be used in the ShapeAppearance. -->
    <attr format="dimension|fraction" name="cornerSizeTopRight"/>
    <!-- Bottom right corner size to be used in the ShapeAppearance. -->
    <attr format="dimension|fraction" name="cornerSizeBottomRight"/>
    <!-- Bottom left corner size to be used in the ShapeAppearance. -->
    <attr format="dimension|fraction" name="cornerSizeBottomLeft"/>

    <!-- Corner family to be used in the ShapeAppearance. All corners default to this value -->
    <attr format="enum" name="cornerFamily">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
    <!-- Top left corner family to be used in the ShapeAppearance. -->
    <attr format="enum" name="cornerFamilyTopLeft">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
    <!-- Top right corner family to be used in the ShapeAppearance. -->
    <attr format="enum" name="cornerFamilyTopRight">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
    <!-- Bottom right corner family to be used in the ShapeAppearance. -->
    <attr format="enum" name="cornerFamilyBottomRight">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
    <!-- Bottom left corner family to be used in the ShapeAppearance. -->
    <attr format="enum" name="cornerFamilyBottomLeft">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="ShapeableImageView">
    <attr name="strokeWidth"/>
    <attr name="strokeColor"/>

    <!-- Shape appearance style reference for ShapeableImageView. Attribute declaration is in the
         shape package. -->
    <attr name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference for ShapeableImageView. To be used to augment
         attributes declared in the shapeAppearance. Attribute declaration is in the shape package.
         -->
    <attr name="shapeAppearanceOverlay"/>

    <!-- Attributes for padding the image, since android:padding is applied to the background
         for ShapeableImageView
         -->
    <attr name="contentPadding"/>
    <attr name="contentPaddingBottom"/>
    <attr format="dimension" name="contentPaddingEnd"/>
    <attr name="contentPaddingLeft"/>
    <attr name="contentPaddingRight"/>
    <attr format="dimension" name="contentPaddingStart"/>
    <attr name="contentPaddingTop"/>
  </declare-styleable>
    <declare-styleable name="Slider">
    <attr name="android:value"/>
    <attr name="android:valueFrom"/>
    <attr name="android:valueTo"/>
    <attr name="android:stepSize"/>
    <!-- Whether the Slider is enabled. -->
    <attr name="android:enabled"/>
    <!-- The color of the slider's halo. -->
    <attr format="color" name="haloColor"/>
    <!-- The radius of the halo. -->
    <attr format="dimension" name="haloRadius"/>
    <!-- Determines if Slider should increase its default height to include space for the label. -->
    <attr name="labelBehavior">
      <!-- Mode that draws the label floating above the bounds of this view. -->
      <enum name="floating" value="0"/>
      <!-- Mode that draws the label within the bounds of the view. -->
      <enum name="withinBounds" value="1"/>
      <!-- Mode that prevents the label from being drawn -->
      <enum name="gone" value="2"/>
    </attr>
    <!-- The style used for the label TooltipDrawable. -->
    <attr format="reference" name="labelStyle"/>
    <!-- The color of the slider's thumb. -->
    <attr format="color" name="thumbColor"/>
    <!-- The elevation of the thumb. -->
    <attr format="dimension" name="thumbElevation"/>
    <!-- The radius of the thumb. -->
    <attr format="dimension" name="thumbRadius"/>
    <!-- The stroke color for the thumb. -->
    <attr format="color" name="thumbStrokeColor"/>
    <!-- The stroke width for the thumb. -->
    <attr format="dimension" name="thumbStrokeWidth"/>
    <!-- The color of the slider's tick marks. Only used when the slider is in discrete mode. -->
    <attr format="color" name="tickColor"/>
    <!-- The color of the slider's tick marks for the active portion of the track. Only used when
         the slider is in discrete mode. -->
    <attr format="color" name="tickColorActive"/>
    <!-- The color of the slider's tick marks for the inactive portion of the track. Only used when
         the slider is in discrete mode. -->
    <attr format="color" name="tickColorInactive"/>
    <!-- Whether to show the tick marks. Only used when the slider is in discrete mode. -->
    <attr format="boolean" name="tickVisible"/>
    <!-- The color of the track. -->
    <attr name="trackColor"/>
    <!-- The color of active portion of the track. -->
    <attr format="color" name="trackColorActive"/>
    <!-- The color of inactive portion of the track. -->
    <attr format="color" name="trackColorInactive"/>
    <!-- The height of the track. -->
    <attr format="dimension" name="trackHeight"/>
  </declare-styleable>
    <declare-styleable name="Snackbar">
    <!-- Style to use for Snackbars in this theme. -->
    <attr format="reference" name="snackbarStyle"/>
    <!-- Style to use for action button within a Snackbar in this theme. -->
    <attr format="reference" name="snackbarButtonStyle"/>
    <!-- Style to use for message text within a Snackbar in this theme. -->
    <attr format="reference" name="snackbarTextViewStyle"/>
  </declare-styleable>
    <declare-styleable name="SnackbarLayout">
    <attr name="android:maxWidth"/>
    <attr name="elevation"/>
    <attr format="dimension" name="maxActionInlineWidth"/>
    <!-- Sets the enter and exit animations for a Snackbar. -->
    <attr format="enum" name="animationMode">
      <!-- Mode that corresponds to the slide in and out animations. -->
      <enum name="slide" value="0"/>
      <!-- Mode that corresponds to the fade in and out animations. -->
      <enum name="fade" value="1"/>
    </attr>
    <!-- Alpha level for the color that is overlaid on top of the background color. Ignored if
         backgroundTint is set. -->
    <attr format="float" name="backgroundOverlayColorAlpha"/>
    <!-- Background tint used by the Snackbar background drawable. Accepts a ColorStateList or
         ColorInt. -->
    <attr name="backgroundTint"/>
    <!-- Background tint mode used by the Snackbar background drawable. -->
    <attr name="backgroundTintMode"/>
    <!-- Alpha level for the action button text, to allow for adjusting the lightness or darkness of
         the theme color used for Snackbar text buttons (e.g., colorPrimary). -->
    <attr format="float" name="actionTextColorAlpha"/>
  </declare-styleable>
    <declare-styleable name="SwitchMaterial">
    <!-- When set to true, SwitchMaterial will tint itself according to
      Material Theme colors. When set to false, Material Theme colors will
      be ignored. This value should be set to false when using custom drawables
      that should not be tinted. This value is ignored if a buttonTint is set. -->
    <attr name="useMaterialThemeColors"/>
  </declare-styleable>
    <declare-styleable name="TabItem">
    <!-- Text to display in the tab. -->
    <attr name="android:text"/>
    <!-- Icon to display in the tab. -->
    <attr name="android:icon"/>
    <!-- A reference to a layout resource to be displayed in the tab. -->
    <attr name="android:layout"/>
  </declare-styleable>
    <declare-styleable name="TabLayout">
    <!-- Color of the indicator used to show the currently selected tab. -->
    <attr format="color" name="tabIndicatorColor"/>
    <!-- {@deprecated Instead, set the intrinsic size of the custom drawable provided to the
         tabIndicator attribute in order to change the indicator height. For example, this can be
         done by setting the <size> property in a <shape> resource.} -->
    <attr format="dimension" name="tabIndicatorHeight"/>
    <!-- Position in the Y axis from the starting edge that tabs should be positioned from. -->
    <attr format="dimension" name="tabContentStart"/>
    <!-- Reference to a background to be applied to tabs. -->
    <attr format="reference" name="tabBackground"/>
    <!-- Reference to a drawable to use as selection indicator for tabs. If this attribute is not
         specified, indicator defaults to a line along the bottom of the tab. -->
    <attr format="reference" name="tabIndicator"/>
    <!-- Gravity constant for tab selection indicator. -->
    <attr name="tabIndicatorGravity">
      <!-- Align indicator to the bottom of this tab layout. -->
      <enum name="bottom" value="0"/>
      <!-- Align indicator along the center of this tab layout. -->
      <enum name="center" value="1"/>
      <!-- Align indicator to the top of this tab layout. -->
      <enum name="top" value="2"/>
      <!-- Stretch indicator to match the height and width of a tab item in this layout. -->
      <enum name="stretch" value="3"/>
    </attr>
    <!-- Duration in milliseconds for the animation of the selection indicator from one tab item
         to another. -->
    <attr format="integer" name="tabIndicatorAnimationDuration"/>
    <!-- Whether the selection indicator width should fill the full width of the tab item,
         or if it should be fitted to the content of the tab text label. If no text label is
         present, it will be set to the width of the icon or to a minimum width of 24dp. -->
    <attr format="boolean" name="tabIndicatorFullWidth"/>
    <!-- The animation mode used to animate the selection indicator between
         destinations. -->
    <attr name="tabIndicatorAnimationMode">
      <!-- Animate the selection indicator's left and right bounds in step with
           each other. -->
      <enum name="linear" value="0"/>
      <!-- Animate the selection indicator's left and right bounds out of step
           with each other, decelerating the front and accelerating the back.
           This causes the indicator to look like it stretches between destinations
           an then shrinks back down to fit the size of it's target tab. -->
      <enum name="elastic" value="1"/>
    </attr>
    <!-- The behavior mode for the Tabs in this layout -->
    <attr name="tabMode">
      <enum name="scrollable" value="0"/>
      <enum name="fixed" value="1"/>
      <enum name="auto" value="2"/>
    </attr>
    <!-- Gravity constant for tabs. -->
    <attr name="tabGravity">
      <enum name="fill" value="0"/>
      <enum name="center" value="1"/>
      <enum name="start" value="2"/>
    </attr>
    <!-- Whether to display tab labels horizontally inline with icons, or underneath icons. -->
    <attr format="boolean" name="tabInlineLabel"/>
    <!-- The minimum width for tabs. -->
    <attr format="dimension" name="tabMinWidth"/>
    <!-- The maximum width for tabs. -->
    <attr format="dimension" name="tabMaxWidth"/>
    <!-- A reference to a TextAppearance style to be applied to tabs. -->
    <attr format="reference" name="tabTextAppearance"/>
    <!-- The default text color to be applied to tabs. -->
    <attr format="color" name="tabTextColor"/>
    <!-- {@deprecated Instead, provide a ColorStateList to the tabTextColor attribute with a
         selected color set.}  -->
    <attr format="color" name="tabSelectedTextColor"/>
    <!-- The preferred padding along the start edge of tabs. -->
    <attr format="dimension" name="tabPaddingStart"/>
    <!-- The preferred padding along the top edge of tabs. -->
    <attr format="dimension" name="tabPaddingTop"/>
    <!-- The preferred padding along the end edge of tabs. -->
    <attr format="dimension" name="tabPaddingEnd"/>
    <!-- The preferred padding along the bottom edge of tabs. -->
    <attr format="dimension" name="tabPaddingBottom"/>
    <!-- The preferred padding along all edges of tabs. -->
    <attr format="dimension" name="tabPadding"/>
    <!-- Tint to apply to tab icons, if present. This can be a color state list or a color. -->
    <attr format="color" name="tabIconTint"/>
    <!-- Blending mode to apply to tab icons. -->
    <attr name="tabIconTintMode">
      <enum name="src_over" value="3"/>
      <enum name="src_in" value="5"/>
      <enum name="src_atop" value="9"/>
      <enum name="multiply" value="14"/>
      <enum name="screen" value="15"/>
      <enum name="add" value="16"/>
    </attr>
    <!-- Ripple color for the tabs. This may be a color state list, if the desired ripple color
         should be stateful.-->
    <attr format="color" name="tabRippleColor"/>
    <!-- Whether to use unbounded ripple effect for tabs, or if ripple should instead be bound to
         tab item bounds. -->
    <attr format="boolean" name="tabUnboundedRipple"/>
  </declare-styleable>
    <declare-styleable name="TextInputEditText">
    <!-- Whether the TextInputEditText should use the TextInputLayout's focused
         rectangle instead of its own. -->
    <attr format="boolean" name="textInputLayoutFocusedRectEnabled"/>
  </declare-styleable>
    <declare-styleable name="TextInputLayout">
    <!-- Whether the layout is enabled -->
    <attr name="android:enabled"/>
    <!-- The hint to display in the floating label. -->
    <attr name="android:hint"/>
    <!-- The text color for the hint when the text field is not activated (such
         as for the resting and disabled states). -->
    <attr name="android:textColorHint"/>
    <!-- Makes the text field be at least this dimension wide if its width is
         set to wrap_content. -->
    <attr name="android:minWidth"/>
    <!-- Makes the text field be at most this dimension wide if its width is set
         to wrap_content.  -->
    <attr name="android:maxWidth"/>

    <!-- Whether the layout's floating label functionality is enabled. -->
    <attr format="boolean" name="hintEnabled"/>
    <!-- Whether to animate hint state changes. -->
    <attr format="boolean" name="hintAnimationEnabled"/>
    <!-- TextAppearance of the hint in the collapsed floating label. -->
    <attr format="reference" name="hintTextAppearance"/>
    <!-- Text color of the hint in the collapsed floating label.
         If set, this takes precedence over hintTextAppearance. -->
    <attr format="color" name="hintTextColor"/>
    <!-- Whether the hint should occupy the input area when the text field is
         unpopulated and not focused. -->
    <attr format="boolean" name="expandedHintEnabled"/>

    <!-- The text to display as helper text underneath the text input area. -->
    <attr format="string" name="helperText"/>
    <!-- Whether the layout's helper text functionality is enabled. -->
    <attr format="boolean" name="helperTextEnabled"/>
    <!-- TextAppearance of the helper text displayed underneath the text input area. -->
    <attr format="reference" name="helperTextTextAppearance"/>
    <!-- Text color of the helper text displayed underneath the text input area.
         If set, this takes precedence over helperTextTextAppearance. -->
    <attr format="color" name="helperTextTextColor"/>

    <!-- Whether the layout is laid out as if an error will be displayed. -->
    <attr format="boolean" name="errorEnabled"/>
    <!-- TextAppearance of any error message displayed. -->
    <attr format="reference" name="errorTextAppearance"/>
    <!-- Text color for any error message displayed.
         If set, this takes precedence over errorTextAppearance. -->
    <attr format="color" name="errorTextColor"/>
    <!-- Text to set as the content description for the error view.
         Should be set when the error message has special characters that a
         screen reader is not able to announce properly. -->
    <attr format="string" name="errorContentDescription"/>
    <!-- End icon to be shown when an error is displayed. -->
    <attr format="reference" name="errorIconDrawable"/>
    <!-- Tint color to use for the error icon. -->
    <attr format="reference" name="errorIconTint"/>
    <!-- Blending mode used to apply the error icon tint. -->
    <attr name="errorIconTintMode">
      <!-- The tint is drawn on top of the drawable.
           [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
      <enum name="src_over" value="3"/>
      <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
           color channels are thrown out. [Sa * Da, Sc * Da] -->
      <enum name="src_in" value="5"/>
      <!-- The tint is drawn above the drawable, but with the drawable’s alpha
           channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
      <enum name="src_atop" value="9"/>
      <!-- Multiplies the color and alpha channels of the drawable with those of
           the tint. [Sa * Da, Sc * Dc] -->
      <enum name="multiply" value="14"/>
      <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
      <enum name="screen" value="15"/>
    </attr>

    <!-- Whether the layout is laid out as if the character counter will be displayed. -->
    <attr format="boolean" name="counterEnabled"/>
    <!-- The max length to display in the character counter. -->
    <attr format="integer" name="counterMaxLength"/>
    <!-- TextAppearance of the character counter. -->
    <attr format="reference" name="counterTextAppearance"/>
    <!-- Text color of the character counter.
         If set, this takes precedence over counterTextAppearance. -->
    <attr format="reference" name="counterTextColor"/>
    <!-- TextAppearance of the character counter when the text is longer than the max. -->
    <attr format="reference" name="counterOverflowTextAppearance"/>
    <!-- Text color of the character counter when the text is longer than the max.
         If set, this takes precedence over counterOverflowTextAppearance. -->
    <attr format="reference" name="counterOverflowTextColor"/>

    <!-- The text to display as placeholder text in the text input area. -->
    <attr format="string" name="placeholderText"/>
    <!-- TextAppearance of the placeholder text displayed in the text input area. -->
    <attr format="reference" name="placeholderTextAppearance"/>
    <!-- Text color of the placeholder text displayed in the text input area.
         If set, this takes precedence over placeholderTextAppearance. -->
    <attr format="color" name="placeholderTextColor"/>

    <!-- The text to display as prefix text in the text input area. -->
    <attr format="string" name="prefixText"/>
    <!-- TextAppearance of the prefix text displayed in the text input area. -->
    <attr format="reference" name="prefixTextAppearance"/>
    <!-- Text color of the prefix text displayed in the text input area.
         If set, this takes precedence over prefixTextAppearance. -->
    <attr format="color" name="prefixTextColor"/>
    <!-- The text to display as suffix text in the text input area. -->
    <attr format="string" name="suffixText"/>
    <!-- TextAppearance of the suffix text displayed in the text input area. -->
    <attr format="reference" name="suffixTextAppearance"/>
    <!-- Text color of the suffix text displayed in the text input area.
         If set, this takes precedence over suffixTextAppearance. -->
    <attr format="color" name="suffixTextColor"/>

    <!-- Drawable to use for the start icon. -->
    <attr format="reference" name="startIconDrawable"/>
    <!-- Text to set as the content description for the start icon. -->
    <attr format="string" name="startIconContentDescription"/>
    <!-- Whether the start icon is checkable. -->
    <attr format="boolean" name="startIconCheckable"/>
    <!-- Tint color to use for the start icon. -->
    <attr format="color" name="startIconTint"/>
    <!-- Blending mode used to apply the background tint. -->
    <attr name="startIconTintMode">
      <!-- The tint is drawn on top of the drawable.
           [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
      <enum name="src_over" value="3"/>
      <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
           color channels are thrown out. [Sa * Da, Sc * Da] -->
      <enum name="src_in" value="5"/>
      <!-- The tint is drawn above the drawable, but with the drawable’s alpha
           channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
      <enum name="src_atop" value="9"/>
      <!-- Multiplies the color and alpha channels of the drawable with those of
           the tint. [Sa * Da, Sc * Dc] -->
      <enum name="multiply" value="14"/>
      <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
      <enum name="screen" value="15"/>
    </attr>
    <!-- The end icon mode of the TextInputLayout. It will display one of the end icons detailed
         below, or no end icon. -->
    <attr name="endIconMode">
      <!-- The view will display a custom icon specified by the user. -->
      <enum name="custom" value="-1"/>
      <!-- No end icon. -->
      <enum name="none" value="0"/>
      <!-- The view will display a toggle when the EditText has a password. -->
      <enum name="password_toggle" value="1"/>
      <!-- The view will display a clear text button while the EditText contains input. -->
      <enum name="clear_text" value="2"/>
      <!-- The view will display a toggle that displays/hides a dropdown menu. -->
      <enum name="dropdown_menu" value="3"/>
    </attr>
    <!-- Drawable to use for the end icon. -->
    <attr format="reference" name="endIconDrawable"/>
    <!-- Text to set as the content description for the end icon. -->
    <attr format="string" name="endIconContentDescription"/>
    <!-- Whether the end icon is checkable. -->
    <attr format="boolean" name="endIconCheckable"/>
    <!-- Tint color to use for the end icon. -->
    <attr format="color" name="endIconTint"/>
    <!-- Blending mode used to apply the background tint. -->
    <attr name="endIconTintMode">
      <!-- The tint is drawn on top of the drawable.
           [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
      <enum name="src_over" value="3"/>
      <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
           color channels are thrown out. [Sa * Da, Sc * Da] -->
      <enum name="src_in" value="5"/>
      <!-- The tint is drawn above the drawable, but with the drawable’s alpha
           channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
      <enum name="src_atop" value="9"/>
      <!-- Multiplies the color and alpha channels of the drawable with those of
           the tint. [Sa * Da, Sc * Dc] -->
      <enum name="multiply" value="14"/>
      <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
      <enum name="screen" value="15"/>
    </attr>

    <!-- Whether the text input area should be drawn as a filled box, an outline box, or not as a box.-->
    <attr name="boxBackgroundMode">
      <!-- Specifies that there should be no box set on the text input area. -->
      <enum name="none" value="0"/>
      <!-- Filled box mode for the text input box. -->
      <enum name="filled" value="1"/>
      <!-- Outline box mode for the text input box. -->
      <enum name="outline" value="2"/>
    </attr>
    <!-- Value to use for the EditText's collapsed top padding in box mode. -->
    <attr format="dimension" name="boxCollapsedPaddingTop"/>
    <!-- The value to use for the box's top start corner radius when in box mode. -->
    <attr format="dimension" name="boxCornerRadiusTopStart"/>
    <!-- The value to use for the box's top end corner radius when in box mode. -->
    <attr format="dimension" name="boxCornerRadiusTopEnd"/>
    <!-- The value to use for the box's bottom start corner radius when in box mode. -->
    <attr format="dimension" name="boxCornerRadiusBottomStart"/>
    <!-- The value to use for the box's bottom end corner radius when in box mode. -->
    <attr format="dimension" name="boxCornerRadiusBottomEnd"/>
    <!-- The color to use for the box's stroke when in outline box mode. -->
    <attr format="color" name="boxStrokeColor"/>
    <!-- The color to use for the box's stroke in outline box mode when an error
         is being displayed. If not set, it defaults to errorTextColor if on
         error state, or to counterOverflowTextColor if on overflow state. -->
    <attr format="color" name="boxStrokeErrorColor"/>
    <!-- The color to use for the box's background color when in filled box mode.
         If a non-stateful color resource is specified, default colors will be used for the hover
         and disabled states.  -->
    <attr format="color" name="boxBackgroundColor"/>
    <!-- The value to use for the box's stroke when in outline box mode, or for the underline stroke
         in filled mode. -->
    <attr format="dimension" name="boxStrokeWidth"/>
    <!-- The value to use for the focused box's stroke when in outline box mode, or for the focused
         underline stroke in filled mode.. -->
    <attr format="dimension" name="boxStrokeWidthFocused"/>

    <!-- Shape appearance style reference for TextInputLayout. Attribute declaration is in the Shape
        package. -->
    <attr name="shapeAppearance"/>
    <!-- Shape appearance overlay style reference for TextInputLayout. To be used to augment
         attributes declared in the shapeAppearance. Attribute declaration is in the Shape
         package. -->
    <attr name="shapeAppearanceOverlay"/>

    <!-- Whether the view will display a toggle when the EditText has a password.
         Deprecated. The view's end icon should be specified via endIconMode instead. -->
    <attr format="boolean" name="passwordToggleEnabled"/>
    <!-- Drawable to use as the password input visibility toggle icon.
         Deprecated. Use endIconDrawable instead. -->
    <attr format="reference" name="passwordToggleDrawable"/>
    <!-- Text to set as the content description for the password input visibility toggle.
         Deprecated. Use endIconContentDescription instead. -->
    <attr format="string" name="passwordToggleContentDescription"/>
    <!-- Icon to use for the password input visibility toggle
         Deprecated. Use endIconTint instead. -->
    <attr format="color" name="passwordToggleTint"/>
    <!-- Blending mode used to apply the background tint.
         Deprecated. Use endIconTintMode instead. -->
    <attr name="passwordToggleTintMode">
      <!-- The tint is drawn on top of the drawable.
           [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
      <enum name="src_over" value="3"/>
      <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
           color channels are thrown out. [Sa * Da, Sc * Da] -->
      <enum name="src_in" value="5"/>
      <!-- The tint is drawn above the drawable, but with the drawable’s alpha
           channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
      <enum name="src_atop" value="9"/>
      <!-- Multiplies the color and alpha channels of the drawable with those of
           the tint. [Sa * Da, Sc * Dc] -->
      <enum name="multiply" value="14"/>
      <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
      <enum name="screen" value="15"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="ThemeEnforcement">
    <!-- Internal flag used to denote that a style uses new attributes defined by
         Theme.MaterialComponents, and that the component should check via ThemeEnforcement that the
         client's app theme inherits from Theme.MaterialComponents.

         Not all usages of new attributes are problematic in the context of a legacy app theme. You
         should only use this flag if a particular usage is known to cause a visual glitch or crash.
         For example, tinting a vector drawable with a non-existent theme attribute is known to
         crash on pre-21 devices. -->
    <attr format="boolean" name="enforceMaterialTheme"/>
    <!-- Internal flag used to denote that a style requires that the textAppearance attribute is
         specified and evaluates to a valid text appearance. -->
    <attr format="boolean" name="enforceTextAppearance"/>
    <!-- Attribute used to check that a component has a TextAppearance specified on it. -->
    <attr name="android:textAppearance"/>
  </declare-styleable>
    <declare-styleable name="Tooltip">
    <attr name="android:text"/>
    <attr name="android:textAppearance"/>
    <attr name="android:textColor"/>
    <attr name="android:layout_margin"/>
    <attr name="android:minWidth"/>
    <attr name="android:minHeight"/>
    <attr name="android:padding"/>
    <attr format="color" name="backgroundTint"/>
  </declare-styleable>
</resources>