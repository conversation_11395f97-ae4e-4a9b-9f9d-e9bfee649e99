<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:id="@+id/material_timepicker_container"
  android:layout_width="wrap_content"
  android:layout_height="wrap_content">

  <androidx.constraintlayout.widget.Guideline
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/guideline"
    app:layout_constraintGuide_begin="16dp"
    android:orientation="horizontal"/>

  <include
    android:id="@+id/material_clock_display"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:layout_constraintBottom_toTopOf="@+id/material_clock_period_toggle"
    android:layout_marginTop="4dp"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintVertical_chainStyle="packed"
    layout="@layout/material_clock_display"
    app:layout_constraintTop_toTopOf="@+id/guideline" />

  <include
    android:id="@+id/material_clock_period_toggle"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    app:layout_constraintVertical_chainStyle="packed"
    app:layout_constraintTop_toBottomOf="@+id/material_clock_display"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintLeft_toLeftOf="@+id/material_clock_display"
    app:layout_constraintRight_toRightOf="@+id/material_clock_display"
    layout="@layout/material_clock_period_toggle_land" />

  <com.google.android.material.timepicker.ClockFaceView
    android:id="@+id/material_clock_face"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/clock_face_margin_start"
    app:layout_constraintStart_toEndOf="@+id/material_clock_display"
    app:layout_constraintTop_toTopOf="parent" />

</merge>
