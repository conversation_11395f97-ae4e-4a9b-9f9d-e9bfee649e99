<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="16dp"
    android:paddingBottom="16dp"
    android:paddingLeft="@dimen/mtrl_calendar_content_padding"
    android:paddingRight="@dimen/mtrl_calendar_content_padding"
    android:orientation="horizontal">

  <com.google.android.material.textfield.TextInputLayout
      android:id="@+id/mtrl_picker_text_input_range_start"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_weight="1">

    <com.google.android.material.textfield.TextInputEditText
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/mtrl_picker_text_input_date_range_start_hint"
        android:inputType="date"
        android:imeOptions="flagNoExtractUi"/>
  </com.google.android.material.textfield.TextInputLayout>

  <Space
      android:layout_width="8dp"
      android:layout_height="0dp"/>

  <com.google.android.material.textfield.TextInputLayout
      android:id="@+id/mtrl_picker_text_input_range_end"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_weight="1">

    <com.google.android.material.textfield.TextInputEditText
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/mtrl_picker_text_input_date_range_end_hint"
        android:inputType="date"
        android:imeOptions="flagNoExtractUi"/>
  </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>
