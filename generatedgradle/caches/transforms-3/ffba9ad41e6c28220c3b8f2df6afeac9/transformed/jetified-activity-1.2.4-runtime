androidx.activity.Cancellable
androidx.activity.ComponentActivity
androidx.activity.ComponentActivity$1
androidx.activity.ComponentActivity$2
androidx.activity.ComponentActivity$2$1
androidx.activity.ComponentActivity$2$2
androidx.activity.ComponentActivity$3
androidx.activity.ComponentActivity$4
androidx.activity.ComponentActivity$5
androidx.activity.ComponentActivity$6
androidx.activity.ComponentActivity$7
androidx.activity.ComponentActivity$NonConfigurationInstances
androidx.activity.ImmLeaksCleaner
androidx.activity.OnBackPressedCallback
androidx.activity.OnBackPressedDispatcher
androidx.activity.OnBackPressedDispatcher$LifecycleOnBackPressedCancellable
androidx.activity.OnBackPressedDispatcher$OnBackPressedCancellable
androidx.activity.OnBackPressedDispatcherOwner
androidx.activity.contextaware.ContextAware
androidx.activity.contextaware.ContextAwareHelper
androidx.activity.contextaware.OnContextAvailableListener
androidx.activity.result.ActivityResult
androidx.activity.result.ActivityResult$1
androidx.activity.result.ActivityResultCallback
androidx.activity.result.ActivityResultCaller
androidx.activity.result.ActivityResultLauncher
androidx.activity.result.ActivityResultRegistry
androidx.activity.result.ActivityResultRegistry$1
androidx.activity.result.ActivityResultRegistry$2
androidx.activity.result.ActivityResultRegistry$3
androidx.activity.result.ActivityResultRegistry$CallbackAndContract
androidx.activity.result.ActivityResultRegistry$LifecycleContainer
androidx.activity.result.ActivityResultRegistryOwner
androidx.activity.result.IntentSenderRequest
androidx.activity.result.IntentSenderRequest$1
androidx.activity.result.IntentSenderRequest$Builder
androidx.activity.result.contract.ActivityResultContract
androidx.activity.result.contract.ActivityResultContract$SynchronousResult
androidx.activity.result.contract.ActivityResultContracts
androidx.activity.result.contract.ActivityResultContracts$CreateDocument
androidx.activity.result.contract.ActivityResultContracts$GetContent
androidx.activity.result.contract.ActivityResultContracts$GetMultipleContents
androidx.activity.result.contract.ActivityResultContracts$OpenDocument
androidx.activity.result.contract.ActivityResultContracts$OpenDocumentTree
androidx.activity.result.contract.ActivityResultContracts$OpenMultipleDocuments
androidx.activity.result.contract.ActivityResultContracts$PickContact
androidx.activity.result.contract.ActivityResultContracts$RequestMultiplePermissions
androidx.activity.result.contract.ActivityResultContracts$RequestPermission
androidx.activity.result.contract.ActivityResultContracts$StartActivityForResult
androidx.activity.result.contract.ActivityResultContracts$StartIntentSenderForResult
androidx.activity.result.contract.ActivityResultContracts$TakePicture
androidx.activity.result.contract.ActivityResultContracts$TakePicturePreview
androidx.activity.result.contract.ActivityResultContracts$TakeVideo