2023-10-20T12:44:43.158+0530 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonMain] Assuming the daemon was started with following jvm opts: [--add-opens=java.base/java.util=ALL-UNNAMED, --add-opens=java.base/java.lang=ALL-UNNAMED, --add-opens=java.base/java.lang.invoke=ALL-UNNAMED, --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED, --add-opens=java.base/java.nio.charset=ALL-UNNAMED, --add-opens=java.base/java.net=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED, -Xmx1536m, -Dfile.encoding=windows-1252, -Duser.country=IN, -Duser.language=en, -Duser.variant]
2023-10-20T12:44:43.346+0530 [DEBUG] [org.gradle.internal.nativeintegration.services.NativeServices] Native-platform posix files integration is not available. Continuing with fallback.
2023-10-20T12:44:43.359+0530 [INFO] [org.gradle.launcher.daemon.server.Daemon] start() called on daemon - DefaultDaemonContext[uid=2d369a25-38c8-42d4-b42e-d89a20d69fed,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=15104,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:44:43.471+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Software Loopback Interface 1
2023-10-20T12:44:43.473+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? true
2023-10-20T12:44:43.473+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /127.0.0.1
2023-10-20T12:44:43.473+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /0:0:0:0:0:0:0:1
2023-10-20T12:44:43.474+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft 6to4 Adapter
2023-10-20T12:44:43.476+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.476+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (PPTP)
2023-10-20T12:44:43.478+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.479+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (L2TP)
2023-10-20T12:44:43.481+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.481+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft IP-HTTPS Platform Adapter
2023-10-20T12:44:43.483+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.483+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)
2023-10-20T12:44:43.485+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.485+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IKEv2)
2023-10-20T12:44:43.487+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.487+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)
2023-10-20T12:44:43.489+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.490+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9
2023-10-20T12:44:43.492+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.492+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:6030:97b:20bb:55a8%eth2
2023-10-20T12:44:43.492+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface RAS Async Adapter
2023-10-20T12:44:43.494+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.494+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (SSTP)
2023-10-20T12:44:43.496+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.497+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (PPPOE)
2023-10-20T12:44:43.499+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.499+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller
2023-10-20T12:44:43.501+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.501+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:c2b9:2b1d:f56d:8cc9%eth3
2023-10-20T12:44:43.501+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Remote NDIS based Internet Sharing Device
2023-10-20T12:44:43.504+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.504+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4
2023-10-20T12:44:43.507+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.507+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:cf1f:8a04:2276:509c%wlan0
2023-10-20T12:44:43.507+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2
2023-10-20T12:44:43.509+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.510+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:2265:1f01:3f26:ef68%eth5
2023-10-20T12:44:43.510+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter
2023-10-20T12:44:43.512+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.512+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Bluetooth Device (Personal Area Network)
2023-10-20T12:44:43.514+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.514+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:53ff:cf82:f38b:6711%eth6
2023-10-20T12:44:43.514+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Teredo Tunneling Adapter
2023-10-20T12:44:43.516+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.516+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #2
2023-10-20T12:44:43.518+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.518+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz
2023-10-20T12:44:43.520+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.521+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /**************
2023-10-20T12:44:43.521+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:f84b:67bf:69b5:22ba%wlan3
2023-10-20T12:44:43.521+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Kernel Debug Network Adapter
2023-10-20T12:44:43.523+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.524+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)
2023-10-20T12:44:43.526+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.526+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3
2023-10-20T12:44:43.528+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.528+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:bc5:162f:7b13:dfc9%wlan4
2023-10-20T12:44:43.528+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface SAMSUNG Mobile USB Remote NDIS Network Device
2023-10-20T12:44:43.531+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.531+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.534+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.534+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.537+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.537+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-QoS Packet Scheduler-0000
2023-10-20T12:44:43.540+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.540+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.542+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.543+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.545+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.545+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-QoS Packet Scheduler-0000
2023-10-20T12:44:43.547+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.547+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.549+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.549+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-QoS Packet Scheduler-0000
2023-10-20T12:44:43.551+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.551+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.554+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.554+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.556+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.556+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-Virtual WiFi Filter Driver-0000
2023-10-20T12:44:43.558+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.558+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-Native WiFi Filter Driver-0000
2023-10-20T12:44:43.560+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.560+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-QoS Packet Scheduler-0000
2023-10-20T12:44:43.562+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.562+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.564+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.564+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.566+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.566+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.568+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.568+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-Native WiFi Filter Driver-0000
2023-10-20T12:44:43.570+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.570+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-QoS Packet Scheduler-0000
2023-10-20T12:44:43.572+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.572+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.574+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.574+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)-QoS Packet Scheduler-0000
2023-10-20T12:44:43.576+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.576+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.578+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.578+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)-QoS Packet Scheduler-0000
2023-10-20T12:44:43.581+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.581+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.583+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.583+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)-QoS Packet Scheduler-0000
2023-10-20T12:44:43.585+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.585+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.586+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.587+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-Native WiFi Filter Driver-0000
2023-10-20T12:44:43.589+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.589+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-QoS Packet Scheduler-0000
2023-10-20T12:44:43.592+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.592+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:44:43.595+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:44:43.603+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Listening on [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]].
2023-10-20T12:44:43.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] Daemon starting at: Fri Oct 20 12:44:43 IST 2023, with address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:44:43.614+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertising the daemon address to the clients: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:44:43.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertised daemon context: DefaultDaemonContext[uid=2d369a25-38c8-42d4-b42e-d89a20d69fed,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=15104,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:44:43.615+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]], context: DefaultDaemonContext[uid=2d369a25-38c8-42d4-b42e-d89a20d69fed,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=15104,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:44:43.627+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:44:43.631+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:44:43.636+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:44:43.637+0530 [LIFECYCLE] [org.gradle.launcher.daemon.server.Daemon] Daemon server started.
2023-10-20T12:44:43.639+0530 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonStartupCommunication] Completed writing the daemon greeting. Closing streams...
2023-10-20T12:44:43.646+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stopOnExpiration() called on daemon
2023-10-20T12:44:43.647+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] awaitExpiration() called on daemon
2023-10-20T12:44:43.647+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:44:43.660+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:58997 to /127.0.0.1:58996.
2023-10-20T12:44:43.738+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:44:43.739+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received non-IO message from client: Build{id=b8ea1b45-6116-471b-aefd-75249d0c5327, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:44:43.739+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=b8ea1b45-6116-471b-aefd-75249d0c5327, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:44:43.739+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=b8ea1b45-6116-471b-aefd-75249d0c5327, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:58996 to /127.0.0.1:58997.
2023-10-20T12:44:43.742+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=b8ea1b45-6116-471b-aefd-75249d0c5327, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:58997] after 0.0 minutes of idle
2023-10-20T12:44:43.742+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:44:43.742+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:44:43.743+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:44:43.743+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:44:43.744+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:44:43.744+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:44:43.745+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:44:43.745+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=b8ea1b45-6116-471b-aefd-75249d0c5327, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:44:43.746+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@3cdc84df
2023-10-20T12:44:43.747+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:44:43.749+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:44:43.750+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@7ac9b9cc
2023-10-20T12:44:43.751+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:44:43.752+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 15104). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-15104.out.log
2023-10-20T12:44:43.754+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting build in new daemon [memory: 1.5 GiB]
2023-10-20T12:44:43.755+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-20T12:44:43.755+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-20T12:44:43.757+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:44:43.758+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=2d369a25-38c8-42d4-b42e-d89a20d69fed,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=15104,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 22s
2023-10-20T12:45:02.021+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:45:02.033+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:45:02.034+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=b8ea1b45-6116-471b-aefd-75249d0c5327, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:58997]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:45:02.034+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=b8ea1b45-6116-471b-aefd-75249d0c5327, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:58997]
2023-10-20T12:45:02.034+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:45:02.035+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:02.035+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:02.036+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:45:02.036+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:02.038+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:02.039+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:45:02.039+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@3f37e55]
2023-10-20T12:45:02.040+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@3f37e55]
2023-10-20T12:45:02.042+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:45:02.042+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@12725524
2023-10-20T12:45:02.044+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:45:02.044+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received null
2023-10-20T12:45:02.044+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received end-of-input from client.
2023-10-20T12:45:02.047+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=b8ea1b45-6116-471b-aefd-75249d0c5327, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:45:02.048+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: null
2023-10-20T12:45:02.049+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:45:02.051+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:45:03.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:45:03.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:03.661+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:03.662+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:45:03.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:03.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:10.813+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59008 to /127.0.0.1:58996.
2023-10-20T12:45:10.816+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:45:10.817+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received non-IO message from client: Build{id=c75af46a-d40f-4557-bd47-f6c43f807d46, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:45:10.817+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=c75af46a-d40f-4557-bd47-f6c43f807d46, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:45:10.817+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=c75af46a-d40f-4557-bd47-f6c43f807d46, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59008.
2023-10-20T12:45:10.817+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=c75af46a-d40f-4557-bd47-f6c43f807d46, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59008] after 0.0 minutes of idle
2023-10-20T12:45:10.817+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:10.817+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:10.818+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:45:10.818+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:10.818+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:10.819+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:45:10.819+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:45:10.819+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=c75af46a-d40f-4557-bd47-f6c43f807d46, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:45:10.819+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@604cdcbb
2023-10-20T12:45:10.820+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:45:10.821+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:45:10.822+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@3e13eb80
2023-10-20T12:45:10.823+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:45:10.824+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 15104). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-15104.out.log
2023-10-20T12:45:10.824+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 2nd build in daemon [uptime: 27.595 secs, performance: 99%]
2023-10-20T12:45:10.825+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:45:10.825+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=2d369a25-38c8-42d4-b42e-d89a20d69fed,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=15104,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Closing daemon's stdin at end of input.
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 1s
2 actionable tasks: 1 executed, 1 up-to-date
2023-10-20T12:45:12.215+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:45:12.233+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=c75af46a-d40f-4557-bd47-f6c43f807d46, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59008]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:45:12.233+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=c75af46a-d40f-4557-bd47-f6c43f807d46, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59008]
2023-10-20T12:45:12.233+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:45:12.234+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:12.234+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:12.244+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:45:12.245+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:12.261+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:12.262+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@4f304c94]
2023-10-20T12:45:12.262+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@4f304c94]
2023-10-20T12:45:12.262+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:45:12.262+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=c75af46a-d40f-4557-bd47-f6c43f807d46, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:45:12.262+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:45:12.263+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@30d41232
2023-10-20T12:45:12.264+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:45:12.264+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received null
2023-10-20T12:45:12.264+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received end-of-input from client.
2023-10-20T12:45:12.264+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@30d41232
2023-10-20T12:45:12.266+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:45:12.266+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:45:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:45:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:13.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:13.664+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:45:13.665+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:13.669+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:17.340+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59011 to /127.0.0.1:58996.
2023-10-20T12:45:17.343+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 241: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:45:17.344+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 241: Received non-IO message from client: Build{id=1272fe16-0a8c-4e5e-a765-1e0a2baef265, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:45:17.344+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=1272fe16-0a8c-4e5e-a765-1e0a2baef265, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:45:17.344+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=1272fe16-0a8c-4e5e-a765-1e0a2baef265, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59011.
2023-10-20T12:45:17.344+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=1272fe16-0a8c-4e5e-a765-1e0a2baef265, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59011] after 0.0 minutes of idle
2023-10-20T12:45:17.344+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:17.344+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:17.345+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:45:17.345+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:17.346+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:17.346+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:45:17.346+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:45:17.346+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=1272fe16-0a8c-4e5e-a765-1e0a2baef265, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:45:17.347+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@38bd52d
2023-10-20T12:45:17.347+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:45:17.348+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 241: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:45:17.348+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 241: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@29adc8bc
2023-10-20T12:45:17.348+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:45:17.348+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 15104). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-15104.out.log
2023-10-20T12:45:17.348+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 3rd build in daemon [uptime: 34.12 secs, performance: 99%]
2023-10-20T12:45:17.348+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:45:17.349+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=2d369a25-38c8-42d4-b42e-d89a20d69fed,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=15104,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:45:17.349+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-20T12:45:17.349+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 472ms
2 actionable tasks: 2 up-to-date
2023-10-20T12:45:17.828+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:45:17.846+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=1272fe16-0a8c-4e5e-a765-1e0a2baef265, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59011]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:45:17.847+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=1272fe16-0a8c-4e5e-a765-1e0a2baef265, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59011]
2023-10-20T12:45:17.847+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:45:17.847+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:17.847+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:17.857+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:45:17.858+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:17.867+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:17.868+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@204b6b7f]
2023-10-20T12:45:17.868+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@204b6b7f]
2023-10-20T12:45:17.868+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:45:17.869+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=1272fe16-0a8c-4e5e-a765-1e0a2baef265, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:45:17.869+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 241: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:45:17.869+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 241: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@276a23f9
2023-10-20T12:45:17.869+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@276a23f9
2023-10-20T12:45:17.870+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:45:17.869+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:45:17.870+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 241: received null
2023-10-20T12:45:17.870+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 241: Received end-of-input from client.
2023-10-20T12:45:17.871+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:45:20.722+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59012 to /127.0.0.1:58996.
2023-10-20T12:45:20.724+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 342: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:45:20.724+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 342: Received non-IO message from client: Build{id=7c9493e2-4f93-4d28-b2ad-bc1dcaa22789, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:45:20.724+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=7c9493e2-4f93-4d28-b2ad-bc1dcaa22789, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:45:20.724+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=7c9493e2-4f93-4d28-b2ad-bc1dcaa22789, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59012.
2023-10-20T12:45:20.725+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=7c9493e2-4f93-4d28-b2ad-bc1dcaa22789, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59012] after 0.0 minutes of idle
2023-10-20T12:45:20.725+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:20.725+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:45:20.725+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:45:20.726+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:45:20.727+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:45:20.727+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:45:20.727+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:45:20.727+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=7c9493e2-4f93-4d28-b2ad-bc1dcaa22789, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:45:20.728+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@6daad772
2023-10-20T12:45:20.728+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:45:20.729+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:45:20.729+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 342: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:45:20.730+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 342: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@7dc17132
2023-10-20T12:45:20.730+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 15104). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-15104.out.log
2023-10-20T12:45:20.730+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 4th build in daemon [uptime: 37.505 secs, performance: 99%]
2023-10-20T12:45:20.730+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:45:20.730+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-20T12:45:20.731+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-20T12:45:20.730+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=2d369a25-38c8-42d4-b42e-d89a20d69fed,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=15104,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
package="com.schnell.xsmanager" found in source AndroidManifest.xml: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml.
Setting the namespace via a source AndroidManifest.xml's package attribute is deprecated.
Please instead set the namespace (or testNamespace) in the module's build.gradle file, as described here: https://developer.android.com/studio/build/configure-app-module#set-namespace
This migration can be done automatically using the AGP Upgrade Assistant, please refer to https://developer.android.com/studio/build/agp-upgrade-assistant for more information.
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:allowBackup was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:usesCleartextTraffic was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
Unable to make progress running work. The following items are queued for execution but none of them can be started:
  - Build ':':
      - Waiting for nodes:
          - producer locations for task group 0 (state=SHOULD_RUN, dependencies=NOT_COMPLETE, group=task group 0, no dependencies )
      - Ordinal groups:
          - group 0 entry nodes: [:xserp:clean (complete)]
          - group 1 entry nodes: [:xserp:assembleDevDebug (complete)]
  - Workers waiting for work: 8
  - Stopped workers: 0

FAILURE: Build completed with 3 failures.

1: Task failed with an exception.
-----------
* What went wrong:
Execution failed for task ':xserp:mapDevDebugSourceSetPaths'.
> Error while evaluating property 'extraGeneratedResDir' of task ':xserp:mapDevDebugSourceSetPaths'.
   > Failed to calculate the value of task ':xserp:mapDevDebugSourceSetPaths' property 'extraGeneratedResDir'.
      > Querying the mapped value of provider(java.util.Set) before task ':xserp:processDevDebugGoogleServices' has completed is not supported

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

2: Task failed with an exception.
-----------
* What went wrong:
Execution failed for task ':xserp:mergeDevDebugResources'.
> A failure occurred while executing com.android.build.gradle.internal.res.ResourceCompilerRunnable
   > Resource compilation failed (Failed to compile resource file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\res\layout\sign_up_screen.xml: . Cause: javax.xml.stream.XMLStreamException: ParseError at [row,col]:[2,6]
     Message: The processing instruction target matching "[xX][mM][lL]" is not allowed.). Check logs for more details.

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

3: Task failed with an exception.
-----------
* What went wrong:
Unable to make progress running work. There are items queued for execution but none of them can be started

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

* Get more help at https://help.gradle.org

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD FAILED in 1m 33s
25 actionable tasks: 24 executed, 1 up-to-date
2023-10-20T12:46:54.388+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:46:54.389+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:46:54.390+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=7c9493e2-4f93-4d28-b2ad-bc1dcaa22789, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59012]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:46:54.390+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=7c9493e2-4f93-4d28-b2ad-bc1dcaa22789, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59012]
2023-10-20T12:46:54.390+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:46:54.390+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:46:54.390+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:46:54.390+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:46:54.391+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:46:54.392+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:46:54.393+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:46:54.393+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@67da256e]
2023-10-20T12:46:54.393+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@67da256e]
2023-10-20T12:46:54.395+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=7c9493e2-4f93-4d28-b2ad-bc1dcaa22789, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:46:54.402+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 342: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:46:54.402+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 342: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@730680fa
2023-10-20T12:46:54.402+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:46:54.403+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 342: received null
2023-10-20T12:46:54.403+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 342: Received end-of-input from client.
2023-10-20T12:46:54.404+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: null
2023-10-20T12:46:54.405+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:46:54.405+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:47:03.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:03.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:03.659+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:03.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:03.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:03.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:13.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:13.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:13.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:13.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:13.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:23.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:23.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:23.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:23.664+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:23.664+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:23.664+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:23.664+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:23.664+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:23.664+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:33.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:33.651+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:33.651+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:33.652+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:33.652+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:33.652+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:33.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:33.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:33.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:36.939+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change CREATED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\res\layout\sign_up_screen.xml~
2023-10-20T12:47:36.943+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\res\layout\sign_up_screen.xml
2023-10-20T12:47:43.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:43.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:43.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:43.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:43.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:43.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:43.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:43.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:43.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:53.658+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:53.658+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:53.658+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:53.659+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:53.659+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:53.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:47:53.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:47:53.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:47:53.661+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:03.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:03.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:03.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:03.658+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:03.658+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:03.659+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:03.659+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:03.659+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:03.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:13.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:13.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:13.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:13.658+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:13.658+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:13.658+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:23.652+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:23.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:23.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:23.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:23.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:23.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:23.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:23.655+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:23.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:33.647+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:33.647+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:33.648+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:33.649+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:33.649+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:33.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:33.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:33.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:33.651+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:43.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:43.651+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:43.651+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:43.652+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:43.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:43.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:43.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:43.653+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:43.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:53.648+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:53.648+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:53.648+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:53.649+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:53.649+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:53.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:48:53.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:48:53.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:48:53.651+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:03.648+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:49:03.648+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:03.649+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:03.649+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:49:03.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:03.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:03.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:49:03.650+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:03.651+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:13.656+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:49:13.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:13.657+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:13.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:49:13.660+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:13.661+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:13.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:49:13.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:13.663+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:23.021+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59088 to /127.0.0.1:58996.
2023-10-20T12:49:23.025+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 471: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:49:23.025+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 471: Received non-IO message from client: Build{id=2a84d565-ccbb-44c9-b3d9-52bc097084a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:49:23.025+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=2a84d565-ccbb-44c9-b3d9-52bc097084a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:49:23.025+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=2a84d565-ccbb-44c9-b3d9-52bc097084a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59088.
2023-10-20T12:49:23.026+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=2a84d565-ccbb-44c9-b3d9-52bc097084a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59088] after 2.0 minutes of idle
2023-10-20T12:49:23.026+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:49:23.026+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:49:23.027+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:49:23.027+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:23.028+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:23.029+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:49:23.029+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:49:23.029+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=2a84d565-ccbb-44c9-b3d9-52bc097084a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:49:23.029+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 473: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@617a705c
2023-10-20T12:49:23.030+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:49:23.030+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 471: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:49:23.030+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 471: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@50ff1204
2023-10-20T12:49:23.031+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:49:23.032+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 15104). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-15104.out.log
2023-10-20T12:49:23.033+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 5th build in daemon [uptime: 4 mins 39.812 secs, performance: 98%]
2023-10-20T12:49:23.033+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:49:23.033+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=2d369a25-38c8-42d4-b42e-d89a20d69fed,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=15104,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 1s
2 actionable tasks: 1 executed, 1 up-to-date
2023-10-20T12:49:24.397+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:49:24.414+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:49:24.414+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=2a84d565-ccbb-44c9-b3d9-52bc097084a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59088]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:49:24.414+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=2a84d565-ccbb-44c9-b3d9-52bc097084a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59088]
2023-10-20T12:49:24.414+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:49:24.415+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:49:24.415+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:49:24.415+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:49:24.416+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:24.416+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:24.417+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:49:24.417+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@7b616406]
2023-10-20T12:49:24.417+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 470: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@7b616406]
2023-10-20T12:49:24.418+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 471: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:49:24.418+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 471: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@5e38f66b
2023-10-20T12:49:24.419+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:49:24.419+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 471: received null
2023-10-20T12:49:24.419+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 471: Received end-of-input from client.
2023-10-20T12:49:24.419+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=2a84d565-ccbb-44c9-b3d9-52bc097084a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:49:24.419+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: null
2023-10-20T12:49:24.419+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 470: stopping connection
2023-10-20T12:49:24.420+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 470: stopping connection
2023-10-20T12:49:28.595+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59093 to /127.0.0.1:58996.
2023-10-20T12:49:28.598+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59092 to /127.0.0.1:58996.
2023-10-20T12:49:28.608+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 573: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2023-10-20T12:49:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 573: Received non-IO message from client: StopWhenIdle[id=9732c8bf-30d8-44de-9d74-5a71af5a1783]
2023-10-20T12:49:28.609+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=9732c8bf-30d8-44de-9d74-5a71af5a1783].
2023-10-20T12:49:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=9732c8bf-30d8-44de-9d74-5a71af5a1783] with connection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59093.
2023-10-20T12:49:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2023-10-20T12:49:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1697786368608
2023-10-20T12:49:28.610+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:49:28.611+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:28.611+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:28.612+0530 [LIFECYCLE] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Daemon will be stopped at the end of the build stop command received
2023-10-20T12:49:28.612+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Marking daemon stopped due to stop command received. The daemon is running a build: false
2023-10-20T12:49:28.612+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 470: dispatching Success[value=null]
2023-10-20T12:49:28.613+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=9732c8bf-30d8-44de-9d74-5a71af5a1783]
2023-10-20T12:49:28.613+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon has stopped.
2023-10-20T12:49:28.613+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stop() called on daemon
2023-10-20T12:49:28.613+0530 [INFO] [org.gradle.launcher.daemon.server.Daemon] Stop requested. Daemon is removing its presence from the registry...
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Removing our presence to clients, eg. removing this address from the registry: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 573: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 573: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@3edd1d1d
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 575: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 575: Received non-IO message from client: StopWhenIdle[id=cf43f89f-ded6-4fa7-a7f7-6b30c42cd542]
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 573: received null
2023-10-20T12:49:28.614+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 573: Received end-of-input from client.
2023-10-20T12:49:28.615+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=cf43f89f-ded6-4fa7-a7f7-6b30c42cd542].
2023-10-20T12:49:28.615+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=cf43f89f-ded6-4fa7-a7f7-6b30c42cd542] with connection: socket connection from /127.0.0.1:58996 to /127.0.0.1:59092.
2023-10-20T12:49:28.615+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2023-10-20T12:49:28.616+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: null
2023-10-20T12:49:28.616+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 470: stopping connection
2023-10-20T12:49:28.616+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 470: stopping connection
2023-10-20T12:49:28.623+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:49:28.624+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:28.638+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:28.639+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Address removed from registry.
2023-10-20T12:49:28.640+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1697786368615
2023-10-20T12:49:28.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:49:28.654+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:28.662+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:49:28.663+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 574: dispatching Success[value=null]
2023-10-20T12:49:28.663+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=cf43f89f-ded6-4fa7-a7f7-6b30c42cd542]
2023-10-20T12:49:28.663+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 575: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:49:28.663+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 575: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@146a8b91
2023-10-20T12:49:28.664+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@146a8b91
2023-10-20T12:49:28.664+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 574: stopping connection
2023-10-20T12:49:28.664+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:49:28.664+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 575: received null
2023-10-20T12:49:28.664+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 575: Received end-of-input from client.
2023-10-20T12:49:28.664+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 574: stopping connection
2023-10-20T12:49:28.665+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9)
2023-10-20T12:49:28.665+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9).
2023-10-20T12:49:28.665+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) has last been fully cleaned up 0 hours ago
2023-10-20T12:49:28.666+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3)
2023-10-20T12:49:28.666+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3).
2023-10-20T12:49:28.666+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3) has last been fully cleaned up 0 hours ago
2023-10-20T12:49:28.687+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent)
2023-10-20T12:49:28.687+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent).
2023-10-20T12:49:28.687+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Closing VFS, dropping state
2023-10-20T12:49:28.695+0530 [DEBUG] [org.gradle.internal.watch.registry.impl.DefaultFileWatcherRegistry] Finished listening to file system change events
2023-10-20T12:49:28.696+0530 [DEBUG] [org.gradle.cache.internal.DefaultCacheAccess] Cache Generated Gradle JARs cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\generated-gradle-jars) was closed 0 times.
2023-10-20T12:49:28.696+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2)
2023-10-20T12:49:28.696+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-metadata.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-metadata.bin)
2023-10-20T12:49:28.697+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifacts.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-artifacts.bin)
2023-10-20T12:49:28.697+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache resource-at-url.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\resource-at-url.bin)
2023-10-20T12:49:28.697+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifact.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-artifact.bin)
2023-10-20T12:49:28.697+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2).
2023-10-20T12:49:28.698+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) has last been fully cleaned up 0 hours ago
2023-10-20T12:49:28.703+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1)
2023-10-20T12:49:28.704+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache file-access.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1\file-access.bin)
2023-10-20T12:49:28.704+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1).
2023-10-20T12:49:28.708+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes)
2023-10-20T12:49:28.709+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache fileHashes.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes\fileHashes.bin)
2023-10-20T12:49:28.709+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache resourceHashesCache.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes\resourceHashesCache.bin)
2023-10-20T12:49:28.709+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes).
2023-10-20T12:49:28.711+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory)
2023-10-20T12:49:28.711+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache executionHistory.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory\executionHistory.bin)
2023-10-20T12:49:28.712+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory).
Daemon vm is shutting down... The daemon has exited normally or was terminated in response to a user interrupt.
2023-10-20T12:49:28.718+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [ae99a316-166c-454f-9c3c-208e15523dea port:58996, addresses:[localhost/127.0.0.1]]
2023-10-20T12:49:28.731+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:49:28.732+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:49:28.742+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
