2023-10-21T14:27:45.465+0530 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonMain] Assuming the daemon was started with following jvm opts: [--add-opens=java.base/java.util=ALL-UNNAMED, --add-opens=java.base/java.lang=ALL-UNNAMED, --add-opens=java.base/java.lang.invoke=ALL-UNNAMED, --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED, --add-opens=java.base/java.nio.charset=ALL-UNNAMED, --add-opens=java.base/java.net=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED, -Xmx4608m, -Dfile.encoding=windows-1252, -Duser.country=IN, -Duser.language=en, -Duser.variant]
2023-10-21T14:27:45.678+0530 [DEBUG] [org.gradle.internal.nativeintegration.services.NativeServices] Native-platform posix files integration is not available. Continuing with fallback.
2023-10-21T14:27:45.699+0530 [INFO] [org.gradle.launcher.daemon.server.Daemon] start() called on daemon - DefaultDaemonContext[uid=2df07e7d-be88-4354-896d-2aab905a36bb,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=13604,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-21T14:27:45.814+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Software Loopback Interface 1
2023-10-21T14:27:45.817+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? true
2023-10-21T14:27:45.818+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /127.0.0.1
2023-10-21T14:27:45.818+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /0:0:0:0:0:0:0:1
2023-10-21T14:27:45.819+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft 6to4 Adapter
2023-10-21T14:27:45.822+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.822+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (PPTP)
2023-10-21T14:27:45.824+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.825+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (L2TP)
2023-10-21T14:27:45.828+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.828+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft IP-HTTPS Platform Adapter
2023-10-21T14:27:45.832+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.832+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)
2023-10-21T14:27:45.835+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.835+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IKEv2)
2023-10-21T14:27:45.838+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.838+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)
2023-10-21T14:27:45.840+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.841+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9
2023-10-21T14:27:45.843+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.843+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:6030:97b:20bb:55a8%eth2
2023-10-21T14:27:45.844+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface RAS Async Adapter
2023-10-21T14:27:45.846+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.846+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (SSTP)
2023-10-21T14:27:45.848+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.848+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (PPPOE)
2023-10-21T14:27:45.850+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.850+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller
2023-10-21T14:27:45.852+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.852+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:c2b9:2b1d:f56d:8cc9%eth3
2023-10-21T14:27:45.852+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Remote NDIS based Internet Sharing Device
2023-10-21T14:27:45.854+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.854+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4
2023-10-21T14:27:45.856+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.857+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:cf1f:8a04:2276:509c%wlan0
2023-10-21T14:27:45.857+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2
2023-10-21T14:27:45.860+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.860+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:2265:1f01:3f26:ef68%eth5
2023-10-21T14:27:45.860+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter
2023-10-21T14:27:45.863+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.863+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Bluetooth Device (Personal Area Network)
2023-10-21T14:27:45.865+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.865+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:53ff:cf82:f38b:6711%eth6
2023-10-21T14:27:45.866+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Teredo Tunneling Adapter
2023-10-21T14:27:45.868+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.868+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #2
2023-10-21T14:27:45.870+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.870+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz
2023-10-21T14:27:45.872+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.872+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /***********
2023-10-21T14:27:45.872+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /2401:4900:1ce3:135:0:0:0:1
2023-10-21T14:27:45.872+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:f84b:67bf:69b5:22ba%wlan3
2023-10-21T14:27:45.872+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Kernel Debug Network Adapter
2023-10-21T14:27:45.874+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.875+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)
2023-10-21T14:27:45.877+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.877+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3
2023-10-21T14:27:45.879+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.879+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:bc5:162f:7b13:dfc9%wlan4
2023-10-21T14:27:45.879+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface SAMSUNG Mobile USB Remote NDIS Network Device
2023-10-21T14:27:45.881+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.881+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.884+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.884+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-QoS Packet Scheduler-0000
2023-10-21T14:27:45.886+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.886+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.888+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.888+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.890+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.890+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-QoS Packet Scheduler-0000
2023-10-21T14:27:45.893+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.893+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.895+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.895+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.898+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.898+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-QoS Packet Scheduler-0000
2023-10-21T14:27:45.901+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.901+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.903+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.903+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.905+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.906+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-Virtual WiFi Filter Driver-0000
2023-10-21T14:27:45.908+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.908+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-Native WiFi Filter Driver-0000
2023-10-21T14:27:45.910+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.910+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-QoS Packet Scheduler-0000
2023-10-21T14:27:45.911+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.912+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.914+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.914+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.916+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.916+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-Native WiFi Filter Driver-0000
2023-10-21T14:27:45.918+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.918+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-QoS Packet Scheduler-0000
2023-10-21T14:27:45.920+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.920+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.922+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.922+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.924+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.924+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)-QoS Packet Scheduler-0000
2023-10-21T14:27:45.926+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.926+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.929+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.929+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)-QoS Packet Scheduler-0000
2023-10-21T14:27:45.931+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.931+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.933+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.933+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)-QoS Packet Scheduler-0000
2023-10-21T14:27:45.935+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.935+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.937+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.938+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-Native WiFi Filter Driver-0000
2023-10-21T14:27:45.940+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.941+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-QoS Packet Scheduler-0000
2023-10-21T14:27:45.943+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.944+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T14:27:45.946+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T14:27:45.953+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Listening on [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]].
2023-10-21T14:27:45.967+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] Daemon starting at: Sat Oct 21 14:27:45 IST 2023, with address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:27:45.967+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertising the daemon address to the clients: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:27:45.967+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertised daemon context: DefaultDaemonContext[uid=2df07e7d-be88-4354-896d-2aab905a36bb,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=13604,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-21T14:27:45.975+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]], context: DefaultDaemonContext[uid=2df07e7d-be88-4354-896d-2aab905a36bb,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=13604,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-21T14:27:46.031+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:27:46.039+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:27:46.047+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:27:46.048+0530 [LIFECYCLE] [org.gradle.launcher.daemon.server.Daemon] Daemon server started.
2023-10-21T14:27:46.051+0530 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonStartupCommunication] Completed writing the daemon greeting. Closing streams...
2023-10-21T14:27:46.067+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stopOnExpiration() called on daemon
2023-10-21T14:27:46.068+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] awaitExpiration() called on daemon
2023-10-21T14:27:46.069+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T14:27:46.078+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:49213 to /127.0.0.1:49212.
2023-10-21T14:27:46.189+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.Build
2023-10-21T14:27:46.190+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received non-IO message from client: Build{id=157e825a-ff48-4926-ae7f-b77117298525, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T14:27:46.190+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=157e825a-ff48-4926-ae7f-b77117298525, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-21T14:27:46.190+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=157e825a-ff48-4926-ae7f-b77117298525, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:49212 to /127.0.0.1:49213.
2023-10-21T14:27:46.194+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=157e825a-ff48-4926-ae7f-b77117298525, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:49212 to /127.0.0.1:49213] after 0.0 minutes of idle
2023-10-21T14:27:46.194+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:27:46.194+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:27:46.195+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:27:46.195+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:27:46.196+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:27:46.197+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T14:27:46.198+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T14:27:46.199+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=157e825a-ff48-4926-ae7f-b77117298525, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-21T14:27:46.199+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@5ccef947
2023-10-21T14:27:46.202+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, EFC_6244, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-21T14:27:46.203+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-21T14:27:46.203+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@45326da8
2023-10-21T14:27:46.209+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-21T14:27:46.209+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 13604). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-13604.out.log
2023-10-21T14:27:46.211+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting build in new daemon [memory: 4.5 GiB]
2023-10-21T14:27:46.212+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-21T14:27:46.213+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-21T14:27:46.218+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-21T14:27:46.218+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=2df07e7d-be88-4354-896d-2aab905a36bb,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=13604,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
Warning: This version only understands SDK XML versions up to 2 but an SDK XML file of version 3 was encountered. This can happen if you use versions of Android Studio and the command-line tools that were released at different times.
Warning: unexpected element (uri:"", local:"extension-level"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
Warning: unexpected element (uri:"", local:"base-extension"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 34s
2023-10-21T14:28:15.818+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-21T14:28:15.835+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T14:28:15.836+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=157e825a-ff48-4926-ae7f-b77117298525, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:49212 to /127.0.0.1:49213]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@2c3a5d83 with state Busy
2023-10-21T14:28:15.836+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=157e825a-ff48-4926-ae7f-b77117298525, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:49212 to /127.0.0.1:49213]
2023-10-21T14:28:15.836+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T14:28:15.836+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:28:15.837+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:28:15.837+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:28:15.838+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:15.839+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:15.840+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T14:28:15.840+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@1af62c18]
2023-10-21T14:28:15.840+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@1af62c18]
2023-10-21T14:28:15.841+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=157e825a-ff48-4926-ae7f-b77117298525, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T14:28:15.842+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T14:28:15.843+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@75b608ca
2023-10-21T14:28:15.843+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@75b608ca
2023-10-21T14:28:15.843+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-21T14:28:15.845+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T14:28:15.846+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received null
2023-10-21T14:28:15.846+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received end-of-input from client.
2023-10-21T14:28:15.846+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-21T14:28:16.100+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:28:16.100+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:16.107+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:16.109+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:28:16.109+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:16.109+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:26.085+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:28:26.085+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:26.085+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:26.086+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:28:26.086+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:26.087+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:26.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:28:26.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:26.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:36.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:28:36.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:36.098+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:36.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:28:36.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:36.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:36.100+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:28:36.100+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:36.100+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:36.614+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:49891 to /127.0.0.1:49212.
2023-10-21T14:28:36.622+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.Build
2023-10-21T14:28:36.622+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received non-IO message from client: Build{id=79f218b1-a499-43c3-9075-2fe70016a2a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T14:28:36.623+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=79f218b1-a499-43c3-9075-2fe70016a2a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-21T14:28:36.623+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=79f218b1-a499-43c3-9075-2fe70016a2a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:49212 to /127.0.0.1:49891.
2023-10-21T14:28:36.623+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=79f218b1-a499-43c3-9075-2fe70016a2a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:49212 to /127.0.0.1:49891] after 0.0 minutes of idle
2023-10-21T14:28:36.624+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:28:36.624+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:28:36.624+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:28:36.625+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:28:36.626+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:28:36.626+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T14:28:36.626+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T14:28:36.626+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=79f218b1-a499-43c3-9075-2fe70016a2a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-21T14:28:36.627+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@7338c352
2023-10-21T14:28:36.629+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, EFC_6244, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-21T14:28:36.629+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-21T14:28:36.630+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@51f410bd
2023-10-21T14:28:36.630+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-21T14:28:36.630+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 13604). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-13604.out.log
2023-10-21T14:28:36.631+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 2nd build in daemon [uptime: 51.065 secs, performance: 100%]
2023-10-21T14:28:36.632+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-21T14:28:36.632+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-21T14:28:36.632+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-21T14:28:36.632+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=2df07e7d-be88-4354-896d-2aab905a36bb,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=13604,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
Warning: unexpected element (uri:"", local:"extension-level"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
Warning: unexpected element (uri:"", local:"base-extension"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
AGPBI: {"kind":"warning","text":"We recommend using a newer Android Gradle plugin to use compileSdk = 33\n\nThis Android Gradle plugin (7.0.4) was tested up to compileSdk = 31\n\nThis warning can be suppressed by adding\n    android.suppressUnsupportedCompileSdk=33\nto this project's gradle.properties\n\nThe build will continue, but you are strongly encouraged to update your project to\nuse a newer Android Gradle Plugin that has been tested with compileSdk = 33","sources":[{}]}
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:allowBackup was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:usesCleartextTraffic was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 1m 15s
38 actionable tasks: 18 executed, 20 up-to-date
2023-10-21T14:29:52.680+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-21T14:29:52.698+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=79f218b1-a499-43c3-9075-2fe70016a2a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:49212 to /127.0.0.1:49891]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@2c3a5d83 with state Busy
2023-10-21T14:29:52.698+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=79f218b1-a499-43c3-9075-2fe70016a2a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:49212 to /127.0.0.1:49891]
2023-10-21T14:29:52.698+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T14:29:52.698+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:29:52.698+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:29:52.699+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:29:52.699+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:29:52.700+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:29:52.701+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T14:29:52.701+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@d38e90d]
2023-10-21T14:29:52.701+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@d38e90d]
2023-10-21T14:29:52.702+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=79f218b1-a499-43c3-9075-2fe70016a2a3, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T14:29:52.702+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T14:29:52.702+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@5f33f4f3
2023-10-21T14:29:52.702+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T14:29:52.702+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received null
2023-10-21T14:29:52.703+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received end-of-input from client.
2023-10-21T14:29:52.702+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@5f33f4f3
2023-10-21T14:29:52.703+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-21T14:29:52.704+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-21T14:29:56.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:29:56.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:29:56.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:29:56.093+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:29:56.093+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:29:56.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:06.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:06.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:06.089+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:06.089+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:06.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:06.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:06.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:06.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:06.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:16.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:16.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:16.095+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:16.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:16.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:16.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:16.098+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:16.098+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:16.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:26.087+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:26.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:26.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:26.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:26.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:26.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:26.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:26.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:26.093+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:36.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:36.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:36.095+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:36.096+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:36.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:36.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:36.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:36.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:36.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:46.086+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:46.087+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:46.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:46.089+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:46.089+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:46.089+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:46.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:46.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:46.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:56.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:56.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:56.095+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:56.096+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:56.096+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:56.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:30:56.098+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:30:56.098+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:30:56.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:06.087+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:06.087+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:06.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:06.089+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:06.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:06.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:06.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:06.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:06.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:16.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:16.093+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:16.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:16.095+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:16.096+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:16.096+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:16.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:16.097+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:16.098+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:26.088+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:26.089+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:26.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:26.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:26.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:26.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:26.093+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:26.093+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:26.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:36.089+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:36.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:36.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:36.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:36.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:36.093+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:36.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:36.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:36.095+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:46.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:46.090+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:46.091+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:46.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:46.092+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:46.093+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:46.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:46.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:46.094+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:56.098+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:56.099+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:56.100+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:56.101+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:56.102+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:56.102+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:56.103+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T14:31:56.104+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:56.104+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:57.769+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:51174 to /127.0.0.1:49212.
2023-10-21T14:31:57.772+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:51173 to /127.0.0.1:49212.
2023-10-21T14:31:57.779+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 290: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2023-10-21T14:31:57.779+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 290: Received non-IO message from client: StopWhenIdle[id=087f7d2e-8bf6-40c2-9489-acccbfb39c01]
2023-10-21T14:31:57.780+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 291: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2023-10-21T14:31:57.780+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=087f7d2e-8bf6-40c2-9489-acccbfb39c01].
2023-10-21T14:31:57.780+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=087f7d2e-8bf6-40c2-9489-acccbfb39c01] with connection: socket connection from /127.0.0.1:49212 to /127.0.0.1:51174.
2023-10-21T14:31:57.780+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2023-10-21T14:31:57.781+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 291: Received non-IO message from client: StopWhenIdle[id=673d5343-5bc6-4724-a3e9-3ac0a0b45a88]
2023-10-21T14:31:57.783+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=673d5343-5bc6-4724-a3e9-3ac0a0b45a88].
2023-10-21T14:31:57.783+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=673d5343-5bc6-4724-a3e9-3ac0a0b45a88] with connection: socket connection from /127.0.0.1:49212 to /127.0.0.1:51173.
2023-10-21T14:31:57.784+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1697878917781
2023-10-21T14:31:57.786+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:31:57.787+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:57.789+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:57.791+0530 [LIFECYCLE] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Daemon will be stopped at the end of the build stop command received
2023-10-21T14:31:57.791+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Marking daemon stopped due to stop command received. The daemon is running a build: false
2023-10-21T14:31:57.792+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon has stopped.
2023-10-21T14:31:57.792+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2023-10-21T14:31:57.792+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1697878917793
2023-10-21T14:31:57.792+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 288: dispatching Success[value=null]
2023-10-21T14:31:57.792+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stop() called on daemon
2023-10-21T14:31:57.793+0530 [INFO] [org.gradle.launcher.daemon.server.Daemon] Stop requested. Daemon is removing its presence from the registry...
2023-10-21T14:31:57.793+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Removing our presence to clients, eg. removing this address from the registry: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:31:57.793+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=087f7d2e-8bf6-40c2-9489-acccbfb39c01]
2023-10-21T14:31:57.793+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 290: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T14:31:57.794+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 290: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@6792cac1
2023-10-21T14:31:57.794+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T14:31:57.794+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@6792cac1
2023-10-21T14:31:57.794+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 290: received null
2023-10-21T14:31:57.794+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 290: Received end-of-input from client.
2023-10-21T14:31:57.794+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 288: stopping connection
2023-10-21T14:31:57.796+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 288: stopping connection
2023-10-21T14:31:57.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:31:57.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:57.814+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:57.815+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 289: dispatching Success[value=null]
2023-10-21T14:31:57.815+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:31:57.816+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=673d5343-5bc6-4724-a3e9-3ac0a0b45a88]
2023-10-21T14:31:57.816+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 291: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T14:31:57.816+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 291: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@6793aeeb
2023-10-21T14:31:57.816+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T14:31:57.816+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@6793aeeb
2023-10-21T14:31:57.816+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 291: received null
2023-10-21T14:31:57.817+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 289: stopping connection
2023-10-21T14:31:57.817+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 291: Received end-of-input from client.
2023-10-21T14:31:57.818+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 289: stopping connection
2023-10-21T14:31:57.827+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:31:57.828+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:57.841+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T14:31:57.842+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Address removed from registry.
2023-10-21T14:31:57.843+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9)
2023-10-21T14:31:57.844+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9).
2023-10-21T14:31:57.844+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) has last been fully cleaned up 26 hours ago
2023-10-21T14:31:57.849+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) cleanup deleted 0 files/directories.
2023-10-21T14:31:57.850+0530 [INFO] [org.gradle.cache.internal.LeastRecentlyUsedCacheCleanup] jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) removing files not accessed on or after Sat Oct 14 14:27:46 IST 2023.
2023-10-21T14:31:57.863+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) cleanup deleted 0 files/directories.
2023-10-21T14:31:57.864+0530 [INFO] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) cleaned up in 0.019 secs.
2023-10-21T14:31:57.864+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3)
2023-10-21T14:31:57.864+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3).
2023-10-21T14:31:57.865+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3) has last been fully cleaned up 26 hours ago
2023-10-21T14:31:57.866+0530 [INFO] [org.gradle.cache.internal.LeastRecentlyUsedCacheCleanup] Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3) removing files not accessed on or after Sat Oct 14 14:27:49 IST 2023.
2023-10-21T14:31:58.073+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3) cleanup deleted 0 files/directories.
2023-10-21T14:31:58.074+0530 [INFO] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3) cleaned up in 0.208 secs.
2023-10-21T14:31:58.081+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent)
2023-10-21T14:31:58.081+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent).
2023-10-21T14:31:58.082+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Closing VFS, dropping state
2023-10-21T14:31:58.084+0530 [DEBUG] [org.gradle.cache.internal.DefaultCacheAccess] Cache Generated Gradle JARs cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\generated-gradle-jars) was closed 0 times.
2023-10-21T14:31:58.084+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2)
2023-10-21T14:31:58.084+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-metadata.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-metadata.bin)
2023-10-21T14:31:58.084+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifacts.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-artifacts.bin)
2023-10-21T14:31:58.085+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifact.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-artifact.bin)
2023-10-21T14:31:58.085+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2).
2023-10-21T14:31:58.085+0530 [DEBUG] [org.gradle.internal.watch.registry.impl.DefaultFileWatcherRegistry] Finished listening to file system change events
2023-10-21T14:31:58.085+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) has last been fully cleaned up 26 hours ago
2023-10-21T14:31:58.086+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) cleanup deleted 0 files/directories.
2023-10-21T14:31:58.087+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) [subdir: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\resources-2.1] cleanup deleted 0 files/directories.
2023-10-21T14:31:58.087+0530 [INFO] [org.gradle.cache.internal.LeastRecentlyUsedCacheCleanup] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) [subdir: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\resources-2.1] removing files not accessed on or after Thu Sep 21 14:27:52 IST 2023.
2023-10-21T14:31:58.088+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) [subdir: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\resources-2.1] cleanup deleted 0 files/directories.
2023-10-21T14:31:58.088+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) [subdir: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\files-2.1] cleanup deleted 0 files/directories.
2023-10-21T14:31:58.088+0530 [INFO] [org.gradle.cache.internal.LeastRecentlyUsedCacheCleanup] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) [subdir: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\files-2.1] removing files not accessed on or after Thu Sep 21 14:27:52 IST 2023.
2023-10-21T14:31:58.256+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) [subdir: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\files-2.1] cleanup deleted 0 files/directories.
2023-10-21T14:31:58.257+0530 [INFO] [org.gradle.cache.internal.AbstractCacheCleanup] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) [subdir: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99] cleanup deleted 0 files/directories.
2023-10-21T14:31:58.257+0530 [INFO] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) cleaned up in 0.171 secs.
2023-10-21T14:31:58.258+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1)
2023-10-21T14:31:58.258+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache file-access.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1\file-access.bin)
2023-10-21T14:31:58.258+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1).
2023-10-21T14:31:58.260+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes)
2023-10-21T14:31:58.260+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache fileHashes.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes\fileHashes.bin)
2023-10-21T14:31:58.260+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache resourceHashesCache.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes\resourceHashesCache.bin)
2023-10-21T14:31:58.261+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes).
2023-10-21T14:31:58.261+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory)
2023-10-21T14:31:58.261+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache executionHistory.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory\executionHistory.bin)
2023-10-21T14:31:58.261+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory).
2023-10-21T14:31:58.262+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for Java compile cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\javaCompile)
2023-10-21T14:31:58.262+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache jarAnalysis.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\javaCompile\jarAnalysis.bin)
2023-10-21T14:31:58.262+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache classAnalysis.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\javaCompile\classAnalysis.bin)
2023-10-21T14:31:58.262+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on Java compile cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\javaCompile).
2023-10-21T14:31:58.264+0530 [DEBUG] [org.gradle.cache.internal.VersionSpecificCacheCleanupAction] Processed version-specific caches at D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches for cleanup in 0.0 secs
Daemon vm is shutting down... The daemon has exited normally or was terminated in response to a user interrupt.
2023-10-21T14:31:58.273+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [f9c08f12-8290-414e-ab8c-4ab9f0b022bd port:49212, addresses:[localhost/127.0.0.1]]
2023-10-21T14:31:58.285+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T14:31:58.286+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T14:31:58.301+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
