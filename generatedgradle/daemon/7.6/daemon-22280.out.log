2023-10-21T18:29:31.262+0530 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonMain] Assuming the daemon was started with following jvm opts: [--add-opens=java.base/java.util=ALL-UNNAMED, --add-opens=java.base/java.lang=ALL-UNNAMED, --add-opens=java.base/java.lang.invoke=ALL-UNNAMED, --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED, --add-opens=java.base/java.nio.charset=ALL-UNNAMED, --add-opens=java.base/java.net=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED, -Xmx4608m, -Dfile.encoding=windows-1252, -Duser.country=IN, -Duser.language=en, -Duser.variant]
2023-10-21T18:29:31.464+0530 [DEBUG] [org.gradle.internal.nativeintegration.services.NativeServices] Native-platform posix files integration is not available. Continuing with fallback.
2023-10-21T18:29:31.478+0530 [INFO] [org.gradle.launcher.daemon.server.Daemon] start() called on daemon - DefaultDaemonContext[uid=6533018f-f634-4d24-8dd3-d62452a02634,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=22280,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-21T18:29:31.593+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Software Loopback Interface 1
2023-10-21T18:29:31.595+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? true
2023-10-21T18:29:31.595+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /127.0.0.1
2023-10-21T18:29:31.596+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /0:0:0:0:0:0:0:1
2023-10-21T18:29:31.596+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft 6to4 Adapter
2023-10-21T18:29:31.599+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.599+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (PPTP)
2023-10-21T18:29:31.602+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.602+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (L2TP)
2023-10-21T18:29:31.605+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.606+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft IP-HTTPS Platform Adapter
2023-10-21T18:29:31.608+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.608+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)
2023-10-21T18:29:31.610+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.610+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IKEv2)
2023-10-21T18:29:31.612+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.613+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)
2023-10-21T18:29:31.615+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.615+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9
2023-10-21T18:29:31.617+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.618+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:6030:97b:20bb:55a8%eth2
2023-10-21T18:29:31.618+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface RAS Async Adapter
2023-10-21T18:29:31.621+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.622+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (SSTP)
2023-10-21T18:29:31.624+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.624+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (PPPOE)
2023-10-21T18:29:31.627+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.627+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller
2023-10-21T18:29:31.629+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.629+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:c2b9:2b1d:f56d:8cc9%eth3
2023-10-21T18:29:31.630+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Remote NDIS based Internet Sharing Device
2023-10-21T18:29:31.632+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.632+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4
2023-10-21T18:29:31.635+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.635+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2
2023-10-21T18:29:31.637+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.637+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:2265:1f01:3f26:ef68%eth5
2023-10-21T18:29:31.638+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter
2023-10-21T18:29:31.640+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.640+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Bluetooth Device (Personal Area Network)
2023-10-21T18:29:31.642+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.643+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:53ff:cf82:f38b:6711%eth6
2023-10-21T18:29:31.643+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Teredo Tunneling Adapter
2023-10-21T18:29:31.645+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.646+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #2
2023-10-21T18:29:31.649+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.649+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz
2023-10-21T18:29:31.652+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.652+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /***********
2023-10-21T18:29:31.653+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /2401:4900:1ce3:135:80c8:be29:9d1b:cb4d
2023-10-21T18:29:31.653+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /2401:4900:1ce3:135:c1d4:ddf1:3ee8:5511
2023-10-21T18:29:31.653+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:f84b:67bf:69b5:22ba%wlan3
2023-10-21T18:29:31.653+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Kernel Debug Network Adapter
2023-10-21T18:29:31.656+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.656+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)
2023-10-21T18:29:31.658+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.659+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3
2023-10-21T18:29:31.660+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.661+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:bc5:162f:7b13:dfc9%wlan4
2023-10-21T18:29:31.661+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface SAMSUNG Mobile USB Remote NDIS Network Device
2023-10-21T18:29:31.663+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.663+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.665+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.666+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.668+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.668+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-QoS Packet Scheduler-0000
2023-10-21T18:29:31.670+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.670+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-QoS Packet Scheduler-0000
2023-10-21T18:29:31.672+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.672+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.675+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.675+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.678+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.678+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.680+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.680+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-QoS Packet Scheduler-0000
2023-10-21T18:29:31.682+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.682+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.685+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.685+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.687+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.687+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-Virtual WiFi Filter Driver-0000
2023-10-21T18:29:31.689+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.689+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-Native WiFi Filter Driver-0000
2023-10-21T18:29:31.692+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.692+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-QoS Packet Scheduler-0000
2023-10-21T18:29:31.694+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.694+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.696+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.697+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-QoS Packet Scheduler-0000
2023-10-21T18:29:31.699+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.699+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.701+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.701+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-Native WiFi Filter Driver-0000
2023-10-21T18:29:31.704+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.705+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.707+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.707+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.709+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.709+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)-QoS Packet Scheduler-0000
2023-10-21T18:29:31.711+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.711+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.714+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.714+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)-QoS Packet Scheduler-0000
2023-10-21T18:29:31.718+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.718+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-21T18:29:31.723+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.723+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)-QoS Packet Scheduler-0000
2023-10-21T18:29:31.726+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-21T18:29:31.734+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Listening on [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]].
2023-10-21T18:29:31.747+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] Daemon starting at: Sat Oct 21 18:29:31 IST 2023, with address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:29:31.747+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertising the daemon address to the clients: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:29:31.747+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertised daemon context: DefaultDaemonContext[uid=6533018f-f634-4d24-8dd3-d62452a02634,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=22280,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-21T18:29:31.748+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]], context: DefaultDaemonContext[uid=6533018f-f634-4d24-8dd3-d62452a02634,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=22280,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-21T18:29:31.764+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:29:31.770+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:29:31.775+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:29:31.775+0530 [LIFECYCLE] [org.gradle.launcher.daemon.server.Daemon] Daemon server started.
2023-10-21T18:29:31.776+0530 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonStartupCommunication] Completed writing the daemon greeting. Closing streams...
2023-10-21T18:29:31.793+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stopOnExpiration() called on daemon
2023-10-21T18:29:31.794+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] awaitExpiration() called on daemon
2023-10-21T18:29:31.795+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:29:31.802+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:55372 to /127.0.0.1:55371.
2023-10-21T18:29:31.886+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.Build
2023-10-21T18:29:31.887+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received non-IO message from client: Build{id=cfa6ae70-0022-4a68-a703-653d20c019f5, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:29:31.887+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=cfa6ae70-0022-4a68-a703-653d20c019f5, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-21T18:29:31.887+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=cfa6ae70-0022-4a68-a703-653d20c019f5, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:55371 to /127.0.0.1:55372.
2023-10-21T18:29:31.890+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=cfa6ae70-0022-4a68-a703-653d20c019f5, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:55372] after 0.0 minutes of idle
2023-10-21T18:29:31.891+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:29:31.891+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:29:31.891+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:29:31.892+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:29:31.893+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:29:31.893+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:29:31.893+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:29:31.894+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=cfa6ae70-0022-4a68-a703-653d20c019f5, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-21T18:29:31.894+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@77a43d37
2023-10-21T18:29:31.896+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, EFC_13800, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-21T18:29:31.898+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-21T18:29:31.898+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@1cffa050
2023-10-21T18:29:31.902+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-21T18:29:31.903+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 22280). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-22280.out.log
2023-10-21T18:29:31.907+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting build in new daemon [memory: 4.5 GiB]
2023-10-21T18:29:31.908+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-21T18:29:31.909+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-21T18:29:31.916+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-21T18:29:31.916+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=6533018f-f634-4d24-8dd3-d62452a02634,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=22280,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 25s
2 actionable tasks: 2 executed
2023-10-21T18:29:55.554+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-21T18:29:55.571+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:29:55.581+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=cfa6ae70-0022-4a68-a703-653d20c019f5, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:55372]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@70ea34af with state Busy
2023-10-21T18:29:55.582+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=cfa6ae70-0022-4a68-a703-653d20c019f5, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:55372]
2023-10-21T18:29:55.582+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:29:55.582+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:29:55.582+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:29:55.583+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:29:55.584+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:29:55.586+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:29:55.587+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:29:55.587+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@72c5b4bb]
2023-10-21T18:29:55.587+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@72c5b4bb]
2023-10-21T18:29:55.588+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=cfa6ae70-0022-4a68-a703-653d20c019f5, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:29:55.590+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T18:29:55.590+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@70c1bbb3
2023-10-21T18:29:55.590+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@70c1bbb3
2023-10-21T18:29:55.590+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-21T18:29:55.591+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T18:29:55.592+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received null
2023-10-21T18:29:55.592+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received end-of-input from client.
2023-10-21T18:29:55.593+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-21T18:30:01.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:30:01.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:30:01.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:30:01.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:30:01.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:30:01.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:30:11.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:30:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:30:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:30:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:30:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:30:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:30:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:30:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:30:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:30:19.866+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:55466 to /127.0.0.1:55371.
2023-10-21T18:30:19.873+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.Build
2023-10-21T18:30:19.873+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received non-IO message from client: Build{id=f240d2ee-f27e-4d60-b974-cc4055b8ff4a, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:30:19.873+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=f240d2ee-f27e-4d60-b974-cc4055b8ff4a, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-21T18:30:19.874+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=f240d2ee-f27e-4d60-b974-cc4055b8ff4a, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:55371 to /127.0.0.1:55466.
2023-10-21T18:30:19.874+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=f240d2ee-f27e-4d60-b974-cc4055b8ff4a, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:55466] after 0.0 minutes of idle
2023-10-21T18:30:19.874+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:30:19.875+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:30:19.875+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:30:19.876+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:30:19.877+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:30:19.877+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:30:19.877+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:30:19.877+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=f240d2ee-f27e-4d60-b974-cc4055b8ff4a, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-21T18:30:19.878+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@20cc389c
2023-10-21T18:30:19.879+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, EFC_13800, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-21T18:30:19.879+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-21T18:30:19.879+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@578f4c67
2023-10-21T18:30:19.880+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-21T18:30:19.880+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 22280). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-22280.out.log
2023-10-21T18:30:19.881+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 2nd build in daemon [uptime: 48.532 secs, performance: 100%]
2023-10-21T18:30:19.881+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-21T18:30:19.881+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-21T18:30:19.881+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-21T18:30:19.881+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=6533018f-f634-4d24-8dd3-d62452a02634,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=22280,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
Warning: This version only understands SDK XML versions up to 2 but an SDK XML file of version 3 was encountered. This can happen if you use versions of Android Studio and the command-line tools that were released at different times.
Warning: unexpected element (uri:"", local:"extension-level"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
Warning: unexpected element (uri:"", local:"base-extension"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
AGPBI: {"kind":"warning","text":"We recommend using a newer Android Gradle plugin to use compileSdk = 33\n\nThis Android Gradle plugin (7.0.4) was tested up to compileSdk = 31\n\nThis warning can be suppressed by adding\n    android.suppressUnsupportedCompileSdk=33\nto this project's gradle.properties\n\nThe build will continue, but you are strongly encouraged to update your project to\nuse a newer Android Gradle Plugin that has been tested with compileSdk = 33","sources":[{}]}
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:allowBackup was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:usesCleartextTraffic was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\adapter\ViewPagerAdapter.kt: (5, 30): 'FragmentPagerAdapter' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\adapter\ViewPagerAdapter.kt: (10, 52): 'FragmentPagerAdapter' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\TextUtil.kt: (29, 34): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\TextUtil.kt: (31, 34): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (98, 30): 'getter for activeNetworkInfo: NetworkInfo?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (99, 44): 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (176, 27): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (395, 22): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (395, 30): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (395, 64): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (396, 22): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (396, 30): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (396, 64): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (439, 36): 'getDefaultDisplay(): Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (439, 56): 'getMetrics(DisplayMetrics!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (472, 26): 'toLowerCase(Locale): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (4, 20): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (28, 27): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (40, 74): 'yes: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (45, 74): 'no: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (101, 20): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (101, 35): 'show(Context!, CharSequence!, CharSequence!, Boolean): ProgressDialog!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AutoCompleteTextView.kt: (13, 68): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\BadgeTabLayout.kt: (255, 72): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\BadgeTabLayout.kt: (276, 41): 'setColorFilter(Int, PorterDuff.Mode): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\CheckBox.kt: (13, 89): Parameter 'defStyleAttr' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\EditText.kt: (12, 56): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\MultiSelectWindow.kt: (40, 51): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\MultiSelectWindow.kt: (42, 27): 'getSize(Point!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\RemarksDialog.kt: (34, 59): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\RemarksDialog.kt: (37, 23): 'getRealSize(Point!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\RemarksDialog.kt: (63, 65): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\RemarksDialog.kt: (65, 23): 'getRealSize(Point!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\SearchView.kt: (14, 58): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\TextView.kt: (13, 56): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\TextViewBold.kt: (13, 60): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\ViewPager.kt: (29, 13): Name shadowed: heightMeasureSpec
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\ViewPager.kt: (29, 33): Variable 'heightMeasureSpec' initializer is redundant
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\AppDatabase.kt: (315, 24): Variable 'rawQuery' initializer is redundant
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\Dashboard.kt: (173, 28): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\Dashboard.kt: (180, 22): 'getter for fragmentManager: FragmentManager?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\Dashboard.kt: (304, 29): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\LoginScreen.kt: (136, 17): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\LoginScreen.kt: (229, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\LoginScreen.kt: (406, 33): 'getSerializableExtra(String!): Serializable?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\LoginScreen.kt: (410, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\ModuleGridAdapter.kt: (76, 29): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\PaymentScreen.kt: (56, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\PaymentScreen.kt: (124, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\PaymentScreen.kt: (143, 21): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\PaymentScreen.kt: (153, 25): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\SignUpScreen.kt: (285, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\SplashScreen.kt: (73, 28): Unnecessary non-null assertion (!!) on a non-null receiver of type Context
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\SplashScreen.kt: (75, 28): Unnecessary non-null assertion (!!) on a non-null receiver of type Context
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\TransactionDetailScreen.kt: (78, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (124, 21): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (181, 29): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (226, 35): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (273, 17): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (289, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (328, 17): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (516, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (3, 20): 'IntentService' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (13, 22): 'constructor IntentService(String!)' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (13, 22): 'IntentService' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (29, 15): 'onCreate(): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (84, 22): 'onStartCommand(Intent!, Int, Int): Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\AccountsDashboard.kt: (553, 34): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\AccountsDashboard.kt: (553, 49): 'getMetrics(DisplayMetrics!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerAdapter.kt: (52, 35): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerAdapter.kt: (67, 43): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerAdapter.kt: (115, 56): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerAdapter.kt: (115, 84): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerDetails.kt: (184, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (57, 9): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (127, 31): Unsafe use of a nullable receiver of type JSONArray?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (128, 52): Unsafe use of a nullable receiver of type JSONArray?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (130, 21): Unsafe use of a nullable receiver of type JSONArray?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (134, 37): Unsafe use of a nullable receiver of type JSONArray?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\VoucherAdapter.kt: (53, 45): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\VoucherAdapter.kt: (54, 79): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\VoucherAdapter.kt: (56, 52): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\FcmService.kt: (7, 32): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\FcmService.kt: (53, 13): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\FcmService.kt: (53, 46): 'getter for instanceId: Task<InstanceIdResult!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\NotificationUtils.kt: (77, 33): Parameter 'title' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\NotificationUtils.kt: (113, 42): 'fromHtml(String!): Spanned!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\NotificationUtils.kt: (142, 35): Parameter 'pendingIntent' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\NotificationUtils.kt: (142, 65): Parameter 'bitmap' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\Notifications.kt: (123, 25): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\Notifications.kt: (220, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\Notifications.kt: (244, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditFragment.kt: (191, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (283, 22): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (283, 30): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (283, 64): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (284, 22): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (284, 30): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (284, 64): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\GrnPagerAdapter.kt: (377, 17): Condition 'list == null' is always 'false'
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (4, 20): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (57, 27): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (164, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (188, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (355, 34): Parameter 'taskSnapshot' is never used, could be renamed to _
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (373, 20): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (373, 35): 'show(Context!, CharSequence!, CharSequence!, Boolean): ProgressDialog!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (647, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseParticularAdapter.kt: (522, 24): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseParticularAdapter.kt: (575, 24): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseParticularAdapter.kt: (585, 13): Condition 'outputDir != null' is always 'true'
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpensesFragment.kt: (92, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpensesTab.kt: (128, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpensesTab.kt: (164, 21): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialAdapter.kt: (74, 45): 'toLowerCase(Locale): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialArrayAdapter.kt: (68, 68): 'toLowerCase(Locale): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialArrayAdapter.kt: (77, 44): Unchecked cast: Any! to List<MaterialName>
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (58, 9): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (92, 71): Unnecessary non-null assertion (!!) on a non-null receiver of type String
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (295, 23): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (295, 38): 'getMetrics(DisplayMetrics!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (376, 34): Parameter 'a' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialListView.kt: (73, 44): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyAdapter.kt: (80, 56): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyArrayAdapter.kt: (65, 66): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyArrayAdapter.kt: (74, 42): Unchecked cast: Any! to List<PartyName>
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyDetail.kt: (25, 9): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyListView.kt: (77, 44): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApproval.kt: (304, 26): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApproval.kt: (318, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApproval.kt: (350, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApprovalActivity.kt: (210, 26): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApprovalActivity.kt: (367, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApprovalActivity.kt: (381, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RatePagerAdapter.kt: (261, 40): The corresponding parameter in the supertype 'OnDismissListener' is named 'remarks'. This may cause problems when calling this function with named arguments.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\POApprovalActivity.kt: (96, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\POMaterialDialog.kt: (50, 13): 'onBackPressed(): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\POSearch.kt: (262, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\POSearch.kt: (392, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\PoPagerAdapter.kt: (121, 75): 'SOFT_INPUT_ADJUST_RESIZE: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\AddMaterialActivity.kt: (50, 35): 'getSerializable(String?): Serializable?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\AddMaterialActivity.kt: (51, 34): 'getSerializable(String?): Serializable?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\AddMaterialAdapter.kt: (49, 44): Parameter 'position' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\AddTaxAdapter.kt: (42, 38): Parameter 'position' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (293, 38): Parameter 'v' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (415, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (425, 91): Unnecessary non-null assertion (!!) on a non-null receiver of type Invoice
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (489, 21): Variable 'tax' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (784, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (787, 37): 'getSerializableExtra(String!): Serializable?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (936, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (957, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoicePagerAdapter.kt: (120, 75): 'SOFT_INPUT_ADJUST_RESIZE: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (96, 21): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (280, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (290, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (327, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (429, 21): Variable 'isInv' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\OAPagerAdapter.kt: (121, 75): 'SOFT_INPUT_ADJUST_RESIZE: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\SalesApprovalActivity.kt: (134, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\SalesDashboard.kt: (147, 29): Variable 'user' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\SalesDashboard.kt: (246, 25): Variable 'user' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\stores\GrnApprovalActivity.kt: (138, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\stores\GrnDetails.kt: (219, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\stores\GrnDetails.kt: (300, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\stores\MakeWiseStockAdapter.kt: (15, 66): Type mismatch: inferred type is List<MaterialStockDetail>? but (MutableCollection<out MaterialStockDetail!>..Collection<MaterialStockDetail!>) was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (4, 19): 'AsyncTask<Params : Any!, Progress : Any!, Result : Any!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (50, 44): 'execute(vararg Void!): AsyncTask<Void!, Void!, String!>!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (59, 137): 'AsyncTask<Params : Any!, Progress : Any!, Result : Any!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (59, 137): 'constructor AsyncTask<Params : Any!, Progress : Any!, Result : Any!>()' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (85, 23): 'onPostExecute(Result!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (4, 19): 'AsyncTask<Params : Any!, Progress : Any!, Result : Any!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (62, 44): 'execute(vararg Void!): AsyncTask<Void!, Void!, String!>!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (71, 137): 'AsyncTask<Params : Any!, Progress : Any!, Result : Any!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (71, 137): 'constructor AsyncTask<Params : Any!, Progress : Any!, Result : Any!>()' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (91, 23): 'onPostExecute(Result!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (5, 32): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (53, 17): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (53, 50): 'getter for instanceId: Task<InstanceIdResult!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (94, 40): Parameter 'user' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (118, 17): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (118, 50): 'getter for instanceId: Task<InstanceIdResult!>' is deprecated. Deprecated in Java

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 3m 1s
38 actionable tasks: 36 executed, 2 up-to-date
2023-10-21T18:33:21.149+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-21T18:33:21.167+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:33:21.167+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=f240d2ee-f27e-4d60-b974-cc4055b8ff4a, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:55466]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@70ea34af with state Busy
2023-10-21T18:33:21.168+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=f240d2ee-f27e-4d60-b974-cc4055b8ff4a, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:55466]
2023-10-21T18:33:21.168+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:33:21.168+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:33:21.168+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:33:21.169+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:33:21.170+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:21.172+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:21.172+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@4e17e87]
2023-10-21T18:33:21.172+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@4e17e87]
2023-10-21T18:33:21.173+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:33:21.174+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T18:33:21.174+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=f240d2ee-f27e-4d60-b974-cc4055b8ff4a, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:33:21.174+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@5948b8e6
2023-10-21T18:33:21.174+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@5948b8e6
2023-10-21T18:33:21.175+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-21T18:33:21.175+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T18:33:21.175+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received null
2023-10-21T18:33:21.176+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received end-of-input from client.
2023-10-21T18:33:21.177+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-21T18:33:21.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:21.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:21.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:21.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:21.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:21.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:31.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:31.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:31.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:31.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:31.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:31.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:41.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:41.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:41.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:41.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:51.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:51.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:51.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:51.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:51.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:51.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:33:51.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:33:51.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:33:51.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:01.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:01.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:01.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:01.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:01.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:01.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:01.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:01.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:01.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:11.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:11.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:11.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:11.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:11.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:11.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:11.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:11.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:11.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:21.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:21.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:21.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:21.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:21.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:21.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:31.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:31.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:31.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:31.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:31.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:31.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:31.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:41.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:41.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:41.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:41.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:41.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:41.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:51.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:51.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:51.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:34:51.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:34:51.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:34:51.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:01.795+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:01.795+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:01.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:01.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:01.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:01.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:01.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:11.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:11.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:11.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:11.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:11.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:21.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:21.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:21.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:21.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:21.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:21.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:31.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:31.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:31.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:31.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:31.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:31.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:41.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:41.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:41.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:51.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:51.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:51.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:51.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:51.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:51.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:35:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:35:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:35:57.778+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(3)-127.0.0.1: accepted socket from [127.0.0.1:56576]
2023-10-21T18:35:57.783+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(3)-127.0.0.1: (port 55501) op = 80
2023-10-21T18:35:57.785+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(3)-127.0.0.1: name = "[Ljava.rmi.server.ObjID;", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:35:57.785+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(3)-127.0.0.1: name = "java.rmi.dgc.Lease", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:35:57.786+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(3)-127.0.0.1: name = "java.rmi.dgc.VMID", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:35:57.786+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(3)-127.0.0.1: name = "[B", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:35:57.787+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(3)-127.0.0.1: name = "java.rmi.server.UID", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:36:01.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:01.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:01.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:01.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:01.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:01.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:01.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:01.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:01.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:11.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:11.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:11.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:11.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:11.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:11.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:11.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:11.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:11.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:12.792+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(3)-127.0.0.1: (port 55501) connection closed
2023-10-21T18:36:12.792+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(3)-127.0.0.1: close connection, socket: Socket[addr=/127.0.0.1,port=56576,localport=55501]
2023-10-21T18:36:12.793+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(3)-127.0.0.1: socket close: Socket[addr=/127.0.0.1,port=56576,localport=55501]
2023-10-21T18:36:21.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:21.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:21.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:21.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:21.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:21.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:31.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:31.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:31.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:31.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:31.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:41.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:51.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:51.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:51.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:51.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:51.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:51.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:36:51.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:36:51.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:36:51.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:01.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:01.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:01.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:01.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:01.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:01.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:01.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:11.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:11.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:11.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:11.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:11.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:11.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:21.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:21.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:21.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:21.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:31.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:31.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:31.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:41.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:41.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:41.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:41.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:41.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:41.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:51.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:51.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:51.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:37:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:37:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:37:51.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:01.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:01.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:01.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:01.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:01.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:01.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:01.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:01.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:11.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:11.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:11.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:21.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:21.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:21.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:21.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:21.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:21.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:21.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:21.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:21.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:31.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:31.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:31.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:31.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:31.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:31.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:31.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:31.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:31.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:41.795+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:41.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:41.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:41.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:41.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:41.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:41.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:41.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:41.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:51.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:51.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:51.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:51.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:51.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:51.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:38:51.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:38:51.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:38:51.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:01.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:01.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:01.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:11.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:11.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:11.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:21.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:21.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:21.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:21.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:21.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:21.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:21.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:31.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:31.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:31.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:41.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:41.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:41.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:45.215+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change CREATED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\source\buildConfig\production\release\com\schnell\xsmanager\BuildConfig.java~
2023-10-21T18:39:45.219+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\source\buildConfig\production\release\com\schnell\xsmanager\BuildConfig.java
2023-10-21T18:39:51.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:51.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:51.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:51.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:51.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:51.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:39:51.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:39:51.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:39:51.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:01.795+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:40:01.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:01.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:01.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:40:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:40:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:40:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:40:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:11.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:40:11.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:11.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:13.261+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:56787 to /127.0.0.1:55371.
2023-10-21T18:40:13.275+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 313: received class org.gradle.launcher.daemon.protocol.InvalidateVirtualFileSystem
2023-10-21T18:40:13.275+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 313: Received non-IO message from client: InvalidateVirtualFileSystem[id=b5e21975-0627-4206-b256-7935c3a7323b]
2023-10-21T18:40:13.275+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: InvalidateVirtualFileSystem[id=b5e21975-0627-4206-b256-7935c3a7323b].
2023-10-21T18:40:13.275+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: InvalidateVirtualFileSystem[id=b5e21975-0627-4206-b256-7935c3a7323b] with connection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56787.
2023-10-21T18:40:13.276+0530 [INFO] [org.gradle.launcher.daemon.server.api.HandleInvalidateVirtualFileSystem] Invalidating [D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build.gradle]
2023-10-21T18:40:13.276+0530 [DEBUG] [org.gradle.internal.vfs.impl.AbstractVirtualFileSystem] Invalidating VFS paths: [D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build.gradle]
2023-10-21T18:40:13.277+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: dispatching Success[value=null]
2023-10-21T18:40:13.277+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: InvalidateVirtualFileSystem[id=b5e21975-0627-4206-b256-7935c3a7323b]
2023-10-21T18:40:13.277+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 313: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T18:40:13.277+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 313: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@2f45126f
2023-10-21T18:40:13.278+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@2f45126f
2023-10-21T18:40:13.278+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: stopping connection
2023-10-21T18:40:13.278+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T18:40:13.278+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 313: received null
2023-10-21T18:40:13.278+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 313: Received end-of-input from client.
2023-10-21T18:40:13.278+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: stopping connection
2023-10-21T18:40:16.722+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:56793 to /127.0.0.1:55371.
2023-10-21T18:40:16.737+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 314: received class org.gradle.launcher.daemon.protocol.Build
2023-10-21T18:40:16.737+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 314: Received non-IO message from client: Build{id=5e06a8e9-a46c-41bb-8c23-c431b431fc9b, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:40:16.737+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=5e06a8e9-a46c-41bb-8c23-c431b431fc9b, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-21T18:40:16.737+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=5e06a8e9-a46c-41bb-8c23-c431b431fc9b, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56793.
2023-10-21T18:40:16.737+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=5e06a8e9-a46c-41bb-8c23-c431b431fc9b, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56793] after 6.0 minutes of idle
2023-10-21T18:40:16.737+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:16.737+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:16.738+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:40:16.738+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:16.739+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:16.740+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:40:16.740+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:40:16.741+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=5e06a8e9-a46c-41bb-8c23-c431b431fc9b, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-21T18:40:16.741+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 316: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@327ca642
2023-10-21T18:40:16.742+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, EFC_13800, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-21T18:40:16.743+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 314: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-21T18:40:16.743+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 314: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@96520da
2023-10-21T18:40:16.744+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-21T18:40:16.744+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 22280). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-22280.out.log
2023-10-21T18:40:16.745+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 3rd build in daemon [uptime: 10 mins 45.394 secs, performance: 100%]
2023-10-21T18:40:16.745+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-21T18:40:16.745+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-21T18:40:16.745+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-21T18:40:16.745+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=6533018f-f634-4d24-8dd3-d62452a02634,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=22280,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
Warning: unexpected element (uri:"", local:"extension-level"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
Warning: unexpected element (uri:"", local:"base-extension"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 9s
2023-10-21T18:40:22.492+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-21T18:40:22.501+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:40:22.501+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=5e06a8e9-a46c-41bb-8c23-c431b431fc9b, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56793]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@70ea34af with state Busy
2023-10-21T18:40:22.501+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=5e06a8e9-a46c-41bb-8c23-c431b431fc9b, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56793]
2023-10-21T18:40:22.501+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:40:22.501+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:22.501+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:22.502+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:40:22.502+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:22.504+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:22.504+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@16764fe2]
2023-10-21T18:40:22.504+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:40:22.504+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@16764fe2]
2023-10-21T18:40:22.505+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=5e06a8e9-a46c-41bb-8c23-c431b431fc9b, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:40:22.559+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 314: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T18:40:22.559+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 314: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@2dfdeafd
2023-10-21T18:40:22.559+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T18:40:22.559+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 314: received null
2023-10-21T18:40:22.559+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 314: Received end-of-input from client.
2023-10-21T18:40:22.559+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: null
2023-10-21T18:40:22.560+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: stopping connection
2023-10-21T18:40:22.561+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: stopping connection
2023-10-21T18:40:27.325+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:56800 to /127.0.0.1:55371.
2023-10-21T18:40:27.328+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 416: received class org.gradle.launcher.daemon.protocol.Build
2023-10-21T18:40:27.328+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 416: Received non-IO message from client: Build{id=bb7c0cf8-ae4a-49b3-a448-e0cdfba4f92f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:40:27.329+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=bb7c0cf8-ae4a-49b3-a448-e0cdfba4f92f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-21T18:40:27.329+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=bb7c0cf8-ae4a-49b3-a448-e0cdfba4f92f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56800.
2023-10-21T18:40:27.329+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=bb7c0cf8-ae4a-49b3-a448-e0cdfba4f92f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56800] after 0.0 minutes of idle
2023-10-21T18:40:27.330+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:27.330+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:27.330+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:40:27.330+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:27.331+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:27.332+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:40:27.332+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:40:27.332+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=bb7c0cf8-ae4a-49b3-a448-e0cdfba4f92f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-21T18:40:27.332+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 316: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@7fd1f76c
2023-10-21T18:40:27.333+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, EFC_13800, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-21T18:40:27.333+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 416: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-21T18:40:27.334+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 416: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@25e66298
2023-10-21T18:40:27.334+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-21T18:40:27.335+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 22280). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-22280.out.log
2023-10-21T18:40:27.335+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 4th build in daemon [uptime: 10 mins 55.985 secs, performance: 100%]
2023-10-21T18:40:27.335+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-21T18:40:27.336+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=6533018f-f634-4d24-8dd3-d62452a02634,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=22280,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-21T18:40:27.336+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-21T18:40:27.336+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 1s
2 actionable tasks: 2 executed
2023-10-21T18:40:28.597+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-21T18:40:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:40:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=bb7c0cf8-ae4a-49b3-a448-e0cdfba4f92f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56800]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@70ea34af with state Busy
2023-10-21T18:40:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=bb7c0cf8-ae4a-49b3-a448-e0cdfba4f92f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56800]
2023-10-21T18:40:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:40:28.609+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:28.609+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:28.618+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:40:28.619+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:28.633+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:28.634+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@46a367cc]
2023-10-21T18:40:28.634+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:40:28.634+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@46a367cc]
2023-10-21T18:40:28.634+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=bb7c0cf8-ae4a-49b3-a448-e0cdfba4f92f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:40:28.634+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 416: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T18:40:28.634+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 416: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@2b218854
2023-10-21T18:40:28.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@2b218854
2023-10-21T18:40:28.635+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T18:40:28.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: stopping connection
2023-10-21T18:40:28.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 416: received null
2023-10-21T18:40:28.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 416: Received end-of-input from client.
2023-10-21T18:40:28.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: stopping connection
2023-10-21T18:40:31.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:40:31.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:31.818+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:31.819+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:40:31.819+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:31.819+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:38.002+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:56804 to /127.0.0.1:55371.
2023-10-21T18:40:38.006+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 517: received class org.gradle.launcher.daemon.protocol.Build
2023-10-21T18:40:38.006+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 517: Received non-IO message from client: Build{id=09c9e715-039c-49d2-95e8-e1675b36e48f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:40:38.006+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=09c9e715-039c-49d2-95e8-e1675b36e48f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-21T18:40:38.006+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=09c9e715-039c-49d2-95e8-e1675b36e48f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56804.
2023-10-21T18:40:38.006+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=09c9e715-039c-49d2-95e8-e1675b36e48f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56804] after 0.0 minutes of idle
2023-10-21T18:40:38.007+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:38.007+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:40:38.007+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:40:38.008+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:40:38.009+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:40:38.009+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:40:38.010+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:40:38.010+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=09c9e715-039c-49d2-95e8-e1675b36e48f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-21T18:40:38.010+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 316: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@104d0cdc
2023-10-21T18:40:38.011+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, EFC_13800, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-21T18:40:38.011+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 517: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-21T18:40:38.012+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 517: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@35754c4d
2023-10-21T18:40:38.012+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-21T18:40:38.012+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 22280). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-22280.out.log
2023-10-21T18:40:38.013+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 5th build in daemon [uptime: 11 mins 6.663 secs, performance: 100%]
2023-10-21T18:40:38.013+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-21T18:40:38.013+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=6533018f-f634-4d24-8dd3-d62452a02634,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=22280,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx4608m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-21T18:40:38.013+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-21T18:40:38.014+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
Warning: unexpected element (uri:"", local:"extension-level"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
Warning: unexpected element (uri:"", local:"base-extension"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
AGPBI: {"kind":"warning","text":"We recommend using a newer Android Gradle plugin to use compileSdk = 33\n\nThis Android Gradle plugin (7.0.4) was tested up to compileSdk = 31\n\nThis warning can be suppressed by adding\n    android.suppressUnsupportedCompileSdk=33\nto this project's gradle.properties\n\nThe build will continue, but you are strongly encouraged to update your project to\nuse a newer Android Gradle Plugin that has been tested with compileSdk = 33","sources":[{}]}
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:allowBackup was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:usesCleartextTraffic was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\adapter\ViewPagerAdapter.kt: (5, 30): 'FragmentPagerAdapter' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\adapter\ViewPagerAdapter.kt: (10, 52): 'FragmentPagerAdapter' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\TextUtil.kt: (29, 34): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\TextUtil.kt: (31, 34): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (98, 30): 'getter for activeNetworkInfo: NetworkInfo?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (99, 44): 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (176, 27): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (395, 22): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (395, 30): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (395, 64): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (396, 22): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (396, 30): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (396, 64): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (439, 36): 'getDefaultDisplay(): Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (439, 56): 'getMetrics(DisplayMetrics!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\util\Utility.kt: (472, 26): 'toLowerCase(Locale): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (4, 20): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (28, 27): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (40, 74): 'yes: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (45, 74): 'no: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (101, 20): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AppDialogs.kt: (101, 35): 'show(Context!, CharSequence!, CharSequence!, Boolean): ProgressDialog!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\AutoCompleteTextView.kt: (13, 68): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\BadgeTabLayout.kt: (255, 72): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\BadgeTabLayout.kt: (276, 41): 'setColorFilter(Int, PorterDuff.Mode): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\CheckBox.kt: (13, 89): Parameter 'defStyleAttr' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\EditText.kt: (12, 56): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\MultiSelectWindow.kt: (40, 51): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\MultiSelectWindow.kt: (42, 27): 'getSize(Point!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\RemarksDialog.kt: (34, 59): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\RemarksDialog.kt: (37, 23): 'getRealSize(Point!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\RemarksDialog.kt: (63, 65): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\RemarksDialog.kt: (65, 23): 'getRealSize(Point!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\SearchView.kt: (14, 58): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\TextView.kt: (13, 56): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\TextViewBold.kt: (13, 60): Parameter 'defStyle' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\ViewPager.kt: (29, 13): Name shadowed: heightMeasureSpec
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\widget\ViewPager.kt: (29, 33): Variable 'heightMeasureSpec' initializer is redundant
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\AppDatabase.kt: (315, 24): Variable 'rawQuery' initializer is redundant
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\Dashboard.kt: (173, 28): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\Dashboard.kt: (180, 22): 'getter for fragmentManager: FragmentManager?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\Dashboard.kt: (304, 29): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\LoginScreen.kt: (136, 17): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\LoginScreen.kt: (229, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\LoginScreen.kt: (406, 33): 'getSerializableExtra(String!): Serializable?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\LoginScreen.kt: (410, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\ModuleGridAdapter.kt: (76, 29): 'toUpperCase(): String' is deprecated. Use uppercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\PaymentScreen.kt: (56, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\PaymentScreen.kt: (124, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\PaymentScreen.kt: (143, 21): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\PaymentScreen.kt: (153, 25): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\SignUpScreen.kt: (285, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\SplashScreen.kt: (73, 28): Unnecessary non-null assertion (!!) on a non-null receiver of type Context
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\SplashScreen.kt: (75, 28): Unnecessary non-null assertion (!!) on a non-null receiver of type Context
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\TransactionDetailScreen.kt: (78, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (124, 21): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (181, 29): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (226, 35): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (273, 17): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (289, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (328, 17): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSDashboard.kt: (516, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (3, 20): 'IntentService' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (13, 22): 'constructor IntentService(String!)' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (13, 22): 'IntentService' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (29, 15): 'onCreate(): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\XSerpService.kt: (84, 22): 'onStartCommand(Intent!, Int, Int): Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\AccountsDashboard.kt: (553, 34): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\AccountsDashboard.kt: (553, 49): 'getMetrics(DisplayMetrics!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerAdapter.kt: (52, 35): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerAdapter.kt: (67, 43): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerAdapter.kt: (115, 56): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerAdapter.kt: (115, 84): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerDetails.kt: (184, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (57, 9): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (127, 31): Unsafe use of a nullable receiver of type JSONArray?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (128, 52): Unsafe use of a nullable receiver of type JSONArray?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (130, 21): Unsafe use of a nullable receiver of type JSONArray?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\LedgerListActivity.kt: (134, 37): Unsafe use of a nullable receiver of type JSONArray?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\VoucherAdapter.kt: (53, 45): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\VoucherAdapter.kt: (54, 79): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\account\VoucherAdapter.kt: (56, 52): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\FcmService.kt: (7, 32): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\FcmService.kt: (53, 13): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\FcmService.kt: (53, 46): 'getter for instanceId: Task<InstanceIdResult!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\NotificationUtils.kt: (77, 33): Parameter 'title' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\NotificationUtils.kt: (113, 42): 'fromHtml(String!): Spanned!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\NotificationUtils.kt: (142, 35): Parameter 'pendingIntent' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\NotificationUtils.kt: (142, 65): Parameter 'bitmap' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\Notifications.kt: (123, 25): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\Notifications.kt: (220, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\attention\Notifications.kt: (244, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditFragment.kt: (191, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (283, 22): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (283, 30): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (283, 64): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (284, 22): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (284, 30): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\AuditTab.kt: (284, 64): Type mismatch: inferred type is String? but String was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\auditing\GrnPagerAdapter.kt: (377, 17): Condition 'list == null' is always 'false'
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (4, 20): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (57, 27): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (164, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (188, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (355, 34): Parameter 'taskSnapshot' is never used, could be renamed to _
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (373, 20): 'ProgressDialog' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (373, 35): 'show(Context!, CharSequence!, CharSequence!, Boolean): ProgressDialog!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseActivity.kt: (647, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseParticularAdapter.kt: (522, 24): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseParticularAdapter.kt: (575, 24): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpenseParticularAdapter.kt: (585, 13): Condition 'outputDir != null' is always 'true'
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpensesFragment.kt: (92, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpensesTab.kt: (128, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\expenses\ExpensesTab.kt: (164, 21): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialAdapter.kt: (74, 45): 'toLowerCase(Locale): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialArrayAdapter.kt: (68, 68): 'toLowerCase(Locale): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialArrayAdapter.kt: (77, 44): Unchecked cast: Any! to List<MaterialName>
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (58, 9): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (92, 71): Unnecessary non-null assertion (!!) on a non-null receiver of type String
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (295, 23): 'getter for defaultDisplay: Display!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (295, 38): 'getMetrics(DisplayMetrics!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialDetail.kt: (376, 34): Parameter 'a' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\MaterialListView.kt: (73, 44): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyAdapter.kt: (80, 56): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyArrayAdapter.kt: (65, 66): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyArrayAdapter.kt: (74, 42): Unchecked cast: Any! to List<PartyName>
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyDetail.kt: (25, 9): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\PartyListView.kt: (77, 44): 'toLowerCase(): String' is deprecated. Use lowercase() instead.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApproval.kt: (304, 26): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApproval.kt: (318, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApproval.kt: (350, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApprovalActivity.kt: (210, 26): Unsafe use of a nullable receiver of type Date?
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApprovalActivity.kt: (367, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RateApprovalActivity.kt: (381, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\masters\RatePagerAdapter.kt: (261, 40): The corresponding parameter in the supertype 'OnDismissListener' is named 'remarks'. This may cause problems when calling this function with named arguments.
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\POApprovalActivity.kt: (96, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\POMaterialDialog.kt: (50, 13): 'onBackPressed(): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\POSearch.kt: (262, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\POSearch.kt: (392, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\purchase\PoPagerAdapter.kt: (121, 75): 'SOFT_INPUT_ADJUST_RESIZE: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\AddMaterialActivity.kt: (50, 35): 'getSerializable(String?): Serializable?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\AddMaterialActivity.kt: (51, 34): 'getSerializable(String?): Serializable?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\AddMaterialAdapter.kt: (49, 44): Parameter 'position' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\AddTaxAdapter.kt: (42, 38): Parameter 'position' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (293, 38): Parameter 'v' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (415, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (425, 91): Unnecessary non-null assertion (!!) on a non-null receiver of type Invoice
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (489, 21): Variable 'tax' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (784, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Overrides deprecated member in 'androidx.activity.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (787, 37): 'getSerializableExtra(String!): Serializable?' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (936, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceCreateActivity.kt: (957, 13): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoicePagerAdapter.kt: (120, 75): 'SOFT_INPUT_ADJUST_RESIZE: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (96, 21): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (280, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (290, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (327, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\InvoiceSearch.kt: (429, 21): Variable 'isInv' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\OAPagerAdapter.kt: (121, 75): 'SOFT_INPUT_ADJUST_RESIZE: Int' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\SalesApprovalActivity.kt: (134, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\SalesDashboard.kt: (147, 29): Variable 'user' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\sales\SalesDashboard.kt: (246, 25): Variable 'user' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\stores\GrnApprovalActivity.kt: (138, 15): 'onBackPressed(): Unit' is deprecated. Overrides deprecated member in 'androidx.core.app.ComponentActivity'. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\stores\GrnDetails.kt: (219, 15): 'onActivityResult(Int, Int, Intent?): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\stores\GrnDetails.kt: (300, 9): 'startActivityForResult(Intent!, Int): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\stores\MakeWiseStockAdapter.kt: (15, 66): Type mismatch: inferred type is List<MaterialStockDetail>? but (MutableCollection<out MaterialStockDetail!>..Collection<MaterialStockDetail!>) was expected
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (4, 19): 'AsyncTask<Params : Any!, Progress : Any!, Result : Any!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (50, 44): 'execute(vararg Void!): AsyncTask<Void!, Void!, String!>!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (59, 137): 'AsyncTask<Params : Any!, Progress : Any!, Result : Any!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (59, 137): 'constructor AsyncTask<Params : Any!, Progress : Any!, Result : Any!>()' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\ExpenseService.kt: (85, 23): 'onPostExecute(Result!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (4, 19): 'AsyncTask<Params : Any!, Progress : Any!, Result : Any!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (62, 44): 'execute(vararg Void!): AsyncTask<Void!, Void!, String!>!' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (71, 137): 'AsyncTask<Params : Any!, Progress : Any!, Result : Any!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (71, 137): 'constructor AsyncTask<Params : Any!, Progress : Any!, Result : Any!>()' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\SalesService.kt: (91, 23): 'onPostExecute(Result!): Unit' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (5, 32): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (53, 17): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (53, 50): 'getter for instanceId: Task<InstanceIdResult!>' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (94, 40): Parameter 'user' is never used
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (118, 17): 'FirebaseInstanceId' is deprecated. Deprecated in Java
w: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\java\com\schnell\xsmanager\webservice\UserService.kt: (118, 50): 'getter for instanceId: Task<InstanceIdResult!>' is deprecated. Deprecated in Java

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 1m 17s
38 actionable tasks: 36 executed, 2 up-to-date
2023-10-21T18:41:55.832+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-21T18:41:55.841+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=09c9e715-039c-49d2-95e8-e1675b36e48f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56804]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@70ea34af with state Busy
2023-10-21T18:41:55.842+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=09c9e715-039c-49d2-95e8-e1675b36e48f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:55371 to /127.0.0.1:56804]
2023-10-21T18:41:55.842+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-21T18:41:55.842+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:41:55.842+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:41:55.842+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:41:55.843+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:41:55.843+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:41:55.844+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-21T18:41:55.844+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@7ff72ce4]
2023-10-21T18:41:55.844+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@7ff72ce4]
2023-10-21T18:41:55.844+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=09c9e715-039c-49d2-95e8-e1675b36e48f, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-21T18:41:55.844+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 517: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T18:41:55.845+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 517: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@3bb2a493
2023-10-21T18:41:55.845+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@3bb2a493
2023-10-21T18:41:55.845+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T18:41:55.845+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: stopping connection
2023-10-21T18:41:55.845+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 517: received null
2023-10-21T18:41:55.845+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 517: Received end-of-input from client.
2023-10-21T18:41:55.845+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 312: stopping connection
2023-10-21T18:41:56.016+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(6)-127.0.0.1: (port 55501) connection closed
2023-10-21T18:41:56.016+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(6)-127.0.0.1: close connection, socket: Socket[addr=/127.0.0.1,port=56857,localport=55501]
2023-10-21T18:41:56.017+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(6)-127.0.0.1: socket close: Socket[addr=/127.0.0.1,port=56857,localport=55501]
2023-10-21T18:41:57.483+0530 [DEBUG] [sun.rmi.transport.tcp] RMI Scheduler(0): close connection, socket: Socket[addr=localhost/127.0.0.1,port=17132,localport=56863]
2023-10-21T18:42:01.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:01.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:01.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:01.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:11.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:11.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:11.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:11.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:11.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:11.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:11.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:21.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:21.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:21.796+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:21.797+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:21.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:21.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:21.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:21.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:21.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:31.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:31.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:31.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:31.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:31.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:31.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:41.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:41.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:41.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:41.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:41.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:41.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:41.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:41.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:41.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:51.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:51.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:51.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:51.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:51.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:51.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:42:51.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:42:51.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:42:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:01.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:01.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:01.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:01.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:01.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:01.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:01.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:01.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:01.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:11.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:11.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:11.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:11.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:21.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:21.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:21.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:21.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:31.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:31.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:31.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:31.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:31.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:31.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:41.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:41.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:41.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:41.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:41.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:41.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:41.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:41.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:41.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:51.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:51.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:51.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:51.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:51.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:51.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:43:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:43:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:43:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:01.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:01.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:01.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:01.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:01.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:01.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:01.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:01.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:01.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:11.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:11.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:11.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:11.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:11.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:11.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:21.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:21.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:21.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:21.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:21.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:21.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:31.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:31.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:31.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:31.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:31.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:31.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:41.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:41.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:41.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:41.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:51.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:51.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:51.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:51.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:44:51.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:44:51.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:44:51.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:01.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:01.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:01.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:01.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:01.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:01.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:01.814+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:01.814+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:01.815+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:11.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:11.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:11.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:11.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:11.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:11.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:11.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:21.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:21.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:21.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:21.802+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:21.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:31.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:31.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:31.803+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:31.804+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:31.805+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:41.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:41.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:41.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:41.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:41.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:51.344+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(7)-127.0.0.1: accepted socket from [127.0.0.1:57268]
2023-10-21T18:45:51.345+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(7)-127.0.0.1: (port 55501) op = 80
2023-10-21T18:45:51.345+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(7)-127.0.0.1: name = "[Ljava.rmi.server.ObjID;", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:51.346+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(7)-127.0.0.1: name = "java.rmi.dgc.Lease", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:51.346+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(7)-127.0.0.1: name = "java.rmi.dgc.VMID", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:51.346+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(7)-127.0.0.1: name = "[B", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:51.346+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(7)-127.0.0.1: name = "java.rmi.server.UID", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:51.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:51.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:51.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:51.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:45:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:45:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:45:57.791+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(8)-127.0.0.1: accepted socket from [127.0.0.1:57271]
2023-10-21T18:45:57.792+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(8)-127.0.0.1: (port 55501) op = 80
2023-10-21T18:45:57.792+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(8)-127.0.0.1: name = "[Ljava.rmi.server.ObjID;", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:57.793+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(8)-127.0.0.1: name = "java.rmi.dgc.Lease", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:57.793+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(8)-127.0.0.1: name = "java.rmi.dgc.VMID", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:57.793+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(8)-127.0.0.1: name = "[B", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:45:57.793+0530 [DEBUG] [sun.rmi.loader] RMI TCP Connection(8)-127.0.0.1: name = "java.rmi.server.UID", codebase = "", defaultLoader = jdk.internal.loader.ClassLoaders$PlatformClassLoader@6e8e0b56
2023-10-21T18:46:01.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:01.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:01.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:01.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:01.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:01.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:01.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:01.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:01.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:06.355+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(7)-127.0.0.1: (port 55501) connection closed
2023-10-21T18:46:06.355+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(7)-127.0.0.1: close connection, socket: Socket[addr=/127.0.0.1,port=57268,localport=55501]
2023-10-21T18:46:06.356+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(7)-127.0.0.1: socket close: Socket[addr=/127.0.0.1,port=57268,localport=55501]
2023-10-21T18:46:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:12.798+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(8)-127.0.0.1: (port 55501) connection closed
2023-10-21T18:46:12.798+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(8)-127.0.0.1: close connection, socket: Socket[addr=/127.0.0.1,port=57271,localport=55501]
2023-10-21T18:46:12.798+0530 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(8)-127.0.0.1: socket close: Socket[addr=/127.0.0.1,port=57271,localport=55501]
2023-10-21T18:46:21.806+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:21.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:21.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:21.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:21.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:21.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:21.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:31.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:31.807+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:31.808+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:31.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:31.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:31.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:31.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:41.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:41.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:41.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:41.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:41.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:41.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:41.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:41.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:41.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:51.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:51.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:51.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:51.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:46:51.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:46:51.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:46:51.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:01.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:47:01.798+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:01.799+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:47:01.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:01.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:01.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:47:01.800+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:01.801+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:11.809+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:47:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:11.810+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:11.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:47:11.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:11.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:11.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-21T18:47:11.812+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:11.813+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:14.139+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:57370 to /127.0.0.1:55371.
2023-10-21T18:47:14.143+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:57371 to /127.0.0.1:55371.
2023-10-21T18:47:14.152+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 676: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2023-10-21T18:47:14.153+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 677: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2023-10-21T18:47:14.153+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 676: Received non-IO message from client: StopWhenIdle[id=446f947f-076c-482e-a675-4dd53d1e5b31]
2023-10-21T18:47:14.153+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 677: Received non-IO message from client: StopWhenIdle[id=194a61ea-54ba-4305-a1f3-b9265ae91618]
2023-10-21T18:47:14.153+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=446f947f-076c-482e-a675-4dd53d1e5b31].
2023-10-21T18:47:14.153+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=194a61ea-54ba-4305-a1f3-b9265ae91618].
2023-10-21T18:47:14.153+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=446f947f-076c-482e-a675-4dd53d1e5b31] with connection: socket connection from /127.0.0.1:55371 to /127.0.0.1:57370.
2023-10-21T18:47:14.153+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=194a61ea-54ba-4305-a1f3-b9265ae91618] with connection: socket connection from /127.0.0.1:55371 to /127.0.0.1:57371.
2023-10-21T18:47:14.153+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2023-10-21T18:47:14.156+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1697894234149
2023-10-21T18:47:14.157+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:47:14.157+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:14.158+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:14.158+0530 [LIFECYCLE] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Daemon will be stopped at the end of the build stop command received
2023-10-21T18:47:14.159+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Marking daemon stopped due to stop command received. The daemon is running a build: false
2023-10-21T18:47:14.159+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon has stopped.
2023-10-21T18:47:14.159+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stop() called on daemon
2023-10-21T18:47:14.159+0530 [INFO] [org.gradle.launcher.daemon.server.Daemon] Stop requested. Daemon is removing its presence from the registry...
2023-10-21T18:47:14.159+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Removing our presence to clients, eg. removing this address from the registry: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:47:14.159+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:47:14.159+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2023-10-21T18:47:14.159+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 674: dispatching Success[value=null]
2023-10-21T18:47:14.160+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=446f947f-076c-482e-a675-4dd53d1e5b31]
2023-10-21T18:47:14.162+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 676: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T18:47:14.163+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 676: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@4631dbb9
2023-10-21T18:47:14.163+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@4631dbb9
2023-10-21T18:47:14.163+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T18:47:14.163+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 674: stopping connection
2023-10-21T18:47:14.163+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 676: received null
2023-10-21T18:47:14.163+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 676: Received end-of-input from client.
2023-10-21T18:47:14.167+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 674: stopping connection
2023-10-21T18:47:14.171+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:47:14.172+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:14.190+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:14.191+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1697894234149
2023-10-21T18:47:14.191+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Address removed from registry.
2023-10-21T18:47:14.207+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:47:14.208+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:14.230+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-21T18:47:14.232+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 675: dispatching Success[value=null]
2023-10-21T18:47:14.232+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=194a61ea-54ba-4305-a1f3-b9265ae91618]
2023-10-21T18:47:14.293+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 677: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-21T18:47:14.293+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 677: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@642187f3
2023-10-21T18:47:14.293+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@642187f3
2023-10-21T18:47:14.293+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-21T18:47:14.294+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 677: received null
2023-10-21T18:47:14.293+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 675: stopping connection
2023-10-21T18:47:14.294+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 677: Received end-of-input from client.
2023-10-21T18:47:14.296+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 675: stopping connection
2023-10-21T18:47:14.299+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9)
2023-10-21T18:47:14.300+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9).
2023-10-21T18:47:14.300+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) has last been fully cleaned up 4 hours ago
2023-10-21T18:47:14.301+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3)
2023-10-21T18:47:14.301+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3).
2023-10-21T18:47:14.302+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3) has last been fully cleaned up 4 hours ago
2023-10-21T18:47:14.314+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent)
2023-10-21T18:47:14.314+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent).
2023-10-21T18:47:14.315+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Closing VFS, dropping state
2023-10-21T18:47:14.318+0530 [DEBUG] [org.gradle.cache.internal.DefaultCacheAccess] Cache Generated Gradle JARs cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\generated-gradle-jars) was closed 0 times.
2023-10-21T18:47:14.318+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2)
2023-10-21T18:47:14.318+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-metadata.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-metadata.bin)
2023-10-21T18:47:14.318+0530 [DEBUG] [org.gradle.internal.watch.registry.impl.DefaultFileWatcherRegistry] Finished listening to file system change events
2023-10-21T18:47:14.318+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifacts.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-artifacts.bin)
2023-10-21T18:47:14.319+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifact.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-artifact.bin)
2023-10-21T18:47:14.319+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2).
2023-10-21T18:47:14.320+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) has last been fully cleaned up 4 hours ago
2023-10-21T18:47:14.321+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1)
2023-10-21T18:47:14.322+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache file-access.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1\file-access.bin)
2023-10-21T18:47:14.322+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1).
2023-10-21T18:47:14.322+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes)
2023-10-21T18:47:14.323+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache fileHashes.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes\fileHashes.bin)
2023-10-21T18:47:14.323+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache resourceHashesCache.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes\resourceHashesCache.bin)
2023-10-21T18:47:14.323+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes).
2023-10-21T18:47:14.325+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory)
2023-10-21T18:47:14.325+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache executionHistory.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory\executionHistory.bin)
2023-10-21T18:47:14.326+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory).
2023-10-21T18:47:14.329+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for Java compile cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\javaCompile)
2023-10-21T18:47:14.329+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache jarAnalysis.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\javaCompile\jarAnalysis.bin)
2023-10-21T18:47:14.329+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache classAnalysis.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\javaCompile\classAnalysis.bin)
2023-10-21T18:47:14.329+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on Java compile cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\javaCompile).
Daemon vm is shutting down... The daemon has exited normally or was terminated in response to a user interrupt.
2023-10-21T18:47:14.335+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [3f120e0b-61f0-44fd-ac3e-d7e84a4a9ab1 port:55371, addresses:[localhost/127.0.0.1]]
2023-10-21T18:47:14.350+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-21T18:47:14.350+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-21T18:47:14.367+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
