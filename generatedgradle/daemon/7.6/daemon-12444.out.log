2023-10-20T12:50:19.129+0530 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonMain] Assuming the daemon was started with following jvm opts: [--add-opens=java.base/java.util=ALL-UNNAMED, --add-opens=java.base/java.lang=ALL-UNNAMED, --add-opens=java.base/java.lang.invoke=ALL-UNNAMED, --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED, --add-opens=java.base/java.nio.charset=ALL-UNNAMED, --add-opens=java.base/java.net=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED, -Xmx1536m, -Dfile.encoding=windows-1252, -Duser.country=IN, -Duser.language=en, -Duser.variant]
2023-10-20T12:50:19.432+0530 [DEBUG] [org.gradle.internal.nativeintegration.services.NativeServices] Native-platform posix files integration is not available. Continuing with fallback.
2023-10-20T12:50:19.463+0530 [INFO] [org.gradle.launcher.daemon.server.Daemon] start() called on daemon - DefaultDaemonContext[uid=93eb786e-4b45-4237-b48f-b2aa4560f453,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=12444,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:50:19.666+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Software Loopback Interface 1
2023-10-20T12:50:19.670+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? true
2023-10-20T12:50:19.670+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /127.0.0.1
2023-10-20T12:50:19.671+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /0:0:0:0:0:0:0:1
2023-10-20T12:50:19.672+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft 6to4 Adapter
2023-10-20T12:50:19.675+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.676+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (PPTP)
2023-10-20T12:50:19.679+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.680+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (L2TP)
2023-10-20T12:50:19.683+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.684+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft IP-HTTPS Platform Adapter
2023-10-20T12:50:19.687+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.688+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)
2023-10-20T12:50:19.692+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.693+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IKEv2)
2023-10-20T12:50:19.701+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.702+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)
2023-10-20T12:50:19.707+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.707+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9
2023-10-20T12:50:19.710+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.710+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:6030:97b:20bb:55a8%eth2
2023-10-20T12:50:19.711+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface RAS Async Adapter
2023-10-20T12:50:19.714+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.715+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (SSTP)
2023-10-20T12:50:19.718+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.719+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (PPPOE)
2023-10-20T12:50:19.723+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.724+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller
2023-10-20T12:50:19.728+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.729+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:c2b9:2b1d:f56d:8cc9%eth3
2023-10-20T12:50:19.729+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Remote NDIS based Internet Sharing Device
2023-10-20T12:50:19.735+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.735+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4
2023-10-20T12:50:19.740+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.741+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:cf1f:8a04:2276:509c%wlan0
2023-10-20T12:50:19.741+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2
2023-10-20T12:50:19.744+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.744+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:2265:1f01:3f26:ef68%eth5
2023-10-20T12:50:19.745+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter
2023-10-20T12:50:19.747+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.747+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Bluetooth Device (Personal Area Network)
2023-10-20T12:50:19.750+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.750+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:53ff:cf82:f38b:6711%eth6
2023-10-20T12:50:19.750+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Teredo Tunneling Adapter
2023-10-20T12:50:19.753+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.754+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #2
2023-10-20T12:50:19.757+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.757+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz
2023-10-20T12:50:19.759+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.760+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /**************
2023-10-20T12:50:19.760+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:f84b:67bf:69b5:22ba%wlan3
2023-10-20T12:50:19.761+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Kernel Debug Network Adapter
2023-10-20T12:50:19.765+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.765+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)
2023-10-20T12:50:19.769+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.769+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3
2023-10-20T12:50:19.773+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.773+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:bc5:162f:7b13:dfc9%wlan4
2023-10-20T12:50:19.773+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface SAMSUNG Mobile USB Remote NDIS Network Device
2023-10-20T12:50:19.776+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.776+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.779+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.780+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.783+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.783+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-QoS Packet Scheduler-0000
2023-10-20T12:50:19.787+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.788+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9 #2-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.791+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.791+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.795+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.795+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-QoS Packet Scheduler-0000
2023-10-20T12:50:19.799+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.799+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.802+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.802+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-QoS Packet Scheduler-0000
2023-10-20T12:50:19.805+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.806+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Realtek PCIe GbE Family Controller-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.809+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.810+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.813+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.813+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-Virtual WiFi Filter Driver-0000
2023-10-20T12:50:19.815+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.815+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-Native WiFi Filter Driver-0000
2023-10-20T12:50:19.818+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.819+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-QoS Packet Scheduler-0000
2023-10-20T12:50:19.822+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.822+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Intel(R) Wi-Fi 6 AX201 160MHz-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.825+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.825+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface TAP-Windows Adapter V9-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.827+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.827+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.830+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.830+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-Native WiFi Filter Driver-0000
2023-10-20T12:50:19.832+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.833+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-QoS Packet Scheduler-0000
2023-10-20T12:50:19.835+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.836+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.838+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.838+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IP)-QoS Packet Scheduler-0000
2023-10-20T12:50:19.841+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.841+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.844+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.844+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (IPv6)-QoS Packet Scheduler-0000
2023-10-20T12:50:19.846+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.846+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)-WFP Native MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.849+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.849+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface WAN Miniport (Network Monitor)-QoS Packet Scheduler-0000
2023-10-20T12:50:19.852+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.853+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #3-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.856+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.856+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-Native WiFi Filter Driver-0000
2023-10-20T12:50:19.859+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.859+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-QoS Packet Scheduler-0000
2023-10-20T12:50:19.862+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.862+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface Microsoft Wi-Fi Direct Virtual Adapter #4-WFP 802.3 MAC Layer LightWeight Filter-0000
2023-10-20T12:50:19.865+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2023-10-20T12:50:19.875+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Listening on [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]].
2023-10-20T12:50:19.888+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] Daemon starting at: Fri Oct 20 12:50:19 IST 2023, with address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:50:19.889+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertising the daemon address to the clients: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:50:19.889+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertised daemon context: DefaultDaemonContext[uid=93eb786e-4b45-4237-b48f-b2aa4560f453,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=12444,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:50:19.890+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]], context: DefaultDaemonContext[uid=93eb786e-4b45-4237-b48f-b2aa4560f453,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=12444,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:50:19.907+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:50:19.912+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:50:19.920+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:50:19.921+0530 [LIFECYCLE] [org.gradle.launcher.daemon.server.Daemon] Daemon server started.
2023-10-20T12:50:19.923+0530 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonStartupCommunication] Completed writing the daemon greeting. Closing streams...
2023-10-20T12:50:19.929+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stopOnExpiration() called on daemon
2023-10-20T12:50:19.930+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] awaitExpiration() called on daemon
2023-10-20T12:50:19.931+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:50:19.953+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59155 to /127.0.0.1:59154.
2023-10-20T12:50:20.050+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:50:20.051+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received non-IO message from client: Build{id=48c68b16-c02b-40bf-becc-0036bb899b28, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:50:20.054+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=48c68b16-c02b-40bf-becc-0036bb899b28, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:50:20.054+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=48c68b16-c02b-40bf-becc-0036bb899b28, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59155.
2023-10-20T12:50:20.058+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=48c68b16-c02b-40bf-becc-0036bb899b28, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59155] after 0.0 minutes of idle
2023-10-20T12:50:20.059+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:50:20.059+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:50:20.060+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:50:20.061+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:50:20.062+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:50:20.062+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:50:20.063+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:50:20.063+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=48c68b16-c02b-40bf-becc-0036bb899b28, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:50:20.064+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@19655761
2023-10-20T12:50:20.070+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:50:20.070+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@428ef416
2023-10-20T12:50:20.072+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:50:20.081+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:50:20.081+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 12444). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-12444.out.log
2023-10-20T12:50:20.085+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting build in new daemon [memory: 1.5 GiB]
2023-10-20T12:50:20.087+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-20T12:50:20.087+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-20T12:50:20.093+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:50:20.094+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=93eb786e-4b45-4237-b48f-b2aa4560f453,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=12444,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 11s
2 actionable tasks: 2 up-to-date
2023-10-20T12:50:28.678+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:50:28.695+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:50:28.695+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=48c68b16-c02b-40bf-becc-0036bb899b28, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59155]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:50:28.696+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=48c68b16-c02b-40bf-becc-0036bb899b28, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59155]
2023-10-20T12:50:28.696+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:50:28.696+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:50:28.696+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:50:28.703+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:50:28.703+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:50:28.705+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:50:28.706+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:50:28.706+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@3f37e55]
2023-10-20T12:50:28.706+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@3f37e55]
2023-10-20T12:50:28.707+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=48c68b16-c02b-40bf-becc-0036bb899b28, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:50:28.708+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:50:28.708+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@368cfd9f
2023-10-20T12:50:28.708+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@368cfd9f
2023-10-20T12:50:28.708+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:50:28.709+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:50:28.709+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 22: received null
2023-10-20T12:50:28.709+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 22: Received end-of-input from client.
2023-10-20T12:50:28.710+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:50:29.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:50:29.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:50:29.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:50:29.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:50:29.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:50:29.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:50:33.521+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59168 to /127.0.0.1:59154.
2023-10-20T12:50:33.524+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:50:33.525+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received non-IO message from client: Build{id=a51cb982-8cb9-42bd-ac57-c1b21f5a9c56, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:50:33.525+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=a51cb982-8cb9-42bd-ac57-c1b21f5a9c56, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:50:33.525+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=a51cb982-8cb9-42bd-ac57-c1b21f5a9c56, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59168.
2023-10-20T12:50:33.525+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=a51cb982-8cb9-42bd-ac57-c1b21f5a9c56, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59168] after 0.0 minutes of idle
2023-10-20T12:50:33.525+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:50:33.525+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:50:33.526+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:50:33.526+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:50:33.527+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:50:33.527+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:50:33.527+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:50:33.527+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=a51cb982-8cb9-42bd-ac57-c1b21f5a9c56, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:50:33.527+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 24: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@63d86a29
2023-10-20T12:50:33.528+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:50:33.528+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:50:33.529+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@1d5d1f1b
2023-10-20T12:50:33.529+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:50:33.529+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 12444). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-12444.out.log
2023-10-20T12:50:33.530+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 2nd build in daemon [uptime: 14.297 secs, performance: 99%]
2023-10-20T12:50:33.530+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:50:33.530+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=93eb786e-4b45-4237-b48f-b2aa4560f453,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=12444,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:50:33.530+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-20T12:50:33.530+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
package="com.schnell.xsmanager" found in source AndroidManifest.xml: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml.
Setting the namespace via a source AndroidManifest.xml's package attribute is deprecated.
Please instead set the namespace (or testNamespace) in the module's build.gradle file, as described here: https://developer.android.com/studio/build/configure-app-module#set-namespace
This migration can be done automatically using the AGP Upgrade Assistant, please refer to https://developer.android.com/studio/build/agp-upgrade-assistant for more information.
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:allowBackup was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:usesCleartextTraffic was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
Unable to make progress running work. The following items are queued for execution but none of them can be started:
  - Build ':':
      - Waiting for nodes:
          - producer locations for task group 0 (state=SHOULD_RUN, dependencies=NOT_COMPLETE, group=task group 0, no dependencies )
      - Ordinal groups:
          - group 0 entry nodes: [:xserp:clean (complete)]
          - group 1 entry nodes: [:xserp:assembleDevDebug (complete)]
  - Workers waiting for work: 8
  - Stopped workers: 0

FAILURE: Build completed with 3 failures.

1: Task failed with an exception.
-----------
* What went wrong:
Execution failed for task ':xserp:mapDevDebugSourceSetPaths'.
> Error while evaluating property 'extraGeneratedResDir' of task ':xserp:mapDevDebugSourceSetPaths'.
   > Failed to calculate the value of task ':xserp:mapDevDebugSourceSetPaths' property 'extraGeneratedResDir'.
      > Querying the mapped value of provider(java.util.Set) before task ':xserp:processDevDebugGoogleServices' has completed is not supported

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

2: Task failed with an exception.
-----------
* What went wrong:
Execution failed for task ':xserp:mergeDevDebugResources'.
> A failure occurred while executing com.android.build.gradle.internal.res.ResourceCompilerRunnable
   > Resource compilation failed (Failed to compile resource file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\res\layout\sign_up_screen.xml: . Cause: javax.xml.stream.XMLStreamException: ParseError at [row,col]:[2,6]
     Message: The processing instruction target matching "[xX][mM][lL]" is not allowed.). Check logs for more details.

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

3: Task failed with an exception.
-----------
* What went wrong:
Unable to make progress running work. There are items queued for execution but none of them can be started

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

* Get more help at https://help.gradle.org

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD FAILED in 34s
25 actionable tasks: 24 executed, 1 up-to-date
2023-10-20T12:51:07.966+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:51:07.982+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=a51cb982-8cb9-42bd-ac57-c1b21f5a9c56, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59168]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:51:07.983+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=a51cb982-8cb9-42bd-ac57-c1b21f5a9c56, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59168]
2023-10-20T12:51:07.983+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:51:07.983+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:51:07.983+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:51:07.983+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:51:07.984+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:07.985+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:07.985+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@4f304c94]
2023-10-20T12:51:07.985+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@4f304c94]
2023-10-20T12:51:07.985+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:51:07.992+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=a51cb982-8cb9-42bd-ac57-c1b21f5a9c56, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:51:08.005+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:51:08.006+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@139ae2c0
2023-10-20T12:51:08.006+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:51:08.006+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 140: received null
2023-10-20T12:51:08.006+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 140: Received end-of-input from client.
2023-10-20T12:51:08.006+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: null
2023-10-20T12:51:08.007+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:51:08.008+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: stopping connection
2023-10-20T12:51:09.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:09.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:09.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:09.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:09.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:09.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:19.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:19.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:19.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:29.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:29.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:39.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:39.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:39.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:39.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:39.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:39.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:39.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:49.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:49.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:49.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:49.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:49.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:49.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:59.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:59.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:59.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:51:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:51:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:51:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:09.930+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:09.931+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:09.931+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:09.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:09.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:09.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:09.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:09.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:09.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:19.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:29.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:29.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:29.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:29.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:29.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:29.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:29.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:29.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:29.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:49.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:49.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:49.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:49.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:49.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:49.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:49.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:49.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:49.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:59.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:59.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:59.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:52:59.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:52:59.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:52:59.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:09.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:09.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:09.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:09.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:09.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:09.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:09.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:09.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:09.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:29.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:29.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:29.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:39.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:39.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:39.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:39.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:39.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:39.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:49.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:49.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:49.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:49.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:49.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:49.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:49.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:49.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:49.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:59.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:53:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:53:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:53:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:02.821+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59235 to /127.0.0.1:59154.
2023-10-20T12:54:02.831+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 268: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:54:02.832+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 268: Received non-IO message from client: Build{id=d09df56b-f057-4dfc-b6c3-d0a5646a2237, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:54:02.832+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=d09df56b-f057-4dfc-b6c3-d0a5646a2237, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:54:02.832+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=d09df56b-f057-4dfc-b6c3-d0a5646a2237, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59235.
2023-10-20T12:54:02.832+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=d09df56b-f057-4dfc-b6c3-d0a5646a2237, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59235] after 2.0 minutes of idle
2023-10-20T12:54:02.832+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:54:02.832+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:54:02.833+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:54:02.833+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:02.834+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:02.834+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:54:02.834+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:54:02.834+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=d09df56b-f057-4dfc-b6c3-d0a5646a2237, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:54:02.834+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 270: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@146bd2f7
2023-10-20T12:54:02.835+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:54:02.835+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 268: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:54:02.835+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 268: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@174a4226
2023-10-20T12:54:02.836+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:54:02.836+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 12444). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-12444.out.log
2023-10-20T12:54:02.836+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 3rd build in daemon [uptime: 3 mins 43.608 secs, performance: 99%]
2023-10-20T12:54:02.836+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:54:02.836+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-20T12:54:02.836+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
2023-10-20T12:54:02.836+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=93eb786e-4b45-4237-b48f-b2aa4560f453,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=12444,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
Warning: This version only understands SDK XML versions up to 2 but an SDK XML file of version 3 was encountered. This can happen if you use versions of Android Studio and the command-line tools that were released at different times.
Warning: unexpected element (uri:"", local:"extension-level"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
Warning: unexpected element (uri:"", local:"base-extension"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 31s
2023-10-20T12:54:32.557+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:54:32.573+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:54:32.573+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=d09df56b-f057-4dfc-b6c3-d0a5646a2237, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59235]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:54:32.573+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=d09df56b-f057-4dfc-b6c3-d0a5646a2237, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59235]
2023-10-20T12:54:32.573+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:54:32.573+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:54:32.573+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:54:32.574+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:54:32.574+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:32.575+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:32.576+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@5f6b097a]
2023-10-20T12:54:32.576+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@5f6b097a]
2023-10-20T12:54:32.576+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:54:32.578+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=d09df56b-f057-4dfc-b6c3-d0a5646a2237, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:54:32.586+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 268: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:54:32.586+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 268: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@3dc50e1e
2023-10-20T12:54:32.586+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:54:32.586+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 268: received null
2023-10-20T12:54:32.587+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 268: Received end-of-input from client.
2023-10-20T12:54:32.587+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@3dc50e1e
2023-10-20T12:54:32.588+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: stopping connection
2023-10-20T12:54:32.589+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: stopping connection
2023-10-20T12:54:39.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:54:39.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:39.957+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:39.959+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:54:39.960+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:39.960+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:49.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:54:49.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:49.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:54:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:49.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:54:49.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:49.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:54:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:59.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:54:59.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:59.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:54:59.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:54:59.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:54:59.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:55:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:55:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:09.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:55:09.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:55:09.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:09.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:55:09.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:55:09.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:15.629+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59267 to /127.0.0.1:59154.
2023-10-20T12:55:15.632+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 370: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:55:15.633+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 370: Received non-IO message from client: Build{id=f3a1d74c-59d0-4f74-97d5-ff5000348321, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:55:15.633+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=f3a1d74c-59d0-4f74-97d5-ff5000348321, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:55:15.633+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=f3a1d74c-59d0-4f74-97d5-ff5000348321, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59267.
2023-10-20T12:55:15.633+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=f3a1d74c-59d0-4f74-97d5-ff5000348321, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59267] after 0.0 minutes of idle
2023-10-20T12:55:15.633+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:55:15.633+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:55:15.633+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:55:15.634+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:55:15.634+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:15.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:55:15.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:55:15.635+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=f3a1d74c-59d0-4f74-97d5-ff5000348321, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:55:15.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 270: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@13361ed8
2023-10-20T12:55:15.635+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:55:15.636+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 370: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:55:15.636+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 370: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@6285a1fb
2023-10-20T12:55:15.636+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:55:15.636+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 12444). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-12444.out.log
2023-10-20T12:55:15.637+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 4th build in daemon [uptime: 4 mins 56.404 secs, performance: 100%]
2023-10-20T12:55:15.637+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:55:15.637+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=93eb786e-4b45-4237-b48f-b2aa4560f453,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=12444,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:55:15.637+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-20T12:55:15.637+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 561ms
2 actionable tasks: 1 executed, 1 up-to-date
2023-10-20T12:55:16.214+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:55:16.232+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:55:16.232+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=f3a1d74c-59d0-4f74-97d5-ff5000348321, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59267]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:55:16.232+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=f3a1d74c-59d0-4f74-97d5-ff5000348321, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59267]
2023-10-20T12:55:16.233+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:55:16.233+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:55:16.233+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:55:16.242+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:55:16.243+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:55:16.255+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:16.256+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@6d04f2fc]
2023-10-20T12:55:16.256+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@6d04f2fc]
2023-10-20T12:55:16.256+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:55:16.257+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=f3a1d74c-59d0-4f74-97d5-ff5000348321, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:55:16.257+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 370: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:55:16.257+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 370: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@6c4253db
2023-10-20T12:55:16.257+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@6c4253db
2023-10-20T12:55:16.257+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:55:16.258+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 370: received null
2023-10-20T12:55:16.258+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 370: Received end-of-input from client.
2023-10-20T12:55:16.258+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: stopping connection
2023-10-20T12:55:16.259+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: stopping connection
2023-10-20T12:55:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:55:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:55:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:55:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:55:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:21.836+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59270 to /127.0.0.1:59154.
2023-10-20T12:55:21.839+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 471: received class org.gradle.launcher.daemon.protocol.Build
2023-10-20T12:55:21.839+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 471: Received non-IO message from client: Build{id=27372022-513c-4532-8127-1890dbde1480, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:55:21.839+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=27372022-513c-4532-8127-1890dbde1480, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}.
2023-10-20T12:55:21.839+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=27372022-513c-4532-8127-1890dbde1480, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage} with connection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59270.
2023-10-20T12:55:21.839+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=27372022-513c-4532-8127-1890dbde1480, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59270] after 0.0 minutes of idle
2023-10-20T12:55:21.839+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:55:21.839+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:55:21.840+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:55:21.840+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:55:21.841+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:55:21.841+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:55:21.841+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:55:21.841+0530 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=27372022-513c-4532-8127-1890dbde1480, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}. Dispatching build started information...
2023-10-20T12:55:21.841+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 270: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@5482d966
2023-10-20T12:55:21.842+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [USERDOMAIN_ROAMINGPROFILE, IGCCSVC_DB, LOCALAPPDATA, PROCESSOR_LEVEL, USERDOMAIN, LOGONSERVER, FPS_BROWSER_APP_PROFILE_STRING, SESSIONNAME, ALLUSERSPROFILE, PROCESSOR_ARCHITECTURE, PSModulePath, SystemDrive, OneDrive, APPDATA, USERNAME, ProgramFiles(x86), CommonProgramFiles, Path, FPS_BROWSER_USER_PROFILE_STRING, PATHEXT, EFC_19440, DriverData, OS, COMPUTERNAME, IntelliJ IDEA Community Edition, PROCESSOR_REVISION, CommonProgramW6432, ComSpec, ProgramData, ProgramW6432, TEMP, HOMEPATH, SystemRoot, PROCESSOR_IDENTIFIER, HOMEDRIVE, USERPROFILE, TMP, CommonProgramFiles(x86), ProgramFiles, PUBLIC, windir, NUMBER_OF_PROCESSORS, =::, IDEA_INITIAL_DIRECTORY, ZES_ENABLE_SYSMAN]
2023-10-20T12:55:21.842+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 471: received class org.gradle.launcher.daemon.protocol.CloseInput
2023-10-20T12:55:21.842+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 471: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@2d132ec2
2023-10-20T12:55:21.842+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2023-10-20T12:55:21.842+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 12444). The daemon log file: D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon\7.6\daemon-12444.out.log
2023-10-20T12:55:21.843+0530 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting 5th build in daemon [uptime: 5 mins 2.61 secs, performance: 100%]
2023-10-20T12:55:21.843+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2023-10-20T12:55:21.843+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=93eb786e-4b45-4237-b48f-b2aa4560f453,javaHome=C:\Users\<USER>\AppData\Local\Programs\Android Studio\jbr,daemonRegistryDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\daemon,pid=12444,idleTimeout=10800000,priority=NORMAL,daemonOpts=--add-opens=java.base/java.util=ALL-UNNAMED,--add-opens=java.base/java.lang=ALL-UNNAMED,--add-opens=java.base/java.lang.invoke=ALL-UNNAMED,--add-opens=java.prefs/java.util.prefs=ALL-UNNAMED,--add-opens=java.base/java.nio.charset=ALL-UNNAMED,--add-opens=java.base/java.net=ALL-UNNAMED,--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED,-Xmx1536m,-Dfile.encoding=windows-1252,-Duser.country=IN,-Duser.language=en,-Duser.variant]
2023-10-20T12:55:21.843+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] Closing daemon's stdin at end of input.
2023-10-20T12:55:21.843+0530 [INFO] [org.gradle.launcher.daemon.server.exec.ForwardClientInput] The daemon will no longer process any standard input.
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
Warning: unexpected element (uri:"", local:"extension-level"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
Warning: unexpected element (uri:"", local:"base-extension"). Expected elements are <{}codename>,<{}layoutlib>,<{}api-level>
AGPBI: {"kind":"warning","text":"We recommend using a newer Android Gradle plugin to use compileSdk = 33\n\nThis Android Gradle plugin (7.0.4) was tested up to compileSdk = 31\n\nThis warning can be suppressed by adding\n    android.suppressUnsupportedCompileSdk=33\nto this project's gradle.properties\n\nThe build will continue, but you are strongly encouraged to update your project to\nuse a newer Android Gradle Plugin that has been tested with compileSdk = 33","sources":[{}]}
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:allowBackup was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\AndroidManifest.xml:13:5-165:19 Warning:
	application@android:usesCleartextTraffic was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present
Unable to make progress running work. The following items are queued for execution but none of them can be started:
  - Build ':':
      - Waiting for nodes:
          - producer locations for task group 0 (state=SHOULD_RUN, dependencies=NOT_COMPLETE, group=task group 0, no dependencies )
      - Ordinal groups:
          - group 0 entry nodes: [:xserp:clean (complete)]
          - group 1 entry nodes: [:xserp:assembleDevDebug (complete)]
  - Workers waiting for work: 8
  - Stopped workers: 0

FAILURE: Build completed with 2 failures.

1: Task failed with an exception.
-----------
* What went wrong:
Execution failed for task ':xserp:mergeDevDebugResources'.
> A failure occurred while executing com.android.build.gradle.internal.res.ResourceCompilerRunnable
   > Resource compilation failed. Check logs for details.

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

2: Task failed with an exception.
-----------
* What went wrong:
Unable to make progress running work. There are items queued for execution but none of them can be started

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

* Get more help at https://help.gradle.org

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.6/userguide/command_line_interface.html#sec:command_line_warnings

BUILD FAILED in 1m 3s
23 actionable tasks: 22 executed, 1 up-to-date
2023-10-20T12:56:25.099+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2023-10-20T12:56:25.116+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:56:25.116+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=27372022-513c-4532-8127-1890dbde1480, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59270]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@6f929656 with state Busy
2023-10-20T12:56:25.116+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=27372022-513c-4532-8127-1890dbde1480, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59270]
2023-10-20T12:56:25.116+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2023-10-20T12:56:25.116+0530 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:56:25.116+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T12:56:25.116+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T12:56:25.117+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:25.117+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:25.118+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2023-10-20T12:56:25.118+0530 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@431b86ec]
2023-10-20T12:56:25.118+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@431b86ec]
2023-10-20T12:56:25.119+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=27372022-513c-4532-8127-1890dbde1480, currentDir=D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage}
2023-10-20T12:56:25.121+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 471: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:56:25.121+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 471: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@23d4d510
2023-10-20T12:56:25.121+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@23d4d510
2023-10-20T12:56:25.121+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:56:25.121+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 471: received null
2023-10-20T12:56:25.121+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 471: Received end-of-input from client.
2023-10-20T12:56:25.121+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: stopping connection
2023-10-20T12:56:25.122+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 267: stopping connection
2023-10-20T12:56:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:29.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:29.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:29.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:39.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:39.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:39.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:39.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:39.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:39.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:39.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:49.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:49.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:49.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:59.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:59.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:59.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:59.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:56:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:56:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:56:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:09.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:09.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:09.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:09.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:09.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:09.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:09.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:29.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:29.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:29.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:29.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:29.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:39.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:39.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:39.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:49.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:49.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:49.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:49.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:49.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:49.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:49.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:49.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:49.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:57:59.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:57:59.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:57:59.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:09.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:09.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:09.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:09.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:19.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:19.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:19.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:19.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:19.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:19.951+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:29.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:29.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:29.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:29.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:29.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:29.949+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:39.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:39.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:39.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:39.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:39.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:39.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:39.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:39.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:48.616+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59348 to /127.0.0.1:59154.
2023-10-20T12:58:48.621+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 594: received class org.gradle.launcher.daemon.protocol.InvalidateVirtualFileSystem
2023-10-20T12:58:48.621+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 594: Received non-IO message from client: InvalidateVirtualFileSystem[id=b8bbd1ef-2b11-482a-ab04-a74ae2375fbf]
2023-10-20T12:58:48.621+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: InvalidateVirtualFileSystem[id=b8bbd1ef-2b11-482a-ab04-a74ae2375fbf].
2023-10-20T12:58:48.621+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: InvalidateVirtualFileSystem[id=b8bbd1ef-2b11-482a-ab04-a74ae2375fbf] with connection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59348.
2023-10-20T12:58:48.622+0530 [INFO] [org.gradle.launcher.daemon.server.api.HandleInvalidateVirtualFileSystem] Invalidating [D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\gradle.properties]
2023-10-20T12:58:48.623+0530 [DEBUG] [org.gradle.internal.vfs.impl.AbstractVirtualFileSystem] Invalidating VFS paths: [D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\gradle.properties]
2023-10-20T12:58:48.623+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 593: dispatching Success[value=null]
2023-10-20T12:58:48.623+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: InvalidateVirtualFileSystem[id=b8bbd1ef-2b11-482a-ab04-a74ae2375fbf]
2023-10-20T12:58:48.623+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 594: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T12:58:48.623+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 594: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@5325619d
2023-10-20T12:58:48.623+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@5325619d
2023-10-20T12:58:48.623+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 593: stopping connection
2023-10-20T12:58:48.624+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T12:58:48.624+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 594: received null
2023-10-20T12:58:48.624+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 594: Received end-of-input from client.
2023-10-20T12:58:48.624+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 593: stopping connection
2023-10-20T12:58:49.866+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Lock on journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1) requested by another process - releasing lock.
2023-10-20T12:58:49.866+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1)
2023-10-20T12:58:49.866+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache file-access.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1\file-access.bin)
2023-10-20T12:58:49.866+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on journal cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\journal-1).
2023-10-20T12:58:49.866+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id -419903189589738871.
2023-10-20T12:58:49.866+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id -419903189589738871.
2023-10-20T12:58:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:49.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:49.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:49.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:49.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:49.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:49.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:49.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:50.022+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Lock on file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes) requested by another process - releasing lock.
2023-10-20T12:58:50.022+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes)
2023-10-20T12:58:50.022+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache fileHashes.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes\fileHashes.bin)
2023-10-20T12:58:50.023+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache resourceHashesCache.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes\resourceHashesCache.bin)
2023-10-20T12:58:50.023+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file hash cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileHashes).
2023-10-20T12:58:50.023+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 4248139211808496878.
2023-10-20T12:58:50.023+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 4248139211808496878.
2023-10-20T12:58:50.499+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Lock on jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) requested by another process - releasing lock.
2023-10-20T12:58:50.499+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9)
2023-10-20T12:58:50.499+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9).
2023-10-20T12:58:50.499+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id -1871856463948822579.
2023-10-20T12:58:50.499+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id -1871856463948822579.
2023-10-20T12:58:50.811+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Lock on file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent) requested by another process - releasing lock.
2023-10-20T12:58:50.811+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent)
2023-10-20T12:58:50.811+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file content cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\fileContent).
2023-10-20T12:58:50.811+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 5361796738745149802.
2023-10-20T12:58:50.811+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 5361796738745149802.
2023-10-20T12:58:50.969+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Lock on execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory) requested by another process - releasing lock.
2023-10-20T12:58:50.969+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory)
2023-10-20T12:58:50.969+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache executionHistory.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory\executionHistory.bin)
2023-10-20T12:58:50.970+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on execution history cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\executionHistory).
2023-10-20T12:58:50.970+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 3734459944631640681.
2023-10-20T12:58:50.970+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 3734459944631640681.
2023-10-20T12:58:52.604+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Lock on artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) requested by another process - releasing lock.
2023-10-20T12:58:52.604+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2)
2023-10-20T12:58:52.604+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-metadata.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-metadata.bin)
2023-10-20T12:58:52.604+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifacts.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-artifacts.bin)
2023-10-20T12:58:52.604+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache resource-at-url.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\resource-at-url.bin)
2023-10-20T12:58:52.604+0530 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifact.bin (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2\metadata-2.99\module-artifact.bin)
2023-10-20T12:58:52.604+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2).
2023-10-20T12:58:52.605+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 9047423567431008548.
2023-10-20T12:58:52.605+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 9047423567431008548.
2023-10-20T12:58:58.007+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Lock on Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3) requested by another process - releasing lock.
2023-10-20T12:58:58.007+0530 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3)
2023-10-20T12:58:58.007+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3).
2023-10-20T12:58:58.007+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 1447503845491462777.
2023-10-20T12:58:58.008+0530 [DEBUG] [org.gradle.cache.internal.locklistener.FileLockCommunicator] Confirming lock release to Gradle process at port 51646 for lock with id 1447503845491462777.
2023-10-20T12:58:59.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:59.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:59.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:59.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:59.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:59.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:58:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:58:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:58:59.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:09.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:09.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:09.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:09.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:19.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:19.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:19.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:29.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:29.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:29.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:29.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:29.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:29.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:39.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:39.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:39.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:39.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:39.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:39.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:39.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:49.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:49.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:49.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:49.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:49.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:49.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:49.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:49.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:49.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:50.258+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\google-services\dev\debug\values\values.xml
2023-10-20T12:59:50.263+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\google-services\dev\debug\values
2023-10-20T12:59:50.263+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\google-services\dev\debug
2023-10-20T12:59:50.263+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-anydpi-v24\circle.xml
2023-10-20T12:59:50.263+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-anydpi-v24
2023-10-20T12:59:50.263+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-hdpi\circle.png
2023-10-20T12:59:50.263+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-hdpi
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-ldpi\circle.png
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-ldpi
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-mdpi\circle.png
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-mdpi
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-xhdpi\circle.png
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-xhdpi
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-xxhdpi\circle.png
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-xxhdpi
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-xxxhdpi\circle.png
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug\drawable-xxxhdpi
2023-10-20T12:59:50.264+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\pngs\dev\debug
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res\resValues\dev\debug
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\res
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\source\buildConfig\dev\debug\com\schnell\xsmanager\BuildConfig.java
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\source\buildConfig\dev\debug\com\schnell\xsmanager
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\source\buildConfig\dev\debug\com\schnell
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\source\buildConfig\dev\debug\com
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated\source\buildConfig\dev\debug
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\generated
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\aar_metadata_check\devDebug
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\annotation_processor_list\devDebug\annotationProcessors.json
2023-10-20T12:59:50.265+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\app_metadata\devDebug\app-metadata.properties
2023-10-20T12:59:50.267+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\compatible_screen_manifest\devDebug\output-metadata.json
2023-10-20T12:59:50.268+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\compatible_screen_manifest\devDebug
2023-10-20T12:59:50.269+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\compressed_assets\devDebug\out
2023-10-20T12:59:50.269+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\data_binding_layout_info_type_merge\devDebug\out
2023-10-20T12:59:50.270+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeExtDexDevDebug\classes.dex
2023-10-20T12:59:50.272+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeExtDexDevDebug\classes2.dex
2023-10-20T12:59:50.273+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeExtDexDevDebug
2023-10-20T12:59:50.273+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\0
2023-10-20T12:59:50.274+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\1
2023-10-20T12:59:50.274+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\10
2023-10-20T12:59:50.274+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\11
2023-10-20T12:59:50.274+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\12
2023-10-20T12:59:50.274+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\13
2023-10-20T12:59:50.274+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\14
2023-10-20T12:59:50.274+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\15
2023-10-20T12:59:50.275+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\2
2023-10-20T12:59:50.275+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\3
2023-10-20T12:59:50.275+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\4
2023-10-20T12:59:50.275+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\5
2023-10-20T12:59:50.275+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\6
2023-10-20T12:59:50.275+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\7
2023-10-20T12:59:50.275+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\8
2023-10-20T12:59:50.275+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug\9
2023-10-20T12:59:50.276+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug\mergeLibDexDevDebug
2023-10-20T12:59:50.276+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\dex\devDebug
2023-10-20T12:59:50.277+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\duplicate_classes_check\devDebug
2023-10-20T12:59:50.277+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\external_file_lib_dex_archives\devDebug
2023-10-20T12:59:50.277+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugAssets\merger.xml
2023-10-20T12:59:50.278+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugAssets
2023-10-20T12:59:50.278+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugJniLibFolders\merger.xml
2023-10-20T12:59:50.279+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugJniLibFolders
2023-10-20T12:59:50.279+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\compile-file-map.properties
2023-10-20T12:59:50.279+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values\values.xml
2023-10-20T12:59:50.280+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values
2023-10-20T12:59:50.280+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-af\values-af.xml
2023-10-20T12:59:50.280+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-af
2023-10-20T12:59:50.281+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-am\values-am.xml
2023-10-20T12:59:50.281+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-am
2023-10-20T12:59:50.281+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ar\values-ar.xml
2023-10-20T12:59:50.282+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ar
2023-10-20T12:59:50.282+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-as\values-as.xml
2023-10-20T12:59:50.282+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-as
2023-10-20T12:59:50.282+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-az\values-az.xml
2023-10-20T12:59:50.283+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-az
2023-10-20T12:59:50.283+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-b+es+419\values-b+es+419.xml
2023-10-20T12:59:50.283+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-b+es+419
2023-10-20T12:59:50.284+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-b+sr+Latn\values-b+sr+Latn.xml
2023-10-20T12:59:50.284+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-b+sr+Latn
2023-10-20T12:59:50.285+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-be\values-be.xml
2023-10-20T12:59:50.285+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-be
2023-10-20T12:59:50.285+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-bg\values-bg.xml
2023-10-20T12:59:50.286+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-bg
2023-10-20T12:59:50.286+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-bn\values-bn.xml
2023-10-20T12:59:50.287+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-bn
2023-10-20T12:59:50.287+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-bs\values-bs.xml
2023-10-20T12:59:50.288+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-bs
2023-10-20T12:59:50.288+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ca\values-ca.xml
2023-10-20T12:59:50.288+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ca
2023-10-20T12:59:50.289+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-cs\values-cs.xml
2023-10-20T12:59:50.291+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-cs
2023-10-20T12:59:50.291+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-da\values-da.xml
2023-10-20T12:59:50.291+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-da
2023-10-20T12:59:50.291+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-de\values-de.xml
2023-10-20T12:59:50.292+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-de
2023-10-20T12:59:50.293+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-el\values-el.xml
2023-10-20T12:59:50.293+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-el
2023-10-20T12:59:50.293+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rAU\values-en-rAU.xml
2023-10-20T12:59:50.294+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rAU
2023-10-20T12:59:50.294+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rCA\values-en-rCA.xml
2023-10-20T12:59:50.295+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rCA
2023-10-20T12:59:50.295+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rGB\values-en-rGB.xml
2023-10-20T12:59:50.296+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rGB
2023-10-20T12:59:50.296+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rIN\values-en-rIN.xml
2023-10-20T12:59:50.296+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rIN
2023-10-20T12:59:50.296+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rXC\values-en-rXC.xml
2023-10-20T12:59:50.297+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-en-rXC
2023-10-20T12:59:50.297+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-es\values-es.xml
2023-10-20T12:59:50.297+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-es
2023-10-20T12:59:50.297+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-es-rUS\values-es-rUS.xml
2023-10-20T12:59:50.298+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-es-rUS
2023-10-20T12:59:50.298+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-et\values-et.xml
2023-10-20T12:59:50.298+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-et
2023-10-20T12:59:50.298+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-eu\values-eu.xml
2023-10-20T12:59:50.299+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-eu
2023-10-20T12:59:50.299+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-fa\values-fa.xml
2023-10-20T12:59:50.299+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-fa
2023-10-20T12:59:50.299+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-fi\values-fi.xml
2023-10-20T12:59:50.300+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-fi
2023-10-20T12:59:50.300+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-fr\values-fr.xml
2023-10-20T12:59:50.300+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-fr
2023-10-20T12:59:50.301+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-fr-rCA\values-fr-rCA.xml
2023-10-20T12:59:50.301+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-fr-rCA
2023-10-20T12:59:50.301+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-gl\values-gl.xml
2023-10-20T12:59:50.302+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-gl
2023-10-20T12:59:50.302+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-gu\values-gu.xml
2023-10-20T12:59:50.302+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-gu
2023-10-20T12:59:50.303+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h320dp-port-v13\values-h320dp-port-v13.xml
2023-10-20T12:59:50.304+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h320dp-port-v13
2023-10-20T12:59:50.304+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h360dp-land-v13\values-h360dp-land-v13.xml
2023-10-20T12:59:50.304+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h360dp-land-v13
2023-10-20T12:59:50.305+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h480dp-land-v13\values-h480dp-land-v13.xml
2023-10-20T12:59:50.305+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h480dp-land-v13
2023-10-20T12:59:50.305+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h550dp-port-v13\values-h550dp-port-v13.xml
2023-10-20T12:59:50.305+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h550dp-port-v13
2023-10-20T12:59:50.305+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h720dp-v13\values-h720dp-v13.xml
2023-10-20T12:59:50.306+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-h720dp-v13
2023-10-20T12:59:50.308+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hdpi-v4\values-hdpi-v4.xml
2023-10-20T12:59:50.313+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hdpi-v4
2023-10-20T12:59:50.313+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hi\values-hi.xml
2023-10-20T12:59:50.313+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hi
2023-10-20T12:59:50.313+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hr\values-hr.xml
2023-10-20T12:59:50.314+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hr
2023-10-20T12:59:50.314+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hu\values-hu.xml
2023-10-20T12:59:50.315+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hu
2023-10-20T12:59:50.315+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hy\values-hy.xml
2023-10-20T12:59:50.315+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-hy
2023-10-20T12:59:50.315+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-in\values-in.xml
2023-10-20T12:59:50.315+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-in
2023-10-20T12:59:50.316+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-is\values-is.xml
2023-10-20T12:59:50.316+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-is
2023-10-20T12:59:50.316+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-it\values-it.xml
2023-10-20T12:59:50.316+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-it
2023-10-20T12:59:50.316+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-iw\values-iw.xml
2023-10-20T12:59:50.316+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-iw
2023-10-20T12:59:50.316+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ja\values-ja.xml
2023-10-20T12:59:50.317+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ja
2023-10-20T12:59:50.317+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ka\values-ka.xml
2023-10-20T12:59:50.317+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ka
2023-10-20T12:59:50.317+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-kk\values-kk.xml
2023-10-20T12:59:50.318+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-kk
2023-10-20T12:59:50.318+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-km\values-km.xml
2023-10-20T12:59:50.319+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-km
2023-10-20T12:59:50.319+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-kn\values-kn.xml
2023-10-20T12:59:50.320+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-kn
2023-10-20T12:59:50.321+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ko\values-ko.xml
2023-10-20T12:59:50.321+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ko
2023-10-20T12:59:50.321+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ky\values-ky.xml
2023-10-20T12:59:50.322+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ky
2023-10-20T12:59:50.322+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-land\values-land.xml
2023-10-20T12:59:50.322+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-land
2023-10-20T12:59:50.322+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-large-v4\values-large-v4.xml
2023-10-20T12:59:50.322+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-large-v4
2023-10-20T12:59:50.323+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ldltr-v21\values-ldltr-v21.xml
2023-10-20T12:59:50.323+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ldltr-v21
2023-10-20T12:59:50.323+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ldrtl-v17\values-ldrtl-v17.xml
2023-10-20T12:59:50.323+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ldrtl-v17
2023-10-20T12:59:50.323+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-lo\values-lo.xml
2023-10-20T12:59:50.324+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-lo
2023-10-20T12:59:50.324+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-lt\values-lt.xml
2023-10-20T12:59:50.324+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-lt
2023-10-20T12:59:50.324+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-lv\values-lv.xml
2023-10-20T12:59:50.324+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-lv
2023-10-20T12:59:50.324+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-mdpi-v4\values-mdpi-v4.xml
2023-10-20T12:59:50.325+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-mdpi-v4
2023-10-20T12:59:50.325+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-mk\values-mk.xml
2023-10-20T12:59:50.325+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-mk
2023-10-20T12:59:50.325+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ml\values-ml.xml
2023-10-20T12:59:50.325+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ml
2023-10-20T12:59:50.325+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-mn\values-mn.xml
2023-10-20T12:59:50.325+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-mn
2023-10-20T12:59:50.326+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-mr\values-mr.xml
2023-10-20T12:59:50.326+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-mr
2023-10-20T12:59:50.326+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ms\values-ms.xml
2023-10-20T12:59:50.326+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ms
2023-10-20T12:59:50.327+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-my\values-my.xml
2023-10-20T12:59:50.327+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-my
2023-10-20T12:59:50.327+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-nb\values-nb.xml
2023-10-20T12:59:50.327+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-nb
2023-10-20T12:59:50.327+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ne\values-ne.xml
2023-10-20T12:59:50.327+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ne
2023-10-20T12:59:50.328+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-night-v8\values-night-v8.xml
2023-10-20T12:59:50.328+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-night-v8
2023-10-20T12:59:50.328+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-nl\values-nl.xml
2023-10-20T12:59:50.328+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-nl
2023-10-20T12:59:50.328+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-or\values-or.xml
2023-10-20T12:59:50.328+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-or
2023-10-20T12:59:50.328+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pa\values-pa.xml
2023-10-20T12:59:50.329+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pa
2023-10-20T12:59:50.329+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pl\values-pl.xml
2023-10-20T12:59:50.329+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pl
2023-10-20T12:59:50.329+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-port\values-port.xml
2023-10-20T12:59:50.330+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-port
2023-10-20T12:59:50.330+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pt\values-pt.xml
2023-10-20T12:59:50.331+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pt
2023-10-20T12:59:50.331+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pt-rBR\values-pt-rBR.xml
2023-10-20T12:59:50.332+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pt-rBR
2023-10-20T12:59:50.332+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pt-rPT\values-pt-rPT.xml
2023-10-20T12:59:50.332+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-pt-rPT
2023-10-20T12:59:50.332+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ro\values-ro.xml
2023-10-20T12:59:50.333+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ro
2023-10-20T12:59:50.333+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ru\values-ru.xml
2023-10-20T12:59:50.333+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ru
2023-10-20T12:59:50.333+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-si\values-si.xml
2023-10-20T12:59:50.334+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-si
2023-10-20T12:59:50.334+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sk\values-sk.xml
2023-10-20T12:59:50.334+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sk
2023-10-20T12:59:50.335+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sl\values-sl.xml
2023-10-20T12:59:50.335+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sl
2023-10-20T12:59:50.335+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-small-v4\values-small-v4.xml
2023-10-20T12:59:50.336+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-small-v4
2023-10-20T12:59:50.336+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sq\values-sq.xml
2023-10-20T12:59:50.337+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sq
2023-10-20T12:59:50.337+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sr\values-sr.xml
2023-10-20T12:59:50.338+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sr
2023-10-20T12:59:50.338+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sv\values-sv.xml
2023-10-20T12:59:50.338+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sv
2023-10-20T12:59:50.339+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sw\values-sw.xml
2023-10-20T12:59:50.339+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sw
2023-10-20T12:59:50.339+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sw600dp-v13\values-sw600dp-v13.xml
2023-10-20T12:59:50.340+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-sw600dp-v13
2023-10-20T12:59:50.340+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ta\values-ta.xml
2023-10-20T12:59:50.341+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ta
2023-10-20T12:59:50.341+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-te\values-te.xml
2023-10-20T12:59:50.341+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-te
2023-10-20T12:59:50.342+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-th\values-th.xml
2023-10-20T12:59:50.342+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-th
2023-10-20T12:59:50.353+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-tl\values-tl.xml
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-tl
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-tr\values-tr.xml
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-tr
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-uk\values-uk.xml
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-uk
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ur\values-ur.xml
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-ur
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-uz\values-uz.xml
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-uz
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v16\values-v16.xml
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v16
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v17\values-v17.xml
2023-10-20T12:59:50.354+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v17
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v18\values-v18.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v18
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v21\values-v21.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v21
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v22\values-v22.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v22
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v23\values-v23.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v23
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v24\values-v24.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v24
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v25\values-v25.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v25
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v26\values-v26.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v26
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v28\values-v28.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v28
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v31\values-v31.xml
2023-10-20T12:59:50.355+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-v31
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-vi\values-vi.xml
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-vi
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-w320dp-land-v13\values-w320dp-land-v13.xml
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-w320dp-land-v13
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-w360dp-port-v13\values-w360dp-port-v13.xml
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-w360dp-port-v13
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-w480dp-port-v13\values-w480dp-port-v13.xml
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-w480dp-port-v13
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-w600dp-land-v13\values-w600dp-land-v13.xml
2023-10-20T12:59:50.356+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-w600dp-land-v13
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-watch-v20\values-watch-v20.xml
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-watch-v20
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-watch-v21\values-watch-v21.xml
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-watch-v21
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-xhdpi-v4\values-xhdpi-v4.xml
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-xhdpi-v4
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-xlarge-v4\values-xlarge-v4.xml
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-xlarge-v4
2023-10-20T12:59:50.357+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-xxhdpi-v4\values-xxhdpi-v4.xml
2023-10-20T12:59:50.358+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-xxhdpi-v4
2023-10-20T12:59:50.359+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-xxxhdpi-v4\values-xxxhdpi-v4.xml
2023-10-20T12:59:50.360+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-xxxhdpi-v4
2023-10-20T12:59:50.363+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-zh-rCN\values-zh-rCN.xml
2023-10-20T12:59:50.364+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-zh-rCN
2023-10-20T12:59:50.364+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-zh-rHK\values-zh-rHK.xml
2023-10-20T12:59:50.364+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-zh-rHK
2023-10-20T12:59:50.364+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-zh-rTW\values-zh-rTW.xml
2023-10-20T12:59:50.364+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-zh-rTW
2023-10-20T12:59:50.365+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-zu\values-zu.xml
2023-10-20T12:59:50.366+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir\values-zu
2023-10-20T12:59:50.366+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merged.dir
2023-10-20T12:59:50.366+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\merger.xml
2023-10-20T12:59:50.366+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources\stripped.dir
2023-10-20T12:59:50.366+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugResources
2023-10-20T12:59:50.366+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugShaders\merger.xml
2023-10-20T12:59:50.367+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental\mergeDevDebugShaders
2023-10-20T12:59:50.367+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\incremental
2023-10-20T12:59:50.367+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\manifest_merge_blame_file\devDebug\manifest-merger-blame-dev-debug-report.txt
2023-10-20T12:59:50.367+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_assets\devDebug\out
2023-10-20T12:59:50.367+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_jni_libs\devDebug\out
2023-10-20T12:59:50.368+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_manifest\devDebug\AndroidManifest.xml
2023-10-20T12:59:50.368+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_manifests\devDebug\AndroidManifest.xml
2023-10-20T12:59:50.368+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_manifests\devDebug\output-metadata.json
2023-10-20T12:59:50.368+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_manifests\devDebug
2023-10-20T12:59:50.369+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-anydpi-v24_circle.xml.flat
2023-10-20T12:59:50.369+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-hdpi_circle.png.flat
2023-10-20T12:59:50.369+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-hdpi_custom_checkbox.xml.flat
2023-10-20T12:59:50.369+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-hdpi_ic_stat_name.png.flat
2023-10-20T12:59:50.369+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-ldpi_circle.png.flat
2023-10-20T12:59:50.369+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-mdpi_circle.png.flat
2023-10-20T12:59:50.370+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-xhdpi_circle.png.flat
2023-10-20T12:59:50.370+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-xhdpi_circle_indicator.xml.flat
2023-10-20T12:59:50.370+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-xhdpi_ic_stat_name.png.flat
2023-10-20T12:59:50.370+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-xxhdpi_circle.png.flat
2023-10-20T12:59:50.370+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-xxhdpi_ic_stat_name.png.flat
2023-10-20T12:59:50.370+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable-xxxhdpi_circle.png.flat
2023-10-20T12:59:50.371+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_bell.xml.flat
2023-10-20T12:59:50.371+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_border_shape_blue.xml.flat
2023-10-20T12:59:50.371+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_chart_marker.9.png.flat
2023-10-20T12:59:50.371+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_chevron_down_circle_outline.xml.flat
2023-10-20T12:59:50.371+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_close_circle_outline.xml.flat
2023-10-20T12:59:50.371+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_cloud_search.xml.flat
2023-10-20T12:59:50.371+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_content_save.xml.flat
2023-10-20T12:59:50.371+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_filter.xml.flat
2023-10-20T12:59:50.372+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_filter_primary.xml.flat
2023-10-20T12:59:50.372+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_grey_corner_bg.xml.flat
2023-10-20T12:59:50.372+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_icon_badge.xml.flat
2023-10-20T12:59:50.372+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_ic_baseline_radio_button_unchecked_24.xml.flat
2023-10-20T12:59:50.372+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_ic_close_baseline.xml.flat
2023-10-20T12:59:50.372+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_ic_maintenance_icon.xml.flat
2023-10-20T12:59:50.372+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_ic_server_error.xml.flat
2023-10-20T12:59:50.373+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_magnify.xml.flat
2023-10-20T12:59:50.373+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_menu_down.xml.flat
2023-10-20T12:59:50.373+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_menu_down_blue.xml.flat
2023-10-20T12:59:50.373+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_menu_down_outline.xml.flat
2023-10-20T12:59:50.373+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_menu_up.xml.flat
2023-10-20T12:59:50.373+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_menu_up_blue.xml.flat
2023-10-20T12:59:50.373+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_pencil.xml.flat
2023-10-20T12:59:50.373+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_plus.xml.flat
2023-10-20T12:59:50.374+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_primary_button.xml.flat
2023-10-20T12:59:50.374+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_primary_text_box.xml.flat
2023-10-20T12:59:50.374+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_primary_text_box_light.xml.flat
2023-10-20T12:59:50.374+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_receipt.xml.flat
2023-10-20T12:59:50.374+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_rect_border.xml.flat
2023-10-20T12:59:50.374+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_refresh.xml.flat
2023-10-20T12:59:50.374+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_ripple_circle.xml.flat
2023-10-20T12:59:50.374+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_server_not_found_error.xml.flat
2023-10-20T12:59:50.375+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_sort.xml.flat
2023-10-20T12:59:50.375+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\drawable_unfold_more_horizontal.xml.flat
2023-10-20T12:59:50.375+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\font_permanentmarker_regular.ttf.flat
2023-10-20T12:59:50.375+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\font_roboto_bold.ttf.flat
2023-10-20T12:59:50.375+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\font_roboto_regular.ttf.flat
2023-10-20T12:59:50.375+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_accounts_dashboard.xml.flat
2023-10-20T12:59:50.376+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_accounts_ledgers_popup.xml.flat
2023-10-20T12:59:50.376+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_accounts_ledger_aging.xml.flat
2023-10-20T12:59:50.376+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_accounts_ledger_card.xml.flat
2023-10-20T12:59:50.376+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_accounts_ledger_item.xml.flat
2023-10-20T12:59:50.376+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_accounts_ledger_value_item.xml.flat
2023-10-20T12:59:50.376+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_accounts_voucher_list_item.xml.flat
2023-10-20T12:59:50.376+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_accounts_voucher_popup.xml.flat
2023-10-20T12:59:50.377+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_account_aging.xml.flat
2023-10-20T12:59:50.377+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_account_aging_set.xml.flat
2023-10-20T12:59:50.377+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_create_invoice.xml.flat
2023-10-20T12:59:50.377+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_ledger_details.xml.flat
2023-10-20T12:59:50.377+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_ledger_list.xml.flat
2023-10-20T12:59:50.377+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_login.xml.flat
2023-10-20T12:59:50.377+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_login_popup.xml.flat
2023-10-20T12:59:50.378+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_material_detail.xml.flat
2023-10-20T12:59:50.378+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_notifications.xml.flat
2023-10-20T12:59:50.378+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_party_detail.xml.flat
2023-10-20T12:59:50.378+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_po_approval.xml.flat
2023-10-20T12:59:50.378+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_splash.xml.flat
2023-10-20T12:59:50.378+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_activity_xsdashboard.xml.flat
2023-10-20T12:59:50.378+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_add_material_activity.xml.flat
2023-10-20T12:59:50.379+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_apptheme_template.xml.flat
2023-10-20T12:59:50.379+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_app_preview.xml.flat
2023-10-20T12:59:50.379+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_app_preview_adapter.xml.flat
2023-10-20T12:59:50.379+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_audit_activity.xml.flat
2023-10-20T12:59:50.379+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_chart_marker.xml.flat
2023-10-20T12:59:50.379+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_content_top_bar.xml.flat
2023-10-20T12:59:50.379+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_content_xsdashboard.xml.flat
2023-10-20T12:59:50.380+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_date_set_with_search.xml.flat
2023-10-20T12:59:50.380+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_date_set_with_submit.xml.flat
2023-10-20T12:59:50.380+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expenses.xml.flat
2023-10-20T12:59:50.380+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expenses_head_popup.xml.flat
2023-10-20T12:59:50.380+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expenses_list_item.xml.flat
2023-10-20T12:59:50.380+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expenses_remarks_popup.xml.flat
2023-10-20T12:59:50.380+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expenses_remarks_popup_list_item.xml.flat
2023-10-20T12:59:50.381+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expense_add_screen.xml.flat
2023-10-20T12:59:50.381+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expense_add_screen_item.xml.flat
2023-10-20T12:59:50.381+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expense_add_screen_list_item.xml.flat
2023-10-20T12:59:50.381+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expense_add_screen_recyler.xml.flat
2023-10-20T12:59:50.381+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_expired_dialog.xml.flat
2023-10-20T12:59:50.381+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_fragment_audit_tab.xml.flat
2023-10-20T12:59:50.381+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_fragment_audit_tab_details.xml.flat
2023-10-20T12:59:50.382+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_fragment_dashboard.xml.flat
2023-10-20T12:59:50.382+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_grn_list_itemview.xml.flat
2023-10-20T12:59:50.382+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_grn_material_header.xml.flat
2023-10-20T12:59:50.382+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_grn_material_item.xml.flat
2023-10-20T12:59:50.382+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_grn_view.xml.flat
2023-10-20T12:59:50.382+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_master_bom.xml.flat
2023-10-20T12:59:50.382+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_master_bom_item.xml.flat
2023-10-20T12:59:50.382+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_master_list.xml.flat
2023-10-20T12:59:50.383+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_master_rate_list_item.xml.flat
2023-10-20T12:59:50.383+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_master_supplier_price_item.xml.flat
2023-10-20T12:59:50.383+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_material_stock_table.xml.flat
2023-10-20T12:59:50.383+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_multi_selection_listview.xml.flat
2023-10-20T12:59:50.383+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_multi_selection_list_item.xml.flat
2023-10-20T12:59:50.383+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_navigation_item.xml.flat
2023-10-20T12:59:50.383+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_nav_header_xsdashboard.xml.flat
2023-10-20T12:59:50.383+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_no_more_item.xml.flat
2023-10-20T12:59:50.384+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_oa_card_item.xml.flat
2023-10-20T12:59:50.384+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_page_no_view.xml.flat
2023-10-20T12:59:50.384+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_payment_dialog.xml.flat
2023-10-20T12:59:50.384+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_payment_plan_description_item.xml.flat
2023-10-20T12:59:50.384+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_payment_screen.xml.flat
2023-10-20T12:59:50.384+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_payment_screen_item.xml.flat
2023-10-20T12:59:50.384+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_payment_transaction_details.xml.flat
2023-10-20T12:59:50.384+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_po_approval_tag_layout.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_po_material_detail.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_po_over_due_item.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_progress_bar.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_purchase_dashboard.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_purchase_po_card.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_purchase_po_card1.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_purchase_po_status.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_purchase_quick_search.xml.flat
2023-10-20T12:59:50.385+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_rate_approvel.xml.flat
2023-10-20T12:59:50.386+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_request_extension_dialog.xml.flat
2023-10-20T12:59:50.386+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_sales_dashboard.xml.flat
2023-10-20T12:59:50.386+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_simple_popup.xml.flat
2023-10-20T12:59:50.386+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_simple_text.xml.flat
2023-10-20T12:59:50.386+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_simple_text_material.xml.flat
2023-10-20T12:59:50.386+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_sort_popup.xml.flat
2023-10-20T12:59:50.386+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_sort_popup_item.xml.flat
2023-10-20T12:59:50.386+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_store_grn.xml.flat
2023-10-20T12:59:50.387+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_store_indents.xml.flat
2023-10-20T12:59:50.387+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_store_stock.xml.flat
2023-10-20T12:59:50.387+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_store_stock_check.xml.flat
2023-10-20T12:59:50.387+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_subscribe_timer.xml.flat
2023-10-20T12:59:50.387+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_supplier_list_view.xml.flat
2023-10-20T12:59:50.387+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_sync_card.xml.flat
2023-10-20T12:59:50.387+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_tab_badge_view.xml.flat
2023-10-20T12:59:50.387+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_tab_layout.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_viewpager.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_view_add_material_item.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_view_add_tax_item.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\layout_view_text_item.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\menu_activity_xsdashboard_drawer.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\menu_auditing_popup_menu.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\menu_expense_popup_menu.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\menu_rate_approval_popup_menu.xml.flat
2023-10-20T12:59:50.388+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-anydpi-v26_ic_launcher.xml.flat
2023-10-20T12:59:50.389+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_arrow_down_black_18dp.png.flat
2023-10-20T12:59:50.389+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_arrow_up_black_18dp.png.flat
2023-10-20T12:59:50.389+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_audit.png.flat
2023-10-20T12:59:50.389+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_bill_invoice.png.flat
2023-10-20T12:59:50.389+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_check_circle.png.flat
2023-10-20T12:59:50.389+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_check_in.png.flat
2023-10-20T12:59:50.389+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_enterprise.png.flat
2023-10-20T12:59:50.390+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_eye.png.flat
2023-10-20T12:59:50.390+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_eye_off.png.flat
2023-10-20T12:59:50.390+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_add_grey_700_24dp.png.flat
2023-10-20T12:59:50.390+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_add_white_24dp.png.flat
2023-10-20T12:59:50.390+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_app_logo.png.flat
2023-10-20T12:59:50.390+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_clear_grey_400_24dp.png.flat
2023-10-20T12:59:50.391+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_close_black_24dp.png.flat
2023-10-20T12:59:50.391+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_close_circle_grey600_24dp.png.flat
2023-10-20T12:59:50.391+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_expand_less_black_36dp.png.flat
2023-10-20T12:59:50.391+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_expand_more_black_36dp.png.flat
2023-10-20T12:59:50.391+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_expand_more_grey_900_24dp.png.flat
2023-10-20T12:59:50.391+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_keyboard_arrow_left_black_24dp.png.flat
2023-10-20T12:59:50.392+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_launcher.png.flat
2023-10-20T12:59:50.392+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_launcher_background.png.flat
2023-10-20T12:59:50.392+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_launcher_foreground.png.flat
2023-10-20T12:59:50.392+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_launcher_monochrome.png.flat
2023-10-20T12:59:50.392+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_ic_warning_red_200_24dp.png.flat
2023-10-20T12:59:50.392+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_master.png.flat
2023-10-20T12:59:50.392+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_preview_accounts.jpg.flat
2023-10-20T12:59:50.393+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_preview_home.jpg.flat
2023-10-20T12:59:50.393+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_preview_purchase.jpg.flat
2023-10-20T12:59:50.393+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_preview_purchase_order.jpg.flat
2023-10-20T12:59:50.393+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_preview_sales.jpg.flat
2023-10-20T12:59:50.394+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_preview_store.jpg.flat
2023-10-20T12:59:50.394+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_purchse.png.flat
2023-10-20T12:59:50.394+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_remarks.png.flat
2023-10-20T12:59:50.394+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_sales.png.flat
2023-10-20T12:59:50.394+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_standard.png.flat
2023-10-20T12:59:50.394+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_stores.png.flat
2023-10-20T12:59:50.394+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_warning.png.flat
2023-10-20T12:59:50.395+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_xserp_exceed_logo.png.flat
2023-10-20T12:59:50.395+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-hdpi_xs_logo_square.png.flat
2023-10-20T12:59:50.395+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-mdpi_ic_add_grey_700_24dp.png.flat
2023-10-20T12:59:50.395+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-mdpi_ic_clear_grey_400_24dp.png.flat
2023-10-20T12:59:50.395+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-mdpi_ic_expand_more_grey_900_24dp.png.flat
2023-10-20T12:59:50.395+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-mdpi_ic_launcher.png.flat
2023-10-20T12:59:50.395+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-mdpi_ic_launcher_background.png.flat
2023-10-20T12:59:50.395+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-mdpi_ic_launcher_foreground.png.flat
2023-10-20T12:59:50.396+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-mdpi_ic_launcher_monochrome.png.flat
2023-10-20T12:59:50.396+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-mdpi_ic_warning_red_200_24dp.png.flat
2023-10-20T12:59:50.396+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_add_circle_outline.png.flat
2023-10-20T12:59:50.396+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_action_filter.png.flat
2023-10-20T12:59:50.396+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_add_grey_700_24dp.png.flat
2023-10-20T12:59:50.396+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_check_white_24dp.png.flat
2023-10-20T12:59:50.397+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_clear_grey_400_24dp.png.flat
2023-10-20T12:59:50.397+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_delete_black_24dp.png.flat
2023-10-20T12:59:50.397+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_expand_more_grey_900_24dp.png.flat
2023-10-20T12:59:50.397+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_launcher.png.flat
2023-10-20T12:59:50.397+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_launcher_background.png.flat
2023-10-20T12:59:50.397+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_launcher_foreground.png.flat
2023-10-20T12:59:50.397+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_ic_launcher_monochrome.png.flat
2023-10-20T12:59:50.397+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xhdpi_splash.jpg.flat
2023-10-20T12:59:50.398+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxhdpi_ic_clear_grey_400_24dp.png.flat
2023-10-20T12:59:50.398+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxhdpi_ic_date_range_grey_900_24dp.png.flat
2023-10-20T12:59:50.398+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxhdpi_ic_expand_more_grey_900_24dp.png.flat
2023-10-20T12:59:50.398+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxhdpi_ic_launcher.png.flat
2023-10-20T12:59:50.398+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxhdpi_ic_launcher_background.png.flat
2023-10-20T12:59:50.398+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxhdpi_ic_launcher_foreground.png.flat
2023-10-20T12:59:50.398+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_add_grey_700_24dp.png.flat
2023-10-20T12:59:50.398+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_clear_grey_400_24dp.png.flat
2023-10-20T12:59:50.399+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_date_range_grey_900_24dp.png.flat
2023-10-20T12:59:50.399+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_expand_more_grey_900_24dp.png.flat
2023-10-20T12:59:50.399+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_launcher.png.flat
2023-10-20T12:59:50.399+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_launcher_background.png.flat
2023-10-20T12:59:50.399+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_launcher_foreground.png.flat
2023-10-20T12:59:50.399+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_launcher_monochrome.png.flat
2023-10-20T12:59:50.399+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\mipmap-xxxhdpi_ic_warning_red_200_24dp.png.flat
2023-10-20T12:59:50.399+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\raw_notification.mp3.flat
2023-10-20T12:59:50.400+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-af_values-af.arsc.flat
2023-10-20T12:59:50.400+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-am_values-am.arsc.flat
2023-10-20T12:59:50.400+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ar_values-ar.arsc.flat
2023-10-20T12:59:50.400+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-as_values-as.arsc.flat
2023-10-20T12:59:50.400+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-az_values-az.arsc.flat
2023-10-20T12:59:50.400+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-b+es+419_values-b+es+419.arsc.flat
2023-10-20T12:59:50.400+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-b+sr+Latn_values-b+sr+Latn.arsc.flat
2023-10-20T12:59:50.400+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-be_values-be.arsc.flat
2023-10-20T12:59:50.401+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-bg_values-bg.arsc.flat
2023-10-20T12:59:50.401+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-bn_values-bn.arsc.flat
2023-10-20T12:59:50.401+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-bs_values-bs.arsc.flat
2023-10-20T12:59:50.401+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ca_values-ca.arsc.flat
2023-10-20T12:59:50.401+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-cs_values-cs.arsc.flat
2023-10-20T12:59:50.402+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-da_values-da.arsc.flat
2023-10-20T12:59:50.402+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-de_values-de.arsc.flat
2023-10-20T12:59:50.402+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-el_values-el.arsc.flat
2023-10-20T12:59:50.402+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-en-rAU_values-en-rAU.arsc.flat
2023-10-20T12:59:50.402+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-en-rCA_values-en-rCA.arsc.flat
2023-10-20T12:59:50.402+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-en-rGB_values-en-rGB.arsc.flat
2023-10-20T12:59:50.402+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-en-rIN_values-en-rIN.arsc.flat
2023-10-20T12:59:50.403+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-en-rXC_values-en-rXC.arsc.flat
2023-10-20T12:59:50.403+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-es-rUS_values-es-rUS.arsc.flat
2023-10-20T12:59:50.403+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-es_values-es.arsc.flat
2023-10-20T12:59:50.403+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-et_values-et.arsc.flat
2023-10-20T12:59:50.403+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-eu_values-eu.arsc.flat
2023-10-20T12:59:50.403+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-fa_values-fa.arsc.flat
2023-10-20T12:59:50.403+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-fi_values-fi.arsc.flat
2023-10-20T12:59:50.404+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-fr-rCA_values-fr-rCA.arsc.flat
2023-10-20T12:59:50.404+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-fr_values-fr.arsc.flat
2023-10-20T12:59:50.404+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-gl_values-gl.arsc.flat
2023-10-20T12:59:50.404+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-gu_values-gu.arsc.flat
2023-10-20T12:59:50.404+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-h320dp-port-v13_values-h320dp-port-v13.arsc.flat
2023-10-20T12:59:50.405+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-h360dp-land-v13_values-h360dp-land-v13.arsc.flat
2023-10-20T12:59:50.405+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-h480dp-land-v13_values-h480dp-land-v13.arsc.flat
2023-10-20T12:59:50.405+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-h550dp-port-v13_values-h550dp-port-v13.arsc.flat
2023-10-20T12:59:50.405+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-h720dp-v13_values-h720dp-v13.arsc.flat
2023-10-20T12:59:50.405+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-hdpi-v4_values-hdpi-v4.arsc.flat
2023-10-20T12:59:50.405+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-hi_values-hi.arsc.flat
2023-10-20T12:59:50.406+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-hr_values-hr.arsc.flat
2023-10-20T12:59:50.406+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-hu_values-hu.arsc.flat
2023-10-20T12:59:50.406+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-hy_values-hy.arsc.flat
2023-10-20T12:59:50.406+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-in_values-in.arsc.flat
2023-10-20T12:59:50.406+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-is_values-is.arsc.flat
2023-10-20T12:59:50.406+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-it_values-it.arsc.flat
2023-10-20T12:59:50.406+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-iw_values-iw.arsc.flat
2023-10-20T12:59:50.407+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ja_values-ja.arsc.flat
2023-10-20T12:59:50.407+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ka_values-ka.arsc.flat
2023-10-20T12:59:50.407+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-kk_values-kk.arsc.flat
2023-10-20T12:59:50.407+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-km_values-km.arsc.flat
2023-10-20T12:59:50.407+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-kn_values-kn.arsc.flat
2023-10-20T12:59:50.407+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ko_values-ko.arsc.flat
2023-10-20T12:59:50.408+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ky_values-ky.arsc.flat
2023-10-20T12:59:50.408+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-land_values-land.arsc.flat
2023-10-20T12:59:50.408+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-large-v4_values-large-v4.arsc.flat
2023-10-20T12:59:50.408+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ldltr-v21_values-ldltr-v21.arsc.flat
2023-10-20T12:59:50.408+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ldrtl-v17_values-ldrtl-v17.arsc.flat
2023-10-20T12:59:50.408+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-lo_values-lo.arsc.flat
2023-10-20T12:59:50.408+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-lt_values-lt.arsc.flat
2023-10-20T12:59:50.409+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-lv_values-lv.arsc.flat
2023-10-20T12:59:50.409+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-mdpi-v4_values-mdpi-v4.arsc.flat
2023-10-20T12:59:50.409+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-mk_values-mk.arsc.flat
2023-10-20T12:59:50.409+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ml_values-ml.arsc.flat
2023-10-20T12:59:50.409+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-mn_values-mn.arsc.flat
2023-10-20T12:59:50.409+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-mr_values-mr.arsc.flat
2023-10-20T12:59:50.409+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ms_values-ms.arsc.flat
2023-10-20T12:59:50.410+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-my_values-my.arsc.flat
2023-10-20T12:59:50.410+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-nb_values-nb.arsc.flat
2023-10-20T12:59:50.410+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ne_values-ne.arsc.flat
2023-10-20T12:59:50.411+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-night-v8_values-night-v8.arsc.flat
2023-10-20T12:59:50.411+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-nl_values-nl.arsc.flat
2023-10-20T12:59:50.411+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-or_values-or.arsc.flat
2023-10-20T12:59:50.411+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-pa_values-pa.arsc.flat
2023-10-20T12:59:50.411+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-pl_values-pl.arsc.flat
2023-10-20T12:59:50.411+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-port_values-port.arsc.flat
2023-10-20T12:59:50.413+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-pt-rBR_values-pt-rBR.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-pt-rPT_values-pt-rPT.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-pt_values-pt.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ro_values-ro.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ru_values-ru.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-si_values-si.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-sk_values-sk.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-sl_values-sl.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-small-v4_values-small-v4.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-sq_values-sq.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-sr_values-sr.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-sv_values-sv.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-sw600dp-v13_values-sw600dp-v13.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-sw_values-sw.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ta_values-ta.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-te_values-te.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-th_values-th.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-tl_values-tl.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-tr_values-tr.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-uk_values-uk.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-ur_values-ur.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-uz_values-uz.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v16_values-v16.arsc.flat
2023-10-20T12:59:50.414+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v17_values-v17.arsc.flat
2023-10-20T12:59:50.415+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v18_values-v18.arsc.flat
2023-10-20T12:59:50.415+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v21_values-v21.arsc.flat
2023-10-20T12:59:50.418+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v22_values-v22.arsc.flat
2023-10-20T12:59:50.418+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v23_values-v23.arsc.flat
2023-10-20T12:59:50.418+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v24_values-v24.arsc.flat
2023-10-20T12:59:50.418+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v25_values-v25.arsc.flat
2023-10-20T12:59:50.418+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v26_values-v26.arsc.flat
2023-10-20T12:59:50.418+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v28_values-v28.arsc.flat
2023-10-20T12:59:50.419+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-v31_values-v31.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-vi_values-vi.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-w320dp-land-v13_values-w320dp-land-v13.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-w360dp-port-v13_values-w360dp-port-v13.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-w480dp-port-v13_values-w480dp-port-v13.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-w600dp-land-v13_values-w600dp-land-v13.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-watch-v20_values-watch-v20.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-watch-v21_values-watch-v21.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-xhdpi-v4_values-xhdpi-v4.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-xlarge-v4_values-xlarge-v4.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-xxhdpi-v4_values-xxhdpi-v4.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-xxxhdpi-v4_values-xxxhdpi-v4.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-zh-rCN_values-zh-rCN.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-zh-rHK_values-zh-rHK.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-zh-rTW_values-zh-rTW.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values-zu_values-zu.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\values_values.arsc.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\xml_data_extraction_rules.xml.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug\xml_provider_paths.xml.flat
2023-10-20T12:59:50.420+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res\devDebug
2023-10-20T12:59:50.422+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\devDebug.json
2023-10-20T12:59:50.422+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-af.json
2023-10-20T12:59:50.422+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-am.json
2023-10-20T12:59:50.422+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ar.json
2023-10-20T12:59:50.422+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-as.json
2023-10-20T12:59:50.423+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-az.json
2023-10-20T12:59:50.423+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-b+es+419.json
2023-10-20T12:59:50.423+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-b+sr+Latn.json
2023-10-20T12:59:50.423+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-be.json
2023-10-20T12:59:50.423+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-bg.json
2023-10-20T12:59:50.423+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-bn.json
2023-10-20T12:59:50.423+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-bs.json
2023-10-20T12:59:50.424+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ca.json
2023-10-20T12:59:50.424+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-cs.json
2023-10-20T12:59:50.424+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-da.json
2023-10-20T12:59:50.424+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-de.json
2023-10-20T12:59:50.424+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-el.json
2023-10-20T12:59:50.424+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-en-rAU.json
2023-10-20T12:59:50.424+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-en-rCA.json
2023-10-20T12:59:50.424+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-en-rGB.json
2023-10-20T12:59:50.425+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-en-rIN.json
2023-10-20T12:59:50.425+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-en-rXC.json
2023-10-20T12:59:50.425+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-es-rUS.json
2023-10-20T12:59:50.425+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-es.json
2023-10-20T12:59:50.425+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-et.json
2023-10-20T12:59:50.426+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-eu.json
2023-10-20T12:59:50.426+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-fa.json
2023-10-20T12:59:50.426+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-fi.json
2023-10-20T12:59:50.426+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-fr-rCA.json
2023-10-20T12:59:50.426+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-fr.json
2023-10-20T12:59:50.426+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-gl.json
2023-10-20T12:59:50.426+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-gu.json
2023-10-20T12:59:50.426+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-h320dp-port-v13.json
2023-10-20T12:59:50.427+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-h360dp-land-v13.json
2023-10-20T12:59:50.427+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-h480dp-land-v13.json
2023-10-20T12:59:50.427+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-h550dp-port-v13.json
2023-10-20T12:59:50.427+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-h720dp-v13.json
2023-10-20T12:59:50.427+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-hdpi-v4.json
2023-10-20T12:59:50.427+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-hi.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-hr.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-hu.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-hy.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-in.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-is.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-it.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-iw.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ja.json
2023-10-20T12:59:50.428+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ka.json
2023-10-20T12:59:50.429+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-kk.json
2023-10-20T12:59:50.429+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-km.json
2023-10-20T12:59:50.429+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-kn.json
2023-10-20T12:59:50.429+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ko.json
2023-10-20T12:59:50.429+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ky.json
2023-10-20T12:59:50.429+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-land.json
2023-10-20T12:59:50.430+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-large-v4.json
2023-10-20T12:59:50.430+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ldltr-v21.json
2023-10-20T12:59:50.430+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ldrtl-v17.json
2023-10-20T12:59:50.430+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-lo.json
2023-10-20T12:59:50.430+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-lt.json
2023-10-20T12:59:50.430+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-lv.json
2023-10-20T12:59:50.430+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-mdpi-v4.json
2023-10-20T12:59:50.431+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-mk.json
2023-10-20T12:59:50.431+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ml.json
2023-10-20T12:59:50.431+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-mn.json
2023-10-20T12:59:50.433+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-mr.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ms.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-my.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-nb.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ne.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-night-v8.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-nl.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-or.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-pa.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-pl.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-port.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-pt-rBR.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-pt-rPT.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-pt.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ro.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ru.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-si.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-sk.json
2023-10-20T12:59:50.434+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-sl.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-small-v4.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-sq.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-sr.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-sv.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-sw.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-sw600dp-v13.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ta.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-te.json
2023-10-20T12:59:50.435+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-th.json
2023-10-20T12:59:50.436+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-tl.json
2023-10-20T12:59:50.436+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-tr.json
2023-10-20T12:59:50.436+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-uk.json
2023-10-20T12:59:50.436+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-ur.json
2023-10-20T12:59:50.436+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-uz.json
2023-10-20T12:59:50.436+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v16.json
2023-10-20T12:59:50.436+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v17.json
2023-10-20T12:59:50.437+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v18.json
2023-10-20T12:59:50.437+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v21.json
2023-10-20T12:59:50.437+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v22.json
2023-10-20T12:59:50.437+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v23.json
2023-10-20T12:59:50.437+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v24.json
2023-10-20T12:59:50.437+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v25.json
2023-10-20T12:59:50.437+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v26.json
2023-10-20T12:59:50.438+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v28.json
2023-10-20T12:59:50.438+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-v31.json
2023-10-20T12:59:50.438+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-vi.json
2023-10-20T12:59:50.438+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-w320dp-land-v13.json
2023-10-20T12:59:50.438+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-w360dp-port-v13.json
2023-10-20T12:59:50.438+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-w480dp-port-v13.json
2023-10-20T12:59:50.438+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-w600dp-land-v13.json
2023-10-20T12:59:50.439+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-watch-v20.json
2023-10-20T12:59:50.439+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-watch-v21.json
2023-10-20T12:59:50.439+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-xhdpi-v4.json
2023-10-20T12:59:50.439+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-xlarge-v4.json
2023-10-20T12:59:50.439+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-xxhdpi-v4.json
2023-10-20T12:59:50.439+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-xxxhdpi-v4.json
2023-10-20T12:59:50.439+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-zh-rCN.json
2023-10-20T12:59:50.439+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-zh-rHK.json
2023-10-20T12:59:50.440+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-zh-rTW.json
2023-10-20T12:59:50.440+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values-zu.json
2023-10-20T12:59:50.440+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2\values.json
2023-10-20T12:59:50.440+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\multi-v2
2023-10-20T12:59:50.440+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\single\devDebug.json
2023-10-20T12:59:50.440+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out\single
2023-10-20T12:59:50.441+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_res_blame_folder\devDebug\out
2023-10-20T12:59:50.441+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\merged_shaders\devDebug\out
2023-10-20T12:59:50.442+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\navigation_json\devDebug\navigation.json
2023-10-20T12:59:50.442+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\packaged_manifests\devDebug\AndroidManifest.xml
2023-10-20T12:59:50.442+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\packaged_manifests\devDebug\output-metadata.json
2023-10-20T12:59:50.442+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\packaged_manifests\devDebug
2023-10-20T12:59:50.443+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\signing_config_versions\devDebug\signing-config-versions.json
2023-10-20T12:59:50.443+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates\validate_signing_config\devDebug
2023-10-20T12:59:50.443+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\intermediates
2023-10-20T12:59:50.444+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build\outputs\logs\manifest-merger-dev-debug-report.txt
2023-10-20T12:59:50.444+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change REMOVED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\build
2023-10-20T12:59:54.332+0530 [DEBUG] [org.gradle.internal.watch.registry.impl.DefaultFileWatcherProbeRegistry] Triggering watch probe for D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage
2023-10-20T12:59:54.332+0530 [DEBUG] [org.gradle.internal.watch.registry.impl.DefaultFileWatcherProbeRegistry] Triggering watch probe for D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage
2023-10-20T12:59:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T12:59:59.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T12:59:59.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T12:59:59.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:09.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:09.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:09.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:09.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:09.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:19.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:19.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:19.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:19.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:19.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:19.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:19.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:19.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:19.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:29.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:29.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:29.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:29.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:29.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:29.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:29.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:29.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:29.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:39.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:39.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:39.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:39.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:49.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:49.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:49.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:59.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:59.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:00:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:00:59.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:00:59.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:09.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:09.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:09.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:09.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:09.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:09.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:09.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:09.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:09.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:19.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:19.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:19.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:19.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:19.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:19.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:19.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:19.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:19.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:29.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:29.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:29.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:29.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:29.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:29.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:29.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:29.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:29.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:39.932+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:39.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:39.933+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:39.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:39.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:39.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:39.934+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:39.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:39.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:49.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:49.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:49.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:49.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:49.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:49.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:49.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:49.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:59.974+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:59.975+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:59.976+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:59.977+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:59.977+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:59.978+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:01:59.978+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:01:59.978+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:01:59.978+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:09.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:09.936+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:09.937+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:09.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:09.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:09.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:09.938+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:09.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:09.939+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:19.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:19.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:19.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:19.944+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:19.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:21.563+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change CREATED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\res\layout\sign_up_screen.xml~
2023-10-20T13:02:21.565+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Handling VFS change MODIFIED D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\xserp\src\main\res\layout\sign_up_screen.xml
2023-10-20T13:02:29.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:29.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:29.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:29.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:39.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:39.940+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:39.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:39.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:39.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:39.941+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:39.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:39.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:39.942+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:45.187+0530 [DEBUG] [org.gradle.process.internal.health.memory.DefaultMemoryManager] ********** memory requested, ********** free
2023-10-20T13:02:45.187+0530 [DEBUG] [org.gradle.workers.internal.WorkerDaemonExpiration] Will attempt to release 1611 of memory
2023-10-20T13:02:45.188+0530 [DEBUG] [org.gradle.process.internal.health.memory.DefaultMemoryManager] ********** memory requested, 0 released, ********** free
2023-10-20T13:02:49.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:49.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:49.945+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:49.946+0530 [DEBUG] [org.gradle.launcher.daemon.server.health.LowMemoryDaemonExpirationStrategy] Nearing low memory threshold - OS memory {Total: **********8, Free: **********}
2023-10-20T13:02:49.946+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2023-10-20T13:02:49.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:49.947+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:50.191+0530 [DEBUG] [org.gradle.process.internal.health.memory.DefaultMemoryManager] ********** memory requested, ********** free
2023-10-20T13:02:50.191+0530 [DEBUG] [org.gradle.workers.internal.WorkerDaemonExpiration] Will attempt to release 1611 of memory
2023-10-20T13:02:50.191+0530 [DEBUG] [org.gradle.process.internal.health.memory.DefaultMemoryManager] ********** memory requested, 0 released, ********** free
2023-10-20T13:02:55.196+0530 [DEBUG] [org.gradle.process.internal.health.memory.DefaultMemoryManager] ********** memory requested, ********** free
2023-10-20T13:02:55.196+0530 [DEBUG] [org.gradle.workers.internal.WorkerDaemonExpiration] Will attempt to release 1611 of memory
2023-10-20T13:02:55.196+0530 [DEBUG] [org.gradle.process.internal.health.memory.DefaultMemoryManager] ********** memory requested, 0 released, ********** free
2023-10-20T13:02:58.897+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59455 to /127.0.0.1:59154.
2023-10-20T13:02:58.899+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:59456 to /127.0.0.1:59154.
2023-10-20T13:02:58.901+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 597: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2023-10-20T13:02:58.901+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 597: Received non-IO message from client: StopWhenIdle[id=55e736c2-7395-47e0-8a70-65f8e127d5d5]
2023-10-20T13:02:58.902+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=55e736c2-7395-47e0-8a70-65f8e127d5d5].
2023-10-20T13:02:58.902+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=55e736c2-7395-47e0-8a70-65f8e127d5d5] with connection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59455.
2023-10-20T13:02:58.902+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2023-10-20T13:02:58.902+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1697787178901
2023-10-20T13:02:58.903+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T13:02:58.903+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:58.904+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:58.904+0530 [LIFECYCLE] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Daemon will be stopped at the end of the build stop command received
2023-10-20T13:02:58.904+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Marking daemon stopped due to stop command received. The daemon is running a build: false
2023-10-20T13:02:58.904+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 596: dispatching Success[value=null]
2023-10-20T13:02:58.904+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon has stopped.
2023-10-20T13:02:58.904+0530 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stop() called on daemon
2023-10-20T13:02:58.905+0530 [INFO] [org.gradle.launcher.daemon.server.Daemon] Stop requested. Daemon is removing its presence from the registry...
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 597: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=55e736c2-7395-47e0-8a70-65f8e127d5d5]
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 597: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@398c541a
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Removing our presence to clients, eg. removing this address from the registry: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: org.gradle.launcher.daemon.protocol.Finished@398c541a
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 597: received null
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 597: Received end-of-input from client.
2023-10-20T13:02:58.905+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 596: stopping connection
2023-10-20T13:02:58.906+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 596: stopping connection
2023-10-20T13:02:58.907+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 599: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2023-10-20T13:02:58.907+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 599: Received non-IO message from client: StopWhenIdle[id=cea9ffa9-dcf7-45cd-82d7-66e667d59883]
2023-10-20T13:02:58.908+0530 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=cea9ffa9-dcf7-45cd-82d7-66e667d59883].
2023-10-20T13:02:58.908+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=cea9ffa9-dcf7-45cd-82d7-66e667d59883] with connection: socket connection from /127.0.0.1:59154 to /127.0.0.1:59456.
2023-10-20T13:02:58.908+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2023-10-20T13:02:58.913+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T13:02:58.914+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:58.923+0530 [DEBUG] [org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler] Gradle process at port 51646 confirmed unlock request for lock with id -9145054052816913734.
2023-10-20T13:02:58.924+0530 [DEBUG] [org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler] Gradle process at port 51646 confirmed unlock request for lock with id -9145054052816913734.
2023-10-20T13:02:58.928+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:58.928+0530 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Address removed from registry.
2023-10-20T13:02:58.929+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1697787178907
2023-10-20T13:02:58.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T13:02:58.935+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:58.943+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2023-10-20T13:02:58.943+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 598: dispatching Success[value=null]
2023-10-20T13:02:58.944+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=cea9ffa9-dcf7-45cd-82d7-66e667d59883]
2023-10-20T13:02:58.944+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 599: received class org.gradle.launcher.daemon.protocol.Finished
2023-10-20T13:02:58.944+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 599: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@12567b2a
2023-10-20T13:02:58.944+0530 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2023-10-20T13:02:58.944+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 599: received null
2023-10-20T13:02:58.944+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 599: Received end-of-input from client.
2023-10-20T13:02:58.946+0530 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received finished message: null
2023-10-20T13:02:58.946+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 598: stopping connection
2023-10-20T13:02:58.947+0530 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 598: stopping connection
2023-10-20T13:02:58.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] jars (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\jars-9) has last been fully cleaned up 0 hours ago
2023-10-20T13:02:58.948+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] Artifact transforms cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\transforms-3) has last been fully cleaned up 0 hours ago
2023-10-20T13:02:58.952+0530 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Closing VFS, dropping state
2023-10-20T13:02:58.953+0530 [DEBUG] [org.gradle.internal.watch.registry.impl.DefaultFileWatcherRegistry] Finished listening to file system change events
2023-10-20T13:02:58.953+0530 [DEBUG] [org.gradle.cache.internal.DefaultCacheAccess] Cache Generated Gradle JARs cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\7.6\generated-gradle-jars) was closed 0 times.
2023-10-20T13:02:58.953+0530 [DEBUG] [org.gradle.cache.internal.DefaultPersistentDirectoryStore] artifact cache (D:\Veeramanikandan\LiveProjects\schnellxserpwithPackage\generatedgradle\caches\modules-2) has last been fully cleaned up 0 hours ago
2023-10-20T13:02:58.961+0530 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [f81965b6-6c82-4a8d-91c6-0bc69091b0f0 port:59154, addresses:[localhost/127.0.0.1]]
Daemon vm is shutting down... The daemon has exited normally or was terminated in response to a user interrupt.
2023-10-20T13:02:58.968+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2023-10-20T13:02:58.969+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] The file lock for daemon addresses registry is held by a different Gradle process (pid: 296, lockId: 5578978609376646885). Pinged owner at port 51646
2023-10-20T13:02:58.974+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2023-10-20T13:02:58.984+0530 [DEBUG] [org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler] Gradle process at port 51646 confirmed unlock request for lock with id 2942327262835302866.
2023-10-20T13:02:58.984+0530 [DEBUG] [org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler] Gradle process at port 51646 confirmed unlock request for lock with id 2942327262835302866.
2023-10-20T13:02:58.985+0530 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
