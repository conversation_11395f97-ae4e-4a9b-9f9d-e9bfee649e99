package com.schnell.xsmanager.webservice

import android.content.Context
import android.util.Log
import com.android.volley.Request
import com.google.firebase.iid.FirebaseInstanceId
import com.schnell.http.*
import com.schnell.widget.AppDialogs
import com.schnell.xsmanager.model.User
import com.schnell.xsmanager.model.VersionInfo
import com.schnell.xsmanager.model.XSNotification

/**
 * @since 14/3/17.
 * User API services
 */

class UserService : RequestManager() {

    /**
     * Web Service partyNames should be paired with @RequestType <BR></BR>
     * WSM - Web service method
     */
    interface API {
        companion object {
            const val login = "user/json/login_api/"
            const val logout = "user/json/logout_api/"
            const val registerFcmId = "commons/json/reg_fcm_id/"
            const val updateNotificationRead = "commons/json/up_nm_read/"
            const val notificationList = "commons/json/nm_list/"
            const val deleteNotification = "commons/json/del_nm/"
            const val document = "commons/json/document/"
            const val enterpriseLogo = "auth/enterprise_logo/"
            const val forgotPassword = "auth/json/forget_password/"
            const val changePassword = "auth/json/change_password/"
            const val terms = "erp/public/terms/"
            const val privacy = "erp/public/privacy/"
            const val versionInfo = "commons/version_info/"
            const val userSettings = "auth/json/user_settings/"
        }
    }

    companion object {

        /**
         * @param c    Context of App
         * @param user instance of [User] will carry only email and password
         * @return false if exception happened before http call
         */
        fun login(c: Context?, user: User): Boolean {

            try {
                // Generating Req
                FirebaseInstanceId.getInstance().instanceId.addOnSuccessListener {
                    val client = RestClient(c, Request.Method.POST, constructUrl(API.login), API.login.hashCode())
                    fillCommons(c, client)
                    client.addParam("user_email", user.email.toString())
                    client.addParam("password", user.password.toString())
                    client.addParam("fcm_id", it.token)
                    Log.d("xserp-fcmid", "fcmid: ${it.token}")
                    client.execute(c as ResponseListener, User::class.java)
                }

            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        /**
         * @param c    Context of App
         * @return false if exception happened before http call
         */
        fun userSettings(c: Context?): Boolean {

            try {
                // Generating Req
                val client = RestClient(c, Request.Method.POST, constructUrl(API.userSettings), API.userSettings.hashCode())
                fillCommons(c, client)
                client.execute(c as ResponseListener, User::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        /**
         * @param c    Context of App
         * @param user instance of [User] that was resulted on login
         * @return false if exception happened before http call
         */
        fun enterpriseLogo(c: Context, user: User): Boolean {

            try {
                // Generating Req
                val client = DownloadClient(c, Request.Method.POST, constructUrl(API.enterpriseLogo), API.enterpriseLogo.hashCode())
                fillCommons(c, client)
                client.execute(c as ResponseListener, FileResponse::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        /**
         * @param c    Context of App
         * @param user instance of [User] will carry only email
         * @return false if exception happened before http call
         */
        fun logout(c: Context, user: User): Boolean {

            try {
                // Generating Req
                FirebaseInstanceId.getInstance().instanceId.addOnSuccessListener {
                    val client = RestClient(c, Request.Method.POST, constructUrl(API.logout), API.logout.hashCode())
                    fillCommons(c, client)
                    client.addParam("user_email", user.email.toString())
                    client.addParam("fcm_id", it.token)
                    client.execute(c as ResponseListener, User::class.java)
                }

            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        /**
         * @param c              Context of App
         * @param notificationId FCM registration id
         * @return false if exception happened before http call
         */
        fun updateNotificationRead(c: Context, notificationId: String): Boolean {

            try {
                // Generating Req
                val client = RestClient(c, Request.Method.POST, constructUrl(API.updateNotificationRead), API.updateNotificationRead.hashCode())

                fillCommons(c, client)
                client.addParam("notification_id", notificationId)
                client.execute(c as ResponseListener, Response::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        /**
         * @param c     Context of App
         * @param fcmId FCM registration id
         * @return false if exception happened before http call
         */
        fun registerForNotification(c: Context, fcmId: String): Boolean {

            try {
                // Generating Req
                val client = RestClient(c, Request.Method.POST, constructUrl(API.registerFcmId), API.registerFcmId.hashCode())
                fillCommons(c, client) ?: return false
                client.addParam("fcm_id", fcmId)
                client.execute(null, Response::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        /**
         * @param c Context
         * @param l response listener implementation
         * @return false for exception happened before http call
         */
        fun getNotificationList(c: Context, l: ResponseListener): Boolean {
            try {
                // Generating Req
                val client = RestClient(c, Request.Method.POST, constructUrl(API.notificationList), API.notificationList.hashCode())

                fillCommons(c, client)
                client.execute(l, XSNotification::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        /**
         * @param c Context
         * @return false for exception happened before http call
         */
        fun deleteNotification(c: Context, l: ResponseListener, notificationId: List<XSNotification>): Boolean {

            try {
                // Generating Req
                val client = RestClient(c, Request.Method.POST, constructUrl(API.deleteNotification), API.deleteNotification.hashCode())
                fillCommons(c, client)
                client.addParam("notification_ids", notificationId.toString().replace("[", "").replace("]", "").replace(" ", ""))
                client.execute(l, XSNotification::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        fun downloadDocument(c: Context, l: ResponseListener, url: String): Boolean {
            try {
                AppDialogs.showProgressDialog(c, "Downloading document...")
                val client = DownloadClient(c, Request.Method.POST, constructUrl(API.document), API.document.hashCode())
                fillCommons(c, client)
                client.addParam("document_uri", url)
                client.execute(l, FileResponse::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun forgotPassword(c: Context?, user: User): Boolean {

            try {
                // Generating Req
                val client = RestClient(c, Request.Method.POST, constructUrl(API.forgotPassword), API.forgotPassword.hashCode())

                fillCommons(c, client)
                client.addParam("user_email", user.email!!)
                client.execute(c as ResponseListener, User::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        fun changePassword(c: Context?, user: User, cp_token: String? = ""): Boolean {

            try {
                // Generating Req
                val client = RestClient(c, Request.Method.POST, constructUrl(API.changePassword), API.changePassword.hashCode())
                fillCommons(c, client)
                client.addParam("user_email", user.email!!)
                client.addParam("old_password", user.password!!)
                client.addParam("new_password", user.confirmPassword!!)
                client.addParam("cp_token", cp_token!!)
                client.execute(c as ResponseListener, User::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        fun getVersionInfo(c: Context): Boolean {
            try {
                val client = RestClient(c, Request.Method.GET, constructUrl(API.versionInfo), API.versionInfo.hashCode())
                // Set shorter timeout for version check to avoid hanging on splash screen
                client.setTimeout(10) // 10 seconds timeout
                client.execute(c as ResponseListener, VersionInfo::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }
    }
}
