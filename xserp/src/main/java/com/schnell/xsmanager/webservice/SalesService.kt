package com.schnell.xsmanager.webservice

import android.content.Context
import android.os.AsyncTask
import android.util.Log

import com.android.volley.Request
import com.google.gson.Gson
import com.schnell.http.*
import com.schnell.widget.AppDialogs
import com.schnell.xsmanager.model.*

/**
 * @since 8/4/17.
 * Sales module services
 */

class SalesService : RequestManager() {

    /**
     * API names for Sales module
     */
    interface API {
        companion object {
            const val dashboard = "sales/json/dashboard/"
            const val salesDetail = "sales/json/salesDetail/"
            const val saveInvoice = "sales/json/save_invoice_page/"
            const val invoiceSearch = "sales/json/invoiceSearch/"
            const val invoiceDraft = "sales/json/draft_invoice_fetch/"
            const val invoiceMaterial = "sales/json/invoice_material/"
            const val invoiceMaterialOverDue = "sales/json/invoice_material_overdue/"
            const val approve = "sales/invoice/approve/"
            const val reject = "sales/invoice/reject/"
            const val oaSearch = "sales/json/oa_search/"
            const val oaFinanceYear = "sales/json/oa_finance_year/"
            const val oaDraft = "sales/json/draft_oa/"
            const val invoiceFinanceYear = "sales/json/finance_year/"
            const val oaMaterial = "sales/json/oa_material/"
            const val oaApprove = "sales/oa/approve/"
            const val oaReject = "sales/oa/reject/"
            const val oaDocument = "sales/json/oa_doc/"
            const val oaCheckInvoiceCount = "sales/json/oa/checkoainvoice_qty/"
            const val invoiceDocument = "sales/json/inv_doc/"
            const val oaStatus = "sales/json/oa_status/"
            const val materialProfilePrice = "sales/json/invoice/loadPartyRate/"
            const val oaUploadDocument = "commons/json/document/"

        }
    }

    companion object {

        /**
         * Save Invoice
         *
         * @param c Context
         * @param l Response Listener
         * @return false if exception happened before http call
         */
        fun saveInvoice(c: Context, l: ResponseListener, invoice: Invoice): Boolean {
            try {
                LoadingTask(c, l, invoice).execute()
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }

            return true
        }

        class LoadingTask internal constructor(private var c: Context, private var l: ResponseListener, private var invoice: Invoice) : AsyncTask<Void, Void, String>() {

            override fun doInBackground(vararg params: Void?): String? {
                val gson = Gson()
                val toUpload = gson.fromJson(gson.toJson(invoice), Invoice::class.java)
                return gson.toJson(toUpload)
            }


            override fun onPostExecute(result: String?) {
                try {
                    val client = RestClient(c, Request.Method.POST, RequestManager.constructUrl(API.saveInvoice), API.saveInvoice.hashCode())
                    fillCommons(c, client)
                    Log.e("Save Invoice", result!!)
                    client.addParam("invoice_data", result)
                    client.execute(l, Response::class.java)
                } catch (e: Exception) {
                    e.printStackTrace()
                    return
                }
                super.onPostExecute(result)
            }
        }

        /**
         * Get Dashboard data for Sales
         *
         * @param c Context
         * @param l Response Listener
         * @return false if exception happened before http call
         */
        fun getDashboardData(c: Context, l: ResponseListener): Boolean {

            try {
                Log.i("Stores", "Connecting Purchase Service")
                // Generating Req
                AppDialogs.showProgressDialog(c, "Loading Sales Dashboard...")
                val client = RestClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.dashboard), API.dashboard.hashCode())
                client.setTimeout(120)
                RequestManager.fillCommons(c, client)
                client.execute(l, SalesDashboardData::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                l.onResponse(null)
                return false
            }

            return true
        }

        /**
         * Get Purchase MonthlySale to deploy chart
         *
         * @param c        Context
         * @param l        Response Listener
         * @param fromDate date in format "yyyy-mm-dd"
         * @param toDate   date in format "yyyy-mm-dd" should be greater than fromDate
         * @return false if exception happened before http call
         */
        fun getSalesDetail(c: Context, l: ResponseListener, fromDate: String?, toDate: String?): Boolean {

            try {
                Log.i("Stores", "Connecting Purchase Service")
                // Generating Req
                val client = RestClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.salesDetail), API.salesDetail.hashCode())

                RequestManager.fillCommons(c, client)
                if (fromDate != null) {
                    client.addParam("from_date", fromDate)
                }
                if (toDate != null) {
                    client.addParam("to_date", toDate)
                }

                client.execute(l, SalesDetail::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                AppDialogs.hideProgressDialog()
                return false
            }

            return true
        }

        /**
         * Search By Invoice / By Supplier / By Material / By Tag
         *
         * @param c         Context
         * @param l         Response Listener
         * @param fromDate  date in format "yyyy-mm-dd"
         * @param toDate    date in format "yyyy-mm-dd" should be greater than fromDate
         * @return false if exception happened before http call
         */
        fun searchInvoices(c: Context, l: ResponseListener, fromDate: String?, toDate: String?, searchNo: String, customerId: String,
                           itemId: String, projectCode: String, status: Int, finance_year: String): Boolean {

            try {
                Log.i("SalesService", "Searching invoices - From: $fromDate, To: $toDate, Customer: $customerId")
                Log.i("SalesService", "Search API URL: ${RequestManager.constructUrl(API.invoiceSearch)}")

                AppDialogs.showProgressDialog(c, "Searching invoice(s)...")
                val client = RestClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.invoiceSearch),
                        API.invoiceSearch.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("invoiceNo", searchNo)
                client.addParam("customerId", customerId)
                client.addParam("project_code", projectCode)
                client.addParam("item_id", itemId)
                client.addParam("status", status.toString())
                client.addParam("since", fromDate!!)
                client.addParam("till", toDate!!)
                client.addParam("finance_year", finance_year)
                client.setTimeout(120) // Set 2 minute timeout for search
                client.execute(l, Invoice::class.java)
            } catch (e: Exception) {
                Log.e("SalesService", "Exception in searchInvoices", e)
                e.printStackTrace()
                AppDialogs.hideProgressDialog()

                // Send error response to listener
                val errorResponse = Response()
                errorResponse.requestType = API.invoiceSearch.hashCode()
                errorResponse.responseCode = 500
                errorResponse.message = "Search Failed"
                errorResponse.customMessage = "Failed to search invoices: ${e.message}"
                l.onResponse(errorResponse)
                return false
            }
            return true
        }

        fun draftAllInvoices(c: Context, l: ResponseListener): Boolean {

            try {
                AppDialogs.showProgressDialog(c, "Loading invoices...")
                val client = RestClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.invoiceDraft), API.invoiceDraft.hashCode())
                RequestManager.fillCommons(c, client)
                client.execute(l, Invoice::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                AppDialogs.hideProgressDialog()
                return false
            }
            return true
        }

        fun searchOA(c: Context, l: ResponseListener, fromDate: String?, toDate: String?, serchNo: String, supplierId: String,
                     itemId: String, projectCode: String, status: Int, finance_year: String): Boolean {

            try {
                AppDialogs.showProgressDialog(c, "Searching oa(s)...")
                Log.i("Stores", "Connecting Sales Service")
                // Generating Req
                val client = RestClient(c, Request.Method.POST, constructUrl(API.oaSearch),
                        API.oaSearch.hashCode())
                fillCommons(c, client)
                client.addParam("oa_no", serchNo)
                client.addParam("supplier_id", supplierId)
                client.addParam("project_code", projectCode)
                client.addParam("item_id", itemId)
                client.addParam("status", status.toString())
                client.addParam("since", fromDate!!)
                client.addParam("till", toDate!!)
                client.addParam("finance_year", finance_year)
                client.execute(l, OA::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                AppDialogs.hideProgressDialog()
                return false
            }
            return true
        }

        fun draftOA(c: Context, l: ResponseListener): Boolean {

            try {
                val client = RestClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.oaDraft), API.oaDraft.hashCode())
                RequestManager.fillCommons(c, client)
                client.execute(l, OA::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                AppDialogs.hideProgressDialog()
                return false
            }
            return true
        }

        fun getOAMaterial(c: Context, l: ResponseListener, oa_id: String): Boolean {
            try {
                // Generating Req
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.oaMaterial), API.oaMaterial.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("oa_id", oa_id)
                client.execute(l, Material::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun oaApprove(c: Context, l: ResponseListener, oa: OA): Boolean {
            try {
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.oaApprove), API.oaApprove.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("oa_id", oa.id!!)
                client.addParam("project_code", oa.projectId!!)
                client.addParam("project_id", oa.projectId!!)
                client.execute(l, Response::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun oaDocument(c: Context, l: ResponseListener, oa_id: String): Boolean {
            try {
                AppDialogs.showProgressDialog(c, "Downloading OA document...")
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.oaDocument), API.oaDocument.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("oa_id", oa_id)
                client.execute(l, FileResponse::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }


        fun oaUploadDocument(c: Context, l: ResponseListener, document: String): Boolean {
            try {
                AppDialogs.showProgressDialog(c, "Downloading OA document...")
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.oaUploadDocument),
                        API.oaUploadDocument.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("document_uri", document)
                client.execute(l, FileResponse::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }


        fun oaReject(c: Context, l: ResponseListener, oa_id: String, remarks: String): Boolean {
            try {
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.oaReject), API.oaReject.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("oa_id", oa_id)
                client.addParam("remarks", remarks)
                client.execute(l, Response::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }


        fun oaCheckInvoiceCount(c: Context, l: ResponseListener, oa_id: String): Boolean {
            try {
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.oaCheckInvoiceCount),
                        API.oaCheckInvoiceCount.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("oa_id", oa_id)
                client.execute(l, Response::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun invoiceDocument(c: Context, l: ResponseListener, invoiceId: String, type: String = "", regenerate: Boolean = false): Boolean {
            try {
                AppDialogs.showProgressDialog(c, "Downloading invoice document...")
                Log.i("Stores", "Connecting Sales Service to download invoice doc $invoiceId")
                Log.i("invoicepdfparams", "invoice_id $invoiceId, inv_type $type")
                // Generating Req
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.invoiceDocument),
                        API.invoiceDocument.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("invoice_id", invoiceId)
                client.addParam("inv_type", type)
                if (regenerate) {
                    client.addParam("document_regenerate", "true")
                }
                client.execute(l, FileResponse::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun getInvoiceMaterial(c: Context, l: ResponseListener, invoiceId: String): Boolean {
            try {
                Log.i("SalesService", "Getting invoice material for ID: $invoiceId")
                Log.i("SalesService", "API URL: ${RequestManager.constructUrl(API.invoiceMaterial)}")

                // Generating Req
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.invoiceMaterial),
                        API.invoiceMaterial.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("invoice_id", invoiceId)
                client.setTimeout(60) // Set 60 second timeout
                client.execute(l, Invoice::class.java)
            } catch (e: Exception) {
                Log.e("SalesService", "Exception in getInvoiceMaterial", e)
                e.printStackTrace()

                // Send error response to listener instead of just returning false
                val errorResponse = Response()
                errorResponse.requestType = API.invoiceMaterial.hashCode()
                errorResponse.responseCode = 500
                errorResponse.message = "Request Failed"
                errorResponse.customMessage = "Failed to load invoice material: ${e.message}"
                l.onResponse(errorResponse)
                return false
            }
            return true
        }

        fun approve(c: Context, l: ResponseListener, invoiceId: String): Boolean {
            try {
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.approve), API.approve.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("invoice_id", invoiceId)
                client.execute(l, Response::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun reject(c: Context, l: ResponseListener, invoiceId: String, remarks: String): Boolean {
            try {

                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.reject), API.reject.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("invoice_id", invoiceId)
                client.addParam("remarks", remarks)
                client.execute(l, Response::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }


        fun getInvoiceMaterialOverDue(c: Context, l: ResponseListener, party_id: String): Boolean {
            try {
                // Generating Req
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.invoiceMaterialOverDue),
                        API.invoiceMaterialOverDue.hashCode())
                RequestManager.fillCommons(c, client)
                client.addParam("party_id", party_id)
                client.execute(l, Ledger::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun getInvoiceFinanceYear(c: Context, l: ResponseListener): Boolean {
            try {
                AppDialogs.showProgressDialog(c, "Get Invoice finance years...")
                val client = DownloadClient(c, Request.Method.POST,
                        constructUrl(API.invoiceFinanceYear), API.invoiceFinanceYear.hashCode())
                RequestManager.fillCommons(c, client)
                client.execute(l, Invoice::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun getOAFinanceYear(c: Context, l: ResponseListener): Boolean {
            try {
                val client = DownloadClient(c, Request.Method.POST, constructUrl(API.oaFinanceYear),
                        API.oaFinanceYear.hashCode())
                RequestManager.fillCommons(c, client)
                client.execute(l, Invoice::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun oaStatus(c: Context, l: ResponseListener, since: String = "", till: String = ""): Boolean {
            try {
                // Generating Req
                val client = DownloadClient(c, Request.Method.POST,
                        RequestManager.constructUrl(API.oaStatus), API.oaStatus.hashCode())
                RequestManager.fillCommons(c, client)
                if (since.isNotEmpty()) {
                    client.addParam("since", since)
                }
                if (till.isNotEmpty()) {
                    client.addParam("till", till)
                }
                client.execute(l, SalesDashboardData::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }

        fun getMaterialProfilePrice(c: Context, l: ResponseListener,
                                    partyId: String, itemId: String, makeId:String): Boolean {
            try {
                val client = DownloadClient(c, Request.Method.POST,
                        constructUrl(API.materialProfilePrice),
                        API.materialProfilePrice.hashCode())
                fillCommons(c, client)
                client.addParam("party_id", partyId)
                client.addParam("item_id", itemId)
                client.addParam("make_id", makeId)
                client.execute(l, Material::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
            return true
        }
    }
}
