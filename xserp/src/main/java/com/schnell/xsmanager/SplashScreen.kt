package com.schnell.xsmanager

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.google.android.material.snackbar.Snackbar
import com.schnell.http.Response
import com.schnell.http.ResponseListener
import com.schnell.util.DateUtil
import com.schnell.util.Utility
import com.schnell.widget.AppDialogs
import com.schnell.xsmanager.attention.NotificationUtils
import com.schnell.xsmanager.model.User
import com.schnell.xsmanager.model.VersionInfo
import com.schnell.xsmanager.webservice.UserService
import kotlinx.android.synthetic.main.activity_splash.*
import java.util.*


/**
 * @since 21/2/17.
 * Launcher activity to decide which screen to launch then
 */

class SplashScreen : Activity(), ResponseListener {

    private var a: Activity? = null
    private var timeoutHandler: Handler? = null
    private var timeoutRunnable: Runnable? = null

    /**
     * Called when the activity is first created.
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)
        a = this
        Log.v("oooo","==="+AppPreference.getSkipFlag(c = this, key = "skip")!!)
    }

    override fun onStart() {
        super.onStart()
        /*
         * New Handler to start the Menu-Activity and close this Splash-Screen after some seconds.
         */
        if (intent.getStringExtra("title") != null) {
            AppDatabase.getInstance(this).deleteNotification(intent.getStringExtra("title")!!)
            NotificationUtils.notificationManager?.cancelAll()
        }

        // Set up timeout mechanism (10 seconds)
        timeoutHandler = Handler(Looper.getMainLooper())
        timeoutRunnable = Runnable {
            Log.w("SplashScreen", "Version check timeout, proceeding to main app")
            Snackbar.make(imageButton, "Connection timeout. Proceeding without version check.",
                    Snackbar.LENGTH_SHORT).show()
            checkAppNavigationStatus(a!!)
            finish()
        }

        Handler(Looper.getMainLooper()).postDelayed({
            if (!UserService.getVersionInfo(c = a!!)) {
                Snackbar.make(imageButton, getString(R.string.may_not_work),
                        Snackbar.LENGTH_LONG).show()
                Handler(Looper.getMainLooper()).postDelayed({
                    checkAppNavigationStatus(a!!)
                    finish()
                }, 3000)
            } else {
                // Start timeout timer only if version check was initiated successfully
                timeoutHandler?.postDelayed(timeoutRunnable!!, 10000) // 10 second timeout
            }
        }, 200)
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clean up timeout handler
        timeoutHandler?.removeCallbacks(timeoutRunnable!!)
    }

    companion object {

        fun checkAppNavigationStatus(context: Context?) {
            val user: User? = AppPreference.getLoginUser(context)
            if (user?.username != null && user.username!!.isNotEmpty()) {
                context!!.startActivity(Intent(context, XSDashboard::class.java))
            } else {
                if(!AppPreference.getSkipFlag(c = context!!, key = "skip")!!)
                {
                    context!!.startActivity(Intent(context, AppPreviewScreen::class.java))
                }else{
                    context!!.startActivity(Intent(context, LoginScreen::class.java))
                }

            }
        }
    }

    fun updateApp() {
        try {
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName")))
        } catch (e: android.content.ActivityNotFoundException) {
            startActivity(Intent(Intent.ACTION_VIEW,
                    Uri.parse("https://play.google.com/store/apps/details?id=$packageName")))
        }

    }

    override fun onResponse(r: Response?) {
        try {
            // Cancel timeout since we received a response
            timeoutHandler?.removeCallbacks(timeoutRunnable!!)

            AppDialogs.hideProgressDialog()
            if (r == null) {
                Log.w("SplashScreen", "Received null response, proceeding to main app")
                checkAppNavigationStatus(a)
                finish()
                return
            }

            // Handle network errors and server failures
            if (r.customMessage == getString(R.string.server_failed_msg) ||
                r.customMessage?.contains("Network connection failed") == true ||
                r.customMessage?.contains("Unable to resolve host") == true ||
                r.responseCode == 444) {
                Log.w("SplashScreen", "Network error detected: ${r.customMessage}, proceeding to main app")
                Snackbar.make(imageButton, "Network error. App may not work properly without internet.",
                        Snackbar.LENGTH_LONG).show()
                Handler(Looper.getMainLooper()).postDelayed({
                    checkAppNavigationStatus(a)
                    finish()
                }, 2000)
                return
            }

            if (r.requestType == UserService.API.versionInfo.hashCode()) {
                // Check if this is actually a VersionInfo response or an error response
                if (r !is VersionInfo || !r.isSuccess) {
                    Log.w("SplashScreen", "Version check failed or invalid response, proceeding to main app")
                    checkAppNavigationStatus(a)
                    finish()
                    return
                }

                val versionInfo = r as VersionInfo
                Log.d("xserp-versionInfo", "API Version: ${versionInfo.version} , BUILD Version: ${BuildConfig.VERSION_NAME}")
                when {
                    BuildConfig.VERSION_NAME == versionInfo.version -> {
                        checkAppNavigationStatus(a)
                        finish()
                    }
                    versionInfo.forceUpdate -> {
                        val l = object : AppDialogs.OptionListener {
                            override fun yes() {
                                updateApp()
                            }

                            override fun no() {
                                finish()
                            }
                        }
                        AppDialogs.optionalAction(
                                c = this, text = getString(R.string.force_update_text), l = l,
                                no = getString(R.string.exit), yes = getString(R.string.update_now),
                                isCancelable = false)
                    }
                    else -> {
                        val date = AppPreference[a!!, "reminder_date_time", ""]
                        if (date == DateUtil.formatDisplayDate(Date())) {
                            checkAppNavigationStatus(a)
                            finish()
                        } else {
                            val l = object : AppDialogs.OptionListener {
                                override fun yes() {
                                    updateApp()
                                }

                                override fun no() {
                                    AppPreference.put(a!!, "reminder_date_time",
                                            DateUtil.formatDisplayDate(Date())!!)
                                    checkAppNavigationStatus(a)
                                    finish()
                                }
                            }
                            AppDialogs.optionalAction(
                                    c = this, text = getString(R.string.recommended_update_text),
                                    l = l, no = getString(R.string.remind_later),
                                    yes = getString(R.string.update_now), isCancelable = false)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("SplashScreen", "Exception in onResponse", e)
            e.printStackTrace()
            // If there's any exception, proceed to main app to avoid getting stuck
            checkAppNavigationStatus(a)
            finish()
        }
    }
}