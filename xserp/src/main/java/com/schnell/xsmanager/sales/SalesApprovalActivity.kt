package com.schnell.xsmanager.sales


import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.snackbar.Snackbar
import com.schnell.http.FileResponse
import com.schnell.http.Response
import com.schnell.http.ResponseListener
import com.schnell.util.Utility
import com.schnell.widget.AppDialogs
import com.schnell.xsmanager.AppPreference
import com.schnell.xsmanager.R
import com.schnell.xsmanager.model.Invoice
import com.schnell.xsmanager.model.OA
import com.schnell.xsmanager.webservice.SalesService
import kotlinx.android.synthetic.main.fragment_audit_tab_details.*
import java.io.File


class SalesApprovalActivity : AppCompatActivity(), ResponseListener {


    private lateinit var self: SalesApprovalActivity
    var position: Int? = 0
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        self = this
        setContentView(R.layout.fragment_audit_tab_details)
        toolbar.visibility = View.VISIBLE
        setSupportActionBar(toolbar)
        supportActionBar!!.setDisplayHomeAsUpEnabled(true)
        supportActionBar!!.setDisplayShowHomeEnabled(true)
        if (!isOA) {
            viewPager.adapter = InvoicePagerAdapter(this, list = invoiceList, activity = this)
            supportActionBar!!.title = getString(R.string.invoice_approval)
        } else {
            viewPager.adapter = OAPagerAdapter(this, list = oaList, activity = this)
            supportActionBar!!.title = getString(R.string.oa_title)
        }
        viewPager.currentItem = itemPosition!!

    }

    fun openFile(_id: String?, type: String = "", status: Int = 0, inWhich: Int = 1) {
        try {
            var fileName = _id
            fileName = fileName!!.replace("/", "-")
            val user = AppPreference.getLoginUser(this)
            val module = when (inWhich) {
                OA -> "oa"
                INVOICE -> "invoice"
                else -> "oa-attachment"
            }
            val filename = "${Environment.getExternalStorageDirectory()}/XSerp/$module-${user.enterpriseId}-$fileName.pdf"
            val file = File(filename)
            if (file.exists()) {
                val listener = object : AppDialogs.OptionListener {
                    override fun yes() {
                        getFile(_id = _id, type = type, inWhich = inWhich, regenerate = true)
                    }

                    override fun no() {
                        Utility.openFile(this@SalesApprovalActivity, file)
                    }
                }
                AppDialogs.optionalAction(c = this,
                        text = getString(R.string.confirm_download_title), l = listener,
                        no = getString(R.string.open_it),
                        yes = if (status == 0) getString(R.string.download_again) else getString(
                                R.string.regenerate))
            } else {
                getFile(_id = _id, type = type, inWhich = inWhich)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getFile(_id: String?, type: String = "", inWhich: Int = 1, regenerate: Boolean = false) {
        when (inWhich) {
            OA -> SalesService.oaDocument(this@SalesApprovalActivity, self, _id!!)
            INVOICE -> SalesService.invoiceDocument(this@SalesApprovalActivity, self, _id!!,
                    type = type, regenerate = regenerate)
            else -> SalesService.oaUploadDocument(this@SalesApprovalActivity, self,
                    document = _id!!)
        }
    }

    override fun onResponse(r: Response?) {
        try {
            AppDialogs.hideProgressDialog()
            if (r == null) {
                val errorMsg = "Network request failed - no response received. Please check your internet connection and try again."
                Log.e("SalesApprovalActivity", errorMsg)
                showError(errorMsg)
                return
            }

            Log.i("SalesApprovalActivity", "Response received - Code: ${r.responseCode}, Message: ${r.message}, Custom: ${r.customMessage}")

            if (r.customMessage == getString(R.string.server_failed_msg)) {
                Utility.serverError(r = r, v = viewPager, context = this)
                return
            }

            // Handle non-success responses with detailed error messages
            if (!r.isSuccess) {
                val errorMsg = when {
                    r.customMessage?.isNotEmpty() == true -> r.customMessage!!
                    r.message?.isNotEmpty() == true -> r.message!!
                    else -> "Server returned error code: ${r.responseCode}"
                }
                Log.e("SalesApprovalActivity", "API Error: $errorMsg")
                showError(errorMsg)
                return
            }

            if (r.requestType == SalesService.API.invoiceDocument.hashCode()) {
                val f = r as? FileResponse
                if (f?.filename != null) {
                    Log.d("SalesApprovalActivity", "Opening invoice document: ${f.filename}")
                    Utility.openFile(this, File(f.filename!!))
                } else {
                    Log.e("SalesApprovalActivity", "Invoice document filename is null")
                    showError("Failed to download invoice document")
                }
            }
            if (r.requestType == SalesService.API.oaDocument.hashCode()) {
                val f = r as? FileResponse
                if (f?.filename != null) {
                    Log.d("SalesApprovalActivity", "Opening OA document: ${f.filename}")
                    Utility.openFile(this, File(f.filename!!))
                } else {
                    Log.e("SalesApprovalActivity", "OA document filename is null")
                    showError("Failed to download OA document")
                }
            }
            if (r.requestType == SalesService.API.oaUploadDocument.hashCode()) {
                val f = r as? FileResponse
                if (f?.filename != null) {
                    Log.d("SalesApprovalActivity", "Opening OA upload document: ${f.filename}")
                    Utility.openFile(this, File(f.filename!!))
                } else {
                    Log.e("SalesApprovalActivity", "OA upload document filename is null")
                    showError("Failed to download OA upload document")
                }
            }
        } catch (e: Exception) {
            Log.e("SalesApprovalActivity", "Exception in onResponse", e)
            e.printStackTrace()
            showError("Error processing response: ${e.message}")
        }
    }


    override fun onBackPressed() {
        val intent = Intent()
        intent.putExtra("sales", "sales")
        setResult(Activity.RESULT_OK, intent)
        finish()
        super.onBackPressed()
    }

    fun showError(message: String?) {
        Snackbar.make(viewPager!!, message!!.toString(), Snackbar.LENGTH_LONG).show()
    }

    companion object {
        var invoiceList: MutableList<Invoice>? = null
        var oaList: MutableList<OA>? = null
        var DRAFT: Int? = 0
        var APPROVED: Int? = 2
        var REVIWED: Int? = 1
        var itemPosition: Int? = 0
        var REJCTED: Int? = -1
        var isOA = false
        var INVOICE: Int? = 1
        var OA: Int? = 2
        var UPLOAD_DOCUMENT: Int? = 3
        fun invoiceList(list: MutableList<Invoice>?, position: Int? = 0, isOA: Boolean = false) {
            this.invoiceList = list
            this.itemPosition = position
            this.isOA = isOA
        }

        fun oaList(list: MutableList<OA>?, position: Int? = 0, isOA: Boolean = true) {
            this.oaList = list
            this.itemPosition = position
            this.isOA = isOA
        }
    }
}
