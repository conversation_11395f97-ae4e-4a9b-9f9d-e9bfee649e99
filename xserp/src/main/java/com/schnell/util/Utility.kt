package com.schnell.util

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.text.Html
import android.text.Spanned
import android.util.Base64
import android.util.DisplayMetrics
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.PopupMenu
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.text.HtmlCompat
import com.google.android.material.snackbar.Snackbar
import com.schnell.http.Response
import com.schnell.xsmanager.R
import com.schnell.xsmanager.model.Ledger
import com.schnell.xsmanager.model.Receipt
import com.schnell.xsmanager.model.SelectionItem
import java.io.*
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DateFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.roundToInt

/**
 * @since 27/2/17.
 * General utility across the app
 */

object Utility {
    private val df = DecimalFormat("#.###")
    private val dfSingle = DecimalFormat("#.#")
    private val dfMoney = DecimalFormat("#,##,##,##,##,##,##,###.00")
    private val dfMoneyEntry = DecimalFormat("################.0")
    private val dfFiveDecimal = DecimalFormat("#,##,##,##,##,##,##,###.00000")
    private val dfFive_decimal = DecimalFormat("################.00000")
    const val SURL = "https://www.payumoney.com/mobileapp/payumoney/success.php"
    const val FURL = "https://www.payumoney.com/mobileapp/payumoney/failure.php"
    fun formatDecimal(value: Double): String {
        return df.format(value)
    }

    fun formatDecimalToSingleDigit(value: Double): String {
        return dfSingle.format(value)
    }

    fun round(value: Double, places: Int): Double {
        if (places < 0) throw IllegalArgumentException()

        var bd = BigDecimal(value)
        bd = bd.setScale(places, RoundingMode.HALF_UP)
        return bd.toDouble()
    }

    fun formatDecimalToMoney(value: Double): String {
        return if (value == 0.0) "0.00" else dfMoney.format(value)
    }

    fun formatDecimalToMoneyFiveDecimal(value: Double): String {
        return if (value == 0.0) "0.00000" else dfFiveDecimal.format(value)
    }

    fun formatDecimalToFiveDecimal(value: Double): String {
        return if (value == 0.0) "0.00000" else dfFive_decimal.format(value)
    }

    fun formatDecimalToMoneyEntry(value: Double): String {
        return if (value == 0.0) "0.0" else dfMoneyEntry.format(value)
    }

    fun isInternetAvailable(context: Context?): Boolean {
        try {
            val cm = context!!.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val netInfo = cm.activeNetworkInfo
            if (netInfo != null && netInfo.isConnected) {
                return true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return false
    }

    /**
     * The build is with or after Marshmallow then this method call is must before using the permission the we need
     */
    fun checkStoragePermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // The permission check is available only after Marshmallow
            if (ContextCompat.checkSelfPermission(activity,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {

                // Should we show an explanation?
                if (ActivityCompat.shouldShowRequestPermissionRationale(activity,
                                Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                    // Show an explanation to the user *asynchronously* -- don't block
                    // this thread waiting for the user's response! After the user
                    // sees the explanation, try again to request the permission.
                    Log.d("XSERP", "Permission request for writing in internal storage")
                } else {
                    // No explanation needed, we can request the permission.
                    ActivityCompat.requestPermissions(activity,
                            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
                                    Manifest.permission.WRITE_EXTERNAL_STORAGE),
                            102)

                }
            }
        }
    }

    /**
     * The build is with or after Marshmallow then this method call is must before using the permission the we need
     */
    fun checkCameraPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // The permission check is available only after Marshmallow
            if (ContextCompat.checkSelfPermission(activity,
                            Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {

                // Should we show an explanation?
                if (ActivityCompat.shouldShowRequestPermissionRationale(activity,
                                Manifest.permission.CAMERA)) {
                    // Show an explanation to the user *asynchronously* -- don't block
                    // this thread waiting for the user's response! After the user
                    // sees the explanation, try again to request the permission.
                    Log.d("XSERP", "Permission request for writing in internal storage")
                } else {
                    // No explanation needed, we can request the permission.
                    ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.CAMERA),
                            102)
                }
            }
        }
    }

    /**
     * Opens file with system app
     *
     * @param c Context object
     * @param file File object
     */
    fun openFile(c: Context, file: File) {
        try {
            Log.d("Utility", "Opening file: ${file.absolutePath}")
            Log.d("Utility", "File exists: ${file.exists()}")
            Log.d("Utility", "File size: ${file.length()} bytes")
            Log.d("Utility", "File parent directory: ${file.parent}")
            Log.d("Utility", "File name: ${file.name}")

            val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                try {
                    FileProvider.getUriForFile(c, c.applicationContext.packageName + ".provider", file)
                } catch (e: IllegalArgumentException) {
                    Log.e("Utility", "FileProvider error, falling back to Uri.fromFile", e)
                    Uri.fromFile(file)
                }
            } else {
                Uri.fromFile(file)
            }

            Log.d("Utility", "Generated URI: $uri")

            val intent = Intent(Intent.ACTION_VIEW)

            // Set the appropriate MIME type based on file extension
            val mimeType = when {
                file.name.lowercase().endsWith(".pdf") -> "application/pdf"
                file.name.lowercase().endsWith(".jpg") || file.name.lowercase().endsWith(".jpeg") -> "image/jpeg"
                file.name.lowercase().endsWith(".png") -> "image/png"
                file.name.lowercase().endsWith(".gif") -> "image/gif"
                file.name.lowercase().endsWith(".doc") -> "application/msword"
                file.name.lowercase().endsWith(".docx") -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                file.name.lowercase().endsWith(".xls") -> "application/vnd.ms-excel"
                file.name.lowercase().endsWith(".xlsx") -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                else -> "*/*"
            }

            intent.setDataAndType(uri, mimeType)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            Log.d("Utility", "Intent MIME type: $mimeType")
            Log.d("Utility", "Starting intent with URI: $uri")

            // Check if there's an app that can handle this intent
            val packageManager = c.packageManager
            val resolveInfo = intent.resolveActivity(packageManager)

            if (resolveInfo != null) {
                Log.d("Utility", "Found app to handle intent")
                c.startActivity(intent)
                Log.d("Utility", "Intent started successfully")
            } else {
                Log.d("Utility", "No direct app found, trying chooser")
                // If no app can handle the specific MIME type, try with a generic chooser
                try {
                    val chooserIntent = Intent.createChooser(intent, "Open with")
                    chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    c.startActivity(chooserIntent)
                    Log.d("Utility", "Chooser intent started")
                } catch (e: ActivityNotFoundException) {
                    Log.e("Utility", "No activity found for chooser", e)
                    // Try with a generic intent as last resort
                    tryGenericFileOpen(c, file, uri)
                }
            }

        } catch (e: Exception) {
            Log.e("Utility", "Error opening file: ${file.absolutePath}", e)
            Log.e("Utility", "File exists: ${file.exists()}")
            Log.e("Utility", "File size: ${file.length()}")
            Log.e("Utility", "File readable: ${file.canRead()}")
            Log.e("Utility", "Exception type: ${e.javaClass.simpleName}")
            Log.e("Utility", "Exception details: ${e.message}")
            e.printStackTrace()

            // Always show an error message
            val errorMessage = when {
                !file.exists() -> "File not found: ${file.name}"
                file.length() == 0L -> "Downloaded file is empty: ${file.name}"
                !file.canRead() -> "Cannot read file: ${file.name}"
                e is SecurityException -> "Permission denied to open file: ${file.name}"
                e is ActivityNotFoundException -> "No app found to open PDF files. Please install a PDF viewer."
                file.name.lowercase().endsWith(".pdf") -> "Unable to open PDF: ${e.message}"
                else -> "Unable to open file: ${e.message}"
            }

            // Force show toast on UI thread
            try {
                if (c is Activity) {
                    c.runOnUiThread {
                        Toast.makeText(c, errorMessage, Toast.LENGTH_LONG).show()
                    }
                } else {
                    Toast.makeText(c, errorMessage, Toast.LENGTH_LONG).show()
                }
            } catch (toastException: Exception) {
                Log.e("Utility", "Failed to show toast", toastException)
            }
        }
    }

    /**
     * Fallback method to try opening file with generic intent
     */
    private fun tryGenericFileOpen(c: Context, file: File, uri: Uri) {
        try {
            Log.d("Utility", "Trying generic file open")
            val genericIntent = Intent(Intent.ACTION_VIEW)
            genericIntent.setDataAndType(uri, "*/*")
            genericIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            genericIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            val chooser = Intent.createChooser(genericIntent, "Open file with")
            chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            c.startActivity(chooser)
            Log.d("Utility", "Generic chooser started")
        } catch (e: Exception) {
            Log.e("Utility", "Generic file open also failed", e)
            // Show file location to user as last resort
            val message = "File downloaded to: ${file.absolutePath}\nPlease open manually with a file manager."
            if (c is Activity) {
                c.runOnUiThread {
                    Toast.makeText(c, message, Toast.LENGTH_LONG).show()
                }
            } else {
                Toast.makeText(c, message, Toast.LENGTH_LONG).show()
            }
        }
    }

    fun getExpenseHeadLegerId(expenseList: List<Ledger>, type: String): Int {
        for (ex in expenseList) {
            if (type == ex.name) {
                return ex.id!!
            }
        }
        return -1
    }

    fun disableSubView(linearLayout: LinearLayout, btn: Button) {
        for (view in linearLayout.touchables) {
            if (view is EditText) {
                view.isEnabled = false
                view.isFocusable = false
                view.isFocusableInTouchMode = false
            }
        }
        btn.isEnabled = false
    }

    fun disableRootView(v: View) {
        for (view in v.touchables) {
            view.isEnabled = false
            view.isFocusable = false
            view.isFocusableInTouchMode = false
        }

    }

    fun checkPermission(a: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(a, permission) == PackageManager.PERMISSION_GRANTED
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    fun getPath(context: Context, uri: Uri): String? {

        val isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT

        // DocumentProvider
        if (isKitKat && DocumentsContract.isDocumentUri(context, uri)) {
            // ExternalStorageProvider
            if (isExternalStorageDocument(uri)) {
                val docId = DocumentsContract.getDocumentId(uri)
                val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                val type = split[0]

                if ("primary".equals(type, ignoreCase = true)) {
                    return Environment.getExternalStorageDirectory().toString() + "/" + split[1]
                }


            } else if (isDownloadsDocument(uri)) {

                val id = DocumentsContract.getDocumentId(uri)
                val contentUri = ContentUris.withAppendedId(
                        Uri.parse("content://downloads/public_downloads"),
                        java.lang.Long.valueOf(id))

                return getDataColumn(context, contentUri, null, null)
            } else if (isMediaDocument(uri)) {
                val docId = DocumentsContract.getDocumentId(uri)
                val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                val type = split[0]

                var contentUri: Uri? = null
                when (type) {
                    "image" -> contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                    "video" -> contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                    "audio" -> contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                }

                val selection = "_id=?"
                val selectionArgs = arrayOf(split[1])

                return getDataColumn(context, contentUri, selection, selectionArgs)
            }// MediaProvider
            // DownloadsProvider
        } else if ("content".equals(uri.scheme, ignoreCase = true)) {

            // Return the remote address
            return if (isGooglePhotosUri(uri)) uri.lastPathSegment else getDataColumn(context, uri,
                    null, null)

        } else if ("file".equals(uri.scheme, ignoreCase = true)) {
            return uri.path
        }// File
        // MediaStore (and general)

        return null
    }

    fun getDataColumn(context: Context, uri: Uri?, selection: String?,
                      selectionArgs: Array<String>?): String? {

        var cursor: Cursor? = null
        val column = "_data"
        val projection = arrayOf(column)

        try {
            cursor = context.contentResolver.query(uri!!, projection, selection, selectionArgs,
                    null)
            if (cursor != null && cursor.moveToFirst()) {
                val index = cursor.getColumnIndexOrThrow(column)
                return cursor.getString(index)
            }
        } finally {
            cursor?.close()
        }
        return null
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is ExternalStorageProvider.
     */
    fun isExternalStorageDocument(uri: Uri): Boolean {
        return "com.android.externalstorage.documents" == uri.authority
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is DownloadsProvider.
     */
    fun isDownloadsDocument(uri: Uri): Boolean {
        return "com.android.providers.downloads.documents" == uri.authority
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is MediaProvider.
     */
    fun isMediaDocument(uri: Uri): Boolean {
        return "com.android.providers.media.documents" == uri.authority
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is Google Photos.
     */
    fun isGooglePhotosUri(uri: Uri): Boolean {
        return "com.google.android.apps.photos.content" == uri.authority
    }

    fun encodeFileToBase64Binary(filePath: String): String? {
        var encodedfile: String? = null
        try {
            val file = File(filePath)
            val fileInputStreamReader = FileInputStream(file)
            val bytes = ByteArray(file.length().toInt())
            fileInputStreamReader.read(bytes)
            encodedfile = Base64.encodeToString(bytes, Base64.DEFAULT)
        } catch (e: FileNotFoundException) {
            // TODO Auto-generated catch <span id="IL_AD1" class="IL_AD">block</span>
            e.printStackTrace()
        } catch (e: IOException) {
            // TODO Auto-generated catch block
            e.printStackTrace()
        }

        return encodedfile
    }

    fun showError(message: String?, v: View) {
        Snackbar.make(v, message!!.toString(), Snackbar.LENGTH_LONG).show()
    }

    /**
     * Created a common filter
     */
    fun setFilter(grnList: ArrayList<Receipt>, filteredList: ArrayList<Receipt>, filters: List<SelectionItem>): HashMap<String, SelectionItem> {
        filteredList.clear()
        val map = HashMap<String, SelectionItem>()
        for (grn in grnList) {
            var isAdded = false
            for (filter in filters) {
                if (filter.selected) {
                    if (filter.text == grn.supplierName) {
                        isAdded = true
                        filteredList.add(grn)
                    }
                }
                if (filter.text == grn.supplierName) {
                    filter.title = null
                    map[filter.text] = filter
                }
            }
            if (map[grn.supplierName] == null) {
                map[grn.supplierName!!] = SelectionItem(grn.supplierName!!)
            }
            if (!isAdded && map[grn.supplierName!!]!!.selected) {
                filteredList.add(grn)
            }
        }
        return map
    }

    /**
     * sort the list
     */
    fun sortList(filteredList: ArrayList<Receipt>, index: Int = 1) {
        Collections.sort(filteredList, object : Comparator<Receipt> {
            @SuppressLint("SimpleDateFormat")
            var f = SimpleDateFormat("dd-MM-yyyy") as DateFormat

            override fun compare(o1: Receipt, o2: Receipt): Int = when (index) {
                0 -> o1.supplierName!!.compareTo(o2.supplierName!!)
                1 -> f.parse(o1.receiptDate).compareTo(f.parse(o2.receiptDate))
                2 -> f.parse(o1.invoiceDate).compareTo(f.parse(o2.invoiceDate))
                3 -> o1.invoiceValue.compareTo(o2.invoiceValue)
                else -> 0
            }
        })
    }

    fun getTimeDifference(date1: Date, date2: Date): Array<Int> {
        var different = date1.time - date2.time
        var isNegative = 0;
        if (different < 0) {
            different *= -1
            isNegative = 1
        }
        val secondsInMilli: Long = 1000
        val minutesInMilli = secondsInMilli * 60
        val hoursInMilli = minutesInMilli * 60
        val daysInMilli = hoursInMilli * 24
        val days = (different / daysInMilli).toInt()
        different = (different % daysInMilli)
        val hours = (different / hoursInMilli).toInt()
        different = (different % hoursInMilli)
        val minutes = (different / minutesInMilli).toInt()
        different = (different % minutesInMilli)
        val seconds = (different / secondsInMilli).toInt()
        return arrayOf(days, hours, minutes, seconds, isNegative)
    }

    @Suppress("DEPRECATION")
    fun spannedHtml(big: String, small: String): Spanned? {
        val str = "<big>$big</big>$small"
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Html.fromHtml(str, HtmlCompat.FROM_HTML_MODE_LEGACY)
        } else {
            Html.fromHtml(str)
        }
    }

    fun getDeviceWidth(activity: Activity): Int {
        val displayMetrics = DisplayMetrics()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            activity.windowManager.currentWindowMetrics
        } else {
            activity.windowManager.getDefaultDisplay().getMetrics(displayMetrics)
        }
        return displayMetrics.widthPixels
    }

    fun hideKeyboard(view: View, c: Context) {
        val inputMethodManager = c.getSystemService(
                Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val heightRatio = (height.toFloat() / reqHeight.toFloat()).roundToInt()
            val widthRatio = (width.toFloat() / reqWidth.toFloat()).roundToInt()
            inSampleSize = if (heightRatio < widthRatio) heightRatio else widthRatio
        }
        val totalPixels = (width * height).toFloat()
        val totalReqPixelsCap = (reqWidth * reqHeight * 100).toFloat()
        while (totalPixels / (inSampleSize * inSampleSize) > totalReqPixelsCap) {
            inSampleSize++
        }

        return inSampleSize
    }

    fun getFileType(fileName: String): Boolean {
        val imageType = arrayOf("jpg", "png", "gif", "jpeg")
        for (type in imageType) {
            if (fileName.toLowerCase(Locale.getDefault()).endsWith(type)) {
                return true
            }
        }
        return false
    }

    fun compressImage(context: Context, filePath: String?, fileName: String): String? {
        val MAX_IMAGE_SIZE = 700 * 1024 // max final file size in kilobytes

        // First decode with inJustDecodeBounds=true to check dimensions of image
        val options: BitmapFactory.Options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(filePath, options)

        // Calculate inSampleSize(First we are going to resize the image to 800x800 image, in order to not have a big but very low quality image.
        //resizing the image will already reduce the file size, but after resizing we will check the file size and start to compress image
        options.inSampleSize = calculateInSampleSize(options, 800, 800)

        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false
        options.inPreferredConfig = Bitmap.Config.ARGB_8888
        val bmpPic = BitmapFactory.decodeFile(filePath, options)
        var compressQuality = 100 // quality decreasing by 5 every loop.
        var streamLength: Int
        do {
            val bmpStream = ByteArrayOutputStream()
            bmpPic.compress(Bitmap.CompressFormat.JPEG, compressQuality, bmpStream)
            val bmpPicByteArray: ByteArray = bmpStream.toByteArray()
            streamLength = bmpPicByteArray.size
            compressQuality -= 5
        } while (streamLength >= MAX_IMAGE_SIZE)
        try {
            //save the resized and compressed file to disk cache
            val bmpFile = FileOutputStream(
                    context.cacheDir.toString() + fileName)
            bmpPic.compress(Bitmap.CompressFormat.JPEG, compressQuality, bmpFile)
            bmpFile.flush()
            bmpFile.close()
        } catch (e: java.lang.Exception) {
        }
        //return the path of resized and compressed file
        return context.cacheDir.toString() + fileName
    }

    /**
     * Set to display custom Snackbar
     */
    private fun showCustomSnackBar(v: View, context: Context, icon: Int, message: String?) {
        val snackbar: Snackbar = Snackbar.make(v, message!!, Snackbar.LENGTH_LONG)
        val snackbarLayout = snackbar.view
        val textView = snackbarLayout.findViewById<View>(
                com.google.android.material.R.id.snackbar_text) as TextView
        textView.gravity = Gravity.CENTER_VERTICAL
        textView.setCompoundDrawablesWithIntrinsicBounds(icon, 0, 0, 0)
        textView.compoundDrawablePadding = context.resources.getDimensionPixelOffset(
                R.dimen.viewpager_padding)
        snackbar.show()
    }

    fun serverError(r: Response, v: View, context: Context) {
        when (r.responseCode) {
            404 -> {
                showCustomSnackBar(v, context, R.drawable.ic_maintenance_icon,
                        context.resources.getString(R.string.server_down_msg))
            }
            500 -> {
                showCustomSnackBar(v, context, R.drawable.ic_server_error,
                        context.resources.getString(R.string.server_error_msg))
            }
            else -> {
                showCustomSnackBar(v, context, R.drawable.server_not_found_error,
                        context.resources.getString(R.string.server_not_found_msg))
            }
        }
    }

    fun readBytes(context: Context, uri: Uri): ByteArray {
        val inputStream = context.contentResolver.openInputStream(uri)
        val byteBuffer = ByteArrayOutputStream()
        val bufferSize = 1024
        val buffer = ByteArray(bufferSize)
        var len = inputStream!!.read(buffer)
        while (len != -1) {
            byteBuffer.write(buffer, 0, len)
            len = inputStream.read(buffer)
        }
        return byteBuffer.toByteArray()
    }

    fun getCurrency(currencyId: String, context: Context): String {
        return when (currencyId) {
            "2" -> context.resources.getString(R.string.us)
            "3" -> context.resources.getString(R.string.eu)
            else -> context.resources.getString(R.string.rs)
        }
    }

    fun showHSNDefaultItems(context: Context,view: EditText){
        val popupMenu = PopupMenu(context,view)
        popupMenu.menuInflater.inflate(R.menu.menu_hsn_list,popupMenu.menu)
        popupMenu.setOnMenuItemClickListener { item ->
            view.setText(item.toString())
            true
        }
        popupMenu.show()
    }


}
