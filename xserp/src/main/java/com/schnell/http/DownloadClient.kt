package com.schnell.http

import android.content.Context
import android.os.Environment
import android.util.Log
import com.android.volley.AuthFailureError
import com.android.volley.DefaultRetryPolicy
import com.android.volley.NetworkResponse
import com.android.volley.Request
import com.android.volley.toolbox.HttpHeaderParser
import com.android.volley.toolbox.HurlStack
import com.android.volley.toolbox.Volley
import com.schnell.util.Utility
import java.io.*
import java.lang.reflect.Type
import java.net.ConnectException
import java.net.HttpURLConnection
import java.net.URL


/**
 * @since 23/2/17.
 * Wrapper class for handling the http request using Volley library
 */

class DownloadClient
/**
 * @param c   Context of Application
 * @param m   Request method [Request.Method].Method.POST / others from Request.Method.*
 * @param url Url of the request
 * @param rt  Unique request type from constants. This param may be deprecated in future release.
 */
(c: Context, m: Int, url: String, rt: Int) : VolleyClient(c, m, url, rt), com.android.volley.Response.Listener<ByteArray> {

    init {
        setTimeout(120)
    }


    override fun onResponse(response: ByteArray) {
        try {
            if (l != null) {
                val r: Response?
                var filename: String? = mHeaders["Content-Disposition"]
                if (filename != null) {
                    val fr = FileResponse()
                    r = fr
                    r.requestType = requestType

                    Log.d("DownloadClient", "Raw Content-Disposition: $filename")

                    // Parse filename from Content-Disposition header
                    filename = try {
                        when {
                            filename.contains("filename*=") -> {
                                // Handle RFC 5987 encoded filenames
                                val encodedPart = filename.substringAfter("filename*=").trim()
                                if (encodedPart.contains("''")) {
                                    encodedPart.substringAfter("''")
                                } else {
                                    encodedPart
                                }
                            }
                            filename.contains("filename=") -> {
                                // Handle standard filename parameter
                                val filenamePart = filename.substringAfter("filename=").trim()
                                if (filenamePart.startsWith("\"") && filenamePart.endsWith("\"")) {
                                    filenamePart.substring(1, filenamePart.length - 1)
                                } else {
                                    filenamePart
                                }
                            }
                            else -> {
                                // Fallback: split by = and take the last part
                                filename.split("=".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray().last().trim()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("DownloadClient", "Error parsing filename from Content-Disposition", e)
                        "document_${System.currentTimeMillis()}.pdf"
                    }

                    // Clean up filename for filesystem
                    filename = filename?.replace(":", ".")
                        ?.replace("\"", "")
                        ?.replace("/", "-")
                        ?.replace("\\", "-")
                        ?.replace("|", "-")
                        ?.replace("?", "")
                        ?.replace("*", "")
                        ?.replace("<", "")
                        ?.replace(">", "")
                        ?.replace("(", "_")  // Replace parentheses with underscores
                        ?.replace(")", "_")
                        ?.replace("[", "_")  // Also handle square brackets
                        ?.replace("]", "_")
                        ?.replace("{", "_")  // And curly brackets
                        ?.replace("}", "_")
                        ?.replace(" ", "_")  // Replace spaces with underscores
                        ?.trim() ?: "document_${System.currentTimeMillis()}.pdf"

                    Log.d("DownloadClient", "Parsed filename: $filename")

                    try {
                        // covert response to input stream
                        val input = ByteArrayInputStream(response)

                        //Create a file on desired path and write stream data to it
                        val path = File(Environment.getExternalStorageDirectory(), "XSerp")
                        if (!path.exists()) {
                            val created = path.mkdirs()
                            Log.d("DownloadClient", "Directory created: $created, Path: ${path.absolutePath}")
                        }

                        val file = File(path, filename)
                        fr.filename = file.absolutePath

                        Log.d("DownloadClient", "Saving file to: ${file.absolutePath}")

                        val output = BufferedOutputStream(FileOutputStream(file))
                        val data = ByteArray(1024)

                        var total: Long = 0
                        var count: Int
                        count = input.read(data)
                        while (count != -1) {
                            total += count.toLong()
                            output.write(data, 0, count)
                            count = input.read(data)
                        }

                        output.flush()
                        output.close()
                        input.close()

                        Log.d("DownloadClient", "File Downloaded. Name: $filename, Size: ${total.toDouble() / 1024} KB")
                        Log.d("DownloadClient", "File exists after download: ${file.exists()}")
                        Log.d("DownloadClient", "File path: ${file.absolutePath}")
                        Log.d("DownloadClient", "File size on disk: ${file.length()} bytes")
                        Log.d("DownloadClient", "File readable: ${file.canRead()}")

                        // Verify the file was written successfully
                        if (!file.exists() || file.length() == 0L) {
                            Log.e("DownloadClient", "File was not created successfully or is empty")
                            fr.filename = null
                            r.responseCode = 500
                            r.message = "File download failed"
                            r.customMessage = "Downloaded file is empty or was not created"
                        }

                    } catch (e: IOException) {
                        Log.e("DownloadClient", "Error downloading file: $filename", e)
                        e.printStackTrace()
                        fr.filename = null
                        r.responseCode = 500
                        r.message = "Download failed"
                        r.customMessage = "IO Error: ${e.message}"
                    }

                    r.responseCode = 200
                    r.message = "Success"
                    Log.d("DownloadClient", "Download completed successfully. Calling onResponse with FileResponse")
                    Log.d("DownloadClient", "FileResponse filename: ${fr.filename}")
                    Log.d("DownloadClient", "FileResponse path: ${fr.path}")
                    Log.d("DownloadClient", "Response success: ${r.isSuccess}")
                    Log.d("DownloadClient", "Response code: ${r.responseCode}")
                    Log.d("DownloadClient", "Response message: ${r.message}")
                } else {
                    r = VolleyClient.parseResponse(String(response), responseType, 200)
                    if (r != null) {
                        r.requestType = requestType
                    }
                }
                r!!.extraOutput = extraOutput
                Log.d("DownloadClient", "About to call onResponse")
                Log.d("DownloadClient", "Listener class: ${l?.javaClass?.simpleName}")
                Log.d("DownloadClient", "Response type: ${r.javaClass.simpleName}")
                Log.d("DownloadClient", "Request type: ${r.requestType}")

                try {
                    l!!.onResponse(r)
                    Log.d("DownloadClient", "Successfully called onResponse")
                } catch (e: Exception) {
                    Log.e("DownloadClient", "Error calling onResponse", e)
                    e.printStackTrace()
                }
            } else {
                Log.w("RestClient", "Response received but not listened.")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    /**
     * Executes the url request that is preset in constructor
     *
     * @param l            Response Listener which is implemented in the activity
     * @param responseType Response type from one of model default is [Response]
     */
    @Throws(Exception::class)
    fun execute(l: ResponseListener, responseType: Type) {
        this.l = l
        this.responseType = responseType

        try {
            if (Utility.isInternetAvailable(c)) {
                val hurlStack = object : HurlStack() {
                    @Throws(IOException::class)
                    override fun createConnection(url: URL): HttpURLConnection {
                        val connection = super.createConnection(url)
                        Log.i("VolleyClient", "Connection created")
                        return connection
                    }
                }

                val queue = Volley.newRequestQueue(c, hurlStack)

                val request = object : Request<ByteArray>(method, url, this) {
                    @Throws(AuthFailureError::class)
                    override fun getHeaders(): Map<String, String> {
                        addHeader("Content-Type", "application/x-www-form-urlencoded")
                        addHeader("Accept-Encoding", "")
                        return mHeaders
                    }

                    @Throws(AuthFailureError::class)
                    override fun getParams(): Map<String, String> {
                        return mParams
                    }

                    override fun parseNetworkResponse(response: NetworkResponse?): com.android.volley.Response<ByteArray> {
                        response?.headers?.let { headers ->
                            addHeaders(headers)
                        }
                        if (response == null) throw AssertionError()
                        return com.android.volley.Response.success(response.data, HttpHeaderParser.parseCacheHeaders(response))
                    }

                    override fun deliverResponse(response: ByteArray) {
                        onResponse(response)
                    }
                }

                // Request Time out
                request.retryPolicy = DefaultRetryPolicy(timeout, DefaultRetryPolicy.DEFAULT_MAX_RETRIES, DefaultRetryPolicy.DEFAULT_BACKOFF_MULT)

                System.setProperty("http.keepAlive", "false")

                // Default request queue
                queue.add(request)
            } else {
                throw ConnectException("No network access detected")
            }
        } catch (e: Exception) {
            Log.e("DownloadClient", "Exception-------------------\n")
            throw e
        }

    }
}
