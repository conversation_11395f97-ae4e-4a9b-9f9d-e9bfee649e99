<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <application
        android:allowBackup="false"
        android:debuggable="false"
        android:fullBackupContent="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning,HardcodedDebugMode,UnusedAttribute"
        tools:replace="android:allowBackup,android:usesCleartextTraffic">

        <!-- Provider -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <!-- Activities -->
        <activity android:name=".SplashScreen"
            tools:node="merge"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".AppPreviewScreen"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".XSDashboard"
            android:launchMode="singleTask"
            android:screenOrientation="userPortrait"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity android:name=".LoginScreen"
            tools:node="merge"
            android:exported="false"
            tools:ignore="AppLinkUrlError">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="dev.permisso.in" android:port="8773"/>
                <data android:scheme="http"/>
                <data android:pathPrefix="/erp"/>
                <data android:host="dev.permisso.in" android:port="8443"/>
                <data android:scheme="http"/>
                <data android:pathPrefix="/erp"/>
                <data android:host="************" android:port="8113"/>
                <data android:scheme="http"/>
                <data android:pathPrefix="/erp"/>
                <data android:scheme="https"/>
                <data android:host="schnell.xserp.in"/>
                <data android:pathPrefix="/erp"/>
            </intent-filter>
        </activity>

        <activity android:name=".AppThemeTemplate" android:exported="false" />

        <!-- Services -->
        <service
            android:name=".XSerpService"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name=".attention.FcmService"
            android:enabled="true"
            android:exported="false"
            android:stopWithTask="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <activity
            android:name=".attention.Notifications"
            android:label="@string/notifications"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <!-- More activities -->
        <activity
            android:name=".masters.PartyDetail"
            android:label="@string/party_detail"
            android:exported="false" />

        <activity
            android:name=".masters.MaterialDetail"
            android:label="@string/material_detail"
            android:exported="false" />

        <activity
            android:name=".account.LedgerDetails"
            android:label="@string/ledger_detail"
            android:screenOrientation="userPortrait"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".account.LedgerListActivity"
            android:label="@string/ledger_list"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".expenses.ExpenseActivity"
            android:label="@string/add_expenses"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".masters.RateApprovalActivity"
            android:label="@string/rate_approvel"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".purchase.POApprovalActivity"
            android:label="@string/po_approvel"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|adjustResize"
            android:exported="false" />

        <activity
            android:name=".sales.SalesApprovalActivity"
            android:label="@string/invoice_approval"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|adjustResize"
            android:exported="false" />

        <activity
            android:name=".stores.GrnApprovalActivity"
            android:label="@string/grn_approval"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|adjustResize"
            android:exported="false" />

        <activity
            android:name=".PaymentScreen"
            android:label="@string/subscription"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|adjustResize"
            android:exported="false" />

        <activity
            android:name=".SignUpScreen"
            android:label="@string/sign_up"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustResize"
            android:exported="false" />

        <activity
            android:name=".sales.InvoiceCreateActivity"
            android:label="@string/new_invoice"
            android:screenOrientation="userPortrait"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".sales.AddMaterialActivity"
            android:label="@string/add_material"
            android:screenOrientation="userPortrait"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".TransactionDetailScreen"
            android:theme="@style/AppTheme"
            android:exported="false" />

    </application>

</manifest>
